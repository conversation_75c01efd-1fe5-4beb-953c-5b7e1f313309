package com.get.aisplatformcenter.controller;

import com.get.aisplatformcenter.service.MMessageService;
import com.get.aisplatformcenterap.vo.MMessageVo;
import com.get.aisplatformcenterap.entity.MMessageEntity;
import com.get.aisplatformcenterap.dto.DeletePatchParamsDto;
import com.get.aisplatformcenterap.dto.MMessagePutAwayParamsDto;
import com.get.aisplatformcenterap.dto.MMessageParamsDto;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "后台管理-消息管理")
@RestController
@RequestMapping("platform/mmessage")
public class MMessageController {
    @Autowired
    MMessageService mMessageService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/消息管理/列表查询")
    @PostMapping("searchPage")
    public ResponseBo<MMessageVo> searchPage(@RequestBody SearchBean<MMessageParamsDto> page){
        List<MMessageVo> datas=mMessageService.searchPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "新增-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/新增")
    @PostMapping("insert")
    public ResponseBo insert(@RequestBody MMessageEntity entity){
        return SaveResponseBo.ok(mMessageService.saveOrUpdateMessage(entity));
    }
    @ApiOperation(value = "新增(修改)-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/新增(修改)")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(BaseVoEntity.Update.class) MMessageParamsDto entity){
        return SaveResponseBo.ok(mMessageService.saveOrUpdateMessage(entity));
    }


    @ApiOperation(value = "一键(上架)下架-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/一键(上架)下架-接口")
    @PostMapping("putAway")
    public ResponseBo putAway(@RequestBody  @Valid MMessagePutAwayParamsDto vo){
        return SaveResponseBo.ok(mMessageService.putAwayMessage(vo));
    }

    @ApiOperation(value = "删除-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(  @PathVariable("id") Long id){
        return SaveResponseBo.ok(mMessageService.deleteOne( id));
    }

    @ApiOperation(value = "详情-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/详情")
    @PostMapping("getDetail/{id}")
    public ResponseBo<MMessageEntity> getDetail(  @PathVariable("id") Long id){
        MMessageEntity messageDetail=mMessageService.getById(id);
        return new ResponseBo<>(messageDetail);
    }



    @ApiOperation(value = "一键删除-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/一键删除")
    @PostMapping("deleteBatch")
    public ResponseBo deleteBatch(@RequestBody  @Valid DeletePatchParamsDto entity){
        mMessageService.deleteBatch(entity);
        return SaveResponseBo.ok();
    }





}

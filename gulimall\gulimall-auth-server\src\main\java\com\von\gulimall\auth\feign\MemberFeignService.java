package com.von.gulimall.auth.feign;

import com.von.common.utils.R;
import com.von.common.vo.MemberLoginVo;
import com.von.common.vo.SocialUser;
import com.von.gulimall.auth.vo.UserRegisterVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Date 2024/11/28 23:36
 * @Version 1.0
 */
@FeignClient("gulimall-member")
public interface MemberFeignService {

    @PostMapping("/member/member/regist")
    R regist(@RequestBody UserRegisterVo userRegisterVo);

    @PostMapping("/member/member/login")
    R login(@RequestBody MemberLoginVo memberLoginVo);

    /**
     * 社交登录
     *
     * @param socialUser
     * @return
     * @throws Exception
     */
    @PostMapping("/member/member/oauth/login")
    R oauthLogin(@RequestBody SocialUser socialUser) throws Exception;

}

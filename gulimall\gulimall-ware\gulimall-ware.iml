<?xml version="1.0" encoding="UTF-8"?>
<module version="4">
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <sourceFolder url="file://$MODULE_DIR$/src/test/java" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.vintage:junit-vintage-engine:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-engine:1.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.opentest4j:opentest4j:1.2.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.platform:junit-platform-commons:1.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: junit:junit:4.13.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-core:2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.apiguardian:apiguardian-api:1.1.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-amqp:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.12" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.12" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.17.2" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: jakarta.annotation:jakarta.annotation-api:1.3.5" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.30" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-messaging:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.amqp:spring-rabbit:2.4.17" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.amqp:spring-amqp:2.4.17" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.retry:spring-retry:1.3.4" level="project" />
    <orderEntry type="library" name="Maven: com.rabbitmq:amqp-client:5.14.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.3.31" level="project" />
    <orderEntry type="module" module-name="gulimall-common" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-actuator:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator-autoconfigure:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-core:1.9.17" level="project" />
    <orderEntry type="library" name="Maven: org.hdrhistogram:HdrHistogram:2.1.12" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.latencyutils:LatencyUtils:2.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.session:spring-session-data-redis:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.session:spring-session-core:2.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-redis:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-keyvalue:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-cache:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-redis:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: redis.clients:jedis:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.36" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-pool2:2.11.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-thymeleaf:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf:thymeleaf-spring5:3.0.15.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf:thymeleaf:3.0.15.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.attoparser:attoparser:2.0.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.unbescape:unbescape:1.1.6.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf.extras:thymeleaf-extras-java8time:3.0.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:nacos-client:2.4.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:nacos-auth-plugin:2.4.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:nacos-encryption-plugin:2.4.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:nacos-logback-adapter-12:2.4.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:logback-adapter:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.nacos:nacos-log4j2-adapter:2.4.3" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.15" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpasyncclient:4.1.5" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore-nio:4.4.16" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.14" level="project" />
    <orderEntry type="library" name="Maven: io.prometheus:simpleclient:0.15.0" level="project" />
    <orderEntry type="library" name="Maven: io.prometheus:simpleclient_tracer_otel:0.15.0" level="project" />
    <orderEntry type="library" name="Maven: io.prometheus:simpleclient_tracer_common:0.15.0" level="project" />
    <orderEntry type="library" name="Maven: io.prometheus:simpleclient_tracer_otel_agent:0.15.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:30.1.1-jre" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:failureaccess:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:listenablefuture:9999.0-empty-to-avoid-conflict-with-guava" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.findbugs:jsr305:3.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.checkerframework:checker-qual:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.errorprone:error_prone_annotations:2.5.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.j2objc:j2objc-annotations:1.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.79" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-validation:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.83" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.2.5.Final" level="project" />
    <orderEntry type="library" name="Maven: jakarta.validation:jakarta.validation-api:2.0.2" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.4.3.Final" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.5.1" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-all:5.7.16" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:2021.0.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.cloud:spring-cloud-alibaba-commons:2021.0.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.spring:spring-context-support:1.0.11" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-context:3.1.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:2021.0.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-boot-starter:3.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus:3.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-extension:3.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-core:3.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-annotation:3.3.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:3.1" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.5.4" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:2.0.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:4.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.30" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.16" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-loadbalancer:3.1.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-loadbalancer:3.1.6" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.4.34" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.4" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor.addons:reactor-extra:3.4.10" level="project" />
    <orderEntry type="library" name="Maven: com.stoyanr:evictor:1.0.0" level="project" />
    <orderEntry type="library" name="Maven: commons-lang:commons-lang:2.6" level="project" />
    <orderEntry type="library" name="Maven: javax.validation:validation-api:2.0.1.Final" level="project" />
    <orderEntry type="library" name="Maven: mysql:mysql-connector-java:8.0.17" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java:3.6.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.xiaoymin:knife4j-spring-boot-starter:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: com.github.xiaoymin:knife4j-spring-boot-autoconfigure:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: com.github.xiaoymin:knife4j-spring:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: com.github.xiaoymin:knife4j-annotations:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-annotations:1.5.22" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-annotations:2.1.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.xiaoymin:knife4j-core:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: org.javassist:javassist:3.25.0-GA" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger2:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spi:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-schema:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-common:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-web:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.github.classgraph:classgraph:4.8.83" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-webflux:3.0.0" level="project" />
    <orderEntry type="library" scope="RUNTIME" name="Maven: org.mapstruct:mapstruct:1.3.1.Final" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-webmvc:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-core:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-oas:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger.core.v3:swagger-models:2.1.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-bean-validators:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-models:1.5.22" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-core:1.5.22" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.12.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-boot-starter:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-data-rest:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-core:2.0.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-metadata:2.0.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.github.xiaoymin:knife4j-spring-ui:3.0.3" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.cloud:spring-cloud-starter-alibaba-sentinel:2021.0.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-transport-simple-http:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-transport-common:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-datasource-extension:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-annotation-aspectj:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-core:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.cloud:spring-cloud-circuitbreaker-sentinel:2021.0.5.0" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-reactor-adapter:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-spring-webflux-adapter:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-spring-webmvc-adapter:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-parameter-flow-control:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.googlecode.concurrentlinkedhashmap:concurrentlinkedhashmap-lru:1.4.2" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-cluster-server-default:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-cluster-common-default:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-handler:4.1.101.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-common:4.1.101.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-resolver:4.1.101.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-buffer:4.1.101.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport:4.1.101.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport-native-unix-common:4.1.101.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec:4.1.101.Final" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.csp:sentinel-cluster-client-default:1.8.6" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba.cloud:spring-cloud-alibaba-sentinel-datasource:2021.0.5.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.13.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.83" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.83" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter-openfeign:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-starter:3.1.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-rsa:1.0.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcpkix-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcutil-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-openfeign-core:3.1.7" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign.form:feign-form-spring:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign.form:feign-form:3.8.0" level="project" />
    <orderEntry type="library" name="Maven: commons-fileupload:commons-fileupload:1.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.cloud:spring-cloud-commons:3.1.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-crypto:5.7.11" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-core:11.10" level="project" />
    <orderEntry type="library" name="Maven: io.github.openfeign:feign-slf4j:11.10" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.7.18" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:2.7.18" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.7.18" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.7.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.4.11" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:2.4.11" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.ow2.asm:asm:9.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: jakarta.xml.bind:jakarta.xml.bind-api:2.3.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: jakarta.activation:jakarta.activation-api:1.2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.22.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest:2.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-api:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-params:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.junit.jupiter:junit-jupiter-engine:5.8.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:4.5.1" level="project" />
    <orderEntry type="library" name="Maven: net.bytebuddy:byte-buddy:1.12.23" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.12.23" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.objenesis:objenesis:3.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-junit-jupiter:4.5.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.3.31" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.3.31" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.9.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.7.18" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.3.31" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.7" level="project" />
  </component>
</module>
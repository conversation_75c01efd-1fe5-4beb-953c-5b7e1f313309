<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.remindercenter.dao.RemindTaskMapper">


  <update id="updateByPrimaryKeySelective" parameterType="com.get.remindercenter.entity.RemindTask">
    update m_remind_task
    <set>
      <if test="taskGuid != null">
        task_guid = #{taskGuid,jdbcType=VARCHAR},
      </if>
      <if test="fkRemindEventTypeKey != null">
        fk_remind_event_type_key = #{fkRemindEventTypeKey,jdbcType=VARCHAR},
      </if>
      <if test="fkStaffId != null">
        fk_staff_id = #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="advanceDays != null">
        advance_days = #{advanceDays,jdbcType=VARCHAR},
      </if>
      <if test="advanceHours != null">
        advance_hours = #{advanceHours,jdbcType=VARCHAR},
      </if>
      <if test="loopIntervalDays != null">
        loop_interval_days = #{loopIntervalDays,jdbcType=INTEGER},
      </if>
      <if test="loopWeekDays != null">
        loop_week_days = #{loopWeekDays,jdbcType=VARCHAR},
      </if>
      <if test="loopMonthDays != null">
        loop_month_days = #{loopMonthDays,jdbcType=VARCHAR},
      </if>
      <if test="taskTitle != null">
        task_title = #{taskTitle,jdbcType=VARCHAR},
      </if>
      <if test="taskRemark != null">
        task_remark = #{taskRemark,jdbcType=VARCHAR},
      </if>
      <if test="taskBgColor != null">
        task_bg_color = #{taskBgColor,jdbcType=VARCHAR},
      </if>
      <if test="taskTxtColor != null">
        task_txt_color = #{taskTxtColor,jdbcType=VARCHAR},
      </if>
      <if test="taskLink != null">
        task_link = #{taskLink,jdbcType=VARCHAR},
      </if>
      <if test="remindMethod != null">
        remind_method = #{remindMethod,jdbcType=VARCHAR},
      </if>
      <if test="fkDbName != null">
        fk_db_name = #{fkDbName,jdbcType=VARCHAR},
      </if>
      <if test="fkTableName != null">
        fk_table_name = #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkTableId != null">
        fk_table_id = #{fkTableId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.remindercenter.entity.RemindTask">
    update m_remind_task
    set task_guid = #{taskGuid,jdbcType=VARCHAR},
      fk_remind_event_type_key = #{fkRemindEventTypeKey,jdbcType=VARCHAR},
      fk_staff_id = #{fkStaffId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      advance_days = #{advanceDays,jdbcType=VARCHAR},
      advance_hours = #{advanceHours,jdbcType=VARCHAR},
      loop_interval_days = #{loopIntervalDays,jdbcType=INTEGER},
      loop_week_days = #{loopWeekDays,jdbcType=VARCHAR},
      loop_month_days = #{loopMonthDays,jdbcType=VARCHAR},
      task_title = #{taskTitle,jdbcType=VARCHAR},
      task_remark = #{taskRemark,jdbcType=VARCHAR},
      task_bg_color = #{taskBgColor,jdbcType=VARCHAR},
      task_txt_color = #{taskTxtColor,jdbcType=VARCHAR},
      task_link = #{taskLink,jdbcType=VARCHAR},
      remind_method = #{remindMethod,jdbcType=VARCHAR},
      fk_db_name = #{fkDbName,jdbcType=VARCHAR},
      fk_table_name = #{fkTableName,jdbcType=VARCHAR},
      fk_table_id = #{fkTableId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getRemindTaskList" resultType="com.get.remindercenter.entity.RemindTask">
    SELECT
    id,
    task_guid,
    fk_remind_event_type_key,
    fk_staff_id,
    start_time,
    end_time,
    advance_days,
    advance_hours,
    loop_interval_days,
    loop_week_days,
    loop_month_days,
    task_title,
    task_remark,
    task_bg_color,
    task_txt_color,
    task_link,
    remind_method,
    fk_db_name,
    `status`,
    fk_table_name,
    fk_table_id,
    gmt_create,
    gmt_create_user,
    gmt_modified,
    gmt_modified_user
    FROM
    m_remind_task
    WHERE
    fk_staff_id = #{fkStaffId} and
    ((DATE_FORMAT(start_time,'%Y-%m') <![CDATA[<= ]]> DATE_FORMAT(DATE_SUB(#{remindTaskListDto.selectTime}, interval 1 MONTH),'%Y-%m')
    and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),DATE_FORMAT(start_time,'%Y-%m')) <![CDATA[>= ]]> DATE_FORMAT(DATE_SUB(#{remindTaskListDto.selectTime}, interval 1 MONTH),'%Y-%m'))
    or
    DATE_FORMAT(start_time,'%Y-%m') <![CDATA[<= ]]> DATE_FORMAT(#{remindTaskListDto.selectTime},'%Y-%m')
    and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),DATE_FORMAT(start_time,'%Y-%m')) <![CDATA[>= ]]> DATE_FORMAT(#{remindTaskListDto.selectTime},'%Y-%m')
    or
    DATE_FORMAT(start_time,'%Y-%m') <![CDATA[<= ]]> DATE_FORMAT(DATE_ADD(#{remindTaskListDto.selectTime}, interval 1 MONTH),'%Y-%m')
    and IFNULL(DATE_FORMAT(end_time,'%Y-%m'),DATE_FORMAT(start_time,'%Y-%m')) <![CDATA[>= ]]> DATE_FORMAT(DATE_ADD(#{remindTaskListDto.selectTime}, interval 1 MONTH),'%Y-%m'))
    order by gmt_create,fk_remind_event_type_key desc
  </select>
  <select id="getTaskCount" resultType="com.get.remindercenter.entity.RemindTask">
    SELECT
    id,
    task_guid,
    fk_remind_event_type_key,
    fk_staff_id,
    start_time,
    end_time,
    advance_days,
    advance_hours,
    loop_interval_days,
    loop_week_days,
    loop_month_days,
    task_title,
    task_remark,
    task_bg_color,
    task_txt_color,
    task_link,
    remind_method,
    fk_db_name,
    `status`,
    fk_table_name,
    fk_table_id,
    gmt_create,
    gmt_create_user,
    gmt_modified,
    gmt_modified_user
    FROM
    m_remind_task
    WHERE
    fk_staff_id = #{fkStaffId}
    and status = 1
    <if test="isDate">
      and DATE_FORMAT(start_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{nowDate},'%Y-%m-%d')
      and DATE_FORMAT(IF(IFNULL(end_time,'') = '',start_time,DATE_SUB(end_time, interval 1 second)),'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{nowDate},'%Y-%m-%d')
    </if>
    <if test="keys != null and keys.size > 0">
      and
      <foreach collection="keys" item="key" separator="or" open="(" close=")">
        FIND_IN_SET(#{key},fk_db_name)
      </foreach>
    </if>
  </select>
    <select id="getStaffContractRemindDto" resultType="com.get.remindercenter.vo.StaffContractRemindVo">
      SELECT
        CONCAT(b.name,IF(b.name_en is null or b.name_en = '','',CONCAT("，",b.name_en))) as staffFullName,
        a.signing_company as companyName,
        c.`name` as departmentName,
        d.`name` as positionName,
        a.end_time as contractEndDate
      FROM
        ais_permission_center.`m_staff_contract` a
          LEFT JOIN ais_permission_center.m_staff b on a.fk_staff_id = b.id
          LEFT JOIN ais_permission_center.m_department c on c.id = b.fk_department_id
          LEFT JOIN ais_permission_center.m_position d on d.id = b.fk_position_id
      where a.id = #{fkTableId}
    </select>

  <select id="getRemindTaskDatas" resultType="com.get.remindercenter.vo.RemindTaskListVo">
    select *
    from m_remind_task
    <where>
      <if test="remindTaskListDto.fkTableName != null and remindTaskListDto.fkTableName !='' ">
        and fk_table_name = #{remindTaskListDto.fkTableName}
      </if>
      <if test="remindTaskListDto.fkTableId != null">
        and fk_table_id = #{remindTaskListDto.fkTableId}
      </if>
      <if test="remindTaskListDto.fkRemindEventTypeKey != null">
        and fk_remind_event_type_key = #{remindTaskListDto.fkRemindEventTypeKey}
      </if>
    </where>
  </select>

</mapper>
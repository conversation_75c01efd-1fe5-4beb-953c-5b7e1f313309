<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.financecenter.dao.InvoiceTargetMapper">

    <select id="getTargetIdAndTypeKeyByInvoiceId" parameterType="Long" resultType="com.get.financecenter.entity.InvoiceTarget">
        SELECT
            fk_type_key,
            fk_type_target_id
        FROM
            r_invoice_type_key_target
        WHERE
            fk_invoice_id = #{invoiceId}
    </select>
    <select id="getTargetIdAndTypeKeyByInvoiceIds" resultType="com.get.financecenter.entity.InvoiceTarget">
        SELECT
            fk_invoice_id,
            fk_type_key,
            fk_type_target_id
        FROM
            r_invoice_type_key_target
        WHERE
            fk_invoice_id in
            <foreach collection="list" item="invoiceId" separator="," open="(" close=")">
                #{invoiceId}
            </foreach>
    </select>
    <select id="getInvoiceNum" resultType="java.lang.String">
        SELECT
            mi.num
        FROM
            m_invoice mi
        INNER JOIN r_invoice_type_key_target ri ON ri.fk_invoice_id = mi.id
        WHERE
            ri.fk_type_target_id in
            <foreach collection="invoiceBindDto.fkTypeTargetIds" open="(" close=")" separator="," item="targetId">
                #{targetId}
            </foreach>
        AND ri.fk_type_key = #{invoiceBindDto.fkTypeKey}
        AND mi.fk_company_id = #{invoiceBindDto.fkCompanyId}
        <if test="invoiceBindDto.id!=null and invoiceBindDto.id!=''">
            AND ri.fk_invoice_id != #{invoiceBindDto.id}
        </if>
        AND mi. STATUS = 1
    </select>
</mapper>

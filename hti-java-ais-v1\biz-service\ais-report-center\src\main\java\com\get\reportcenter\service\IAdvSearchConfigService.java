package com.get.reportcenter.service;

import com.get.common.result.Page;
import com.get.reportcenter.vo.AdvSearchConfigVo;
import com.get.reportcenter.vo.StudentVo;
import com.get.reportcenter.dto.AdvSearchRunDto;
import com.get.reportcenter.dto.StudentDto;

import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/3/4
 * @TIME: 18:19
 * @Description:
 **/
public interface IAdvSearchConfigService {
    /**
     * 获取高级搜索配置
     *
     * @param queryKey
     */
    AdvSearchConfigVo getAdvSearchConfigByQueryKey(String queryKey);

    /**
     * 获取高级搜索结果
     *
     * @param advSearchRunDtos,page
     */
    List<LinkedHashMap<String, String>> getDatasByAdvSearchRunVo(List<AdvSearchRunDto> advSearchRunDtos, Page page);

    /**
     * 获取学生高级搜索结果
     *
     * @param advSearchRunDtos,page
     */
    List<LinkedHashMap<String, String>> getStudentDatasByAdvSearchRunVo(List<AdvSearchRunDto> advSearchRunDtos, Page page);


    /**
     * 导出高级搜索Excel
     *
     * @param advSearchRunDtos
     */
    void exportEventExcel(HttpServletResponse response, List<AdvSearchRunDto> advSearchRunDtos);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentVo>
     * @Description: 学生列表搜索
     * @Param [studentDto, page]
     */
    List<StudentVo> getStudents(StudentDto studentDto, Page page);
}

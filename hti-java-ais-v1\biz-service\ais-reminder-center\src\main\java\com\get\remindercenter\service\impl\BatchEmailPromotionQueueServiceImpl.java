package com.get.remindercenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.dao.BatchEmailPromotionQueueMapper;
import com.get.remindercenter.dao.BatchSendingEmailMapper;
import com.get.remindercenter.dao.InvalidEmailMapper;
import com.get.remindercenter.dao.UnsubscribeEmailMapper;
import com.get.remindercenter.dto.AliyunSendMailDto;
import com.get.remindercenter.dto.BatchEmailPromotionQueueDto;
import com.get.remindercenter.entity.BatchEmailPromotionQueue;
import com.get.remindercenter.entity.BatchSendingEmail;
import com.get.remindercenter.entity.InvalidEmail;
import com.get.remindercenter.service.IBatchEmailPromotionQueueService;
import com.get.remindercenter.vo.AliyunSendMailVo;
import com.get.remindercenter.vo.BatchEmailPromotionQueueVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/20 14:01
 */
@Service
public class BatchEmailPromotionQueueServiceImpl extends BaseServiceImpl<BatchEmailPromotionQueueMapper, BatchEmailPromotionQueue> implements IBatchEmailPromotionQueueService {

    @Resource
    private BatchEmailPromotionQueueMapper batchEmailPromotionQueueMapper;
    @Resource
    private InvalidEmailMapper invalidEmailMapper;
    @Resource
    private BatchSendingEmailMapper batchSendingEmailMapper;
    @Resource
    private UnsubscribeEmailMapper unsubscribeEmailMapper;
    @Resource
    private UtilService utilService;

    /**
     *
     * @return 待发送的邮件队列
     */
    @Override
    public List<BatchEmailPromotionQueueVo> getSendNewsEamilQueues() {
        List<BatchEmailPromotionQueue> batchEmailPromotionQueues = batchEmailPromotionQueueMapper.selectList(Wrappers.<BatchEmailPromotionQueue>lambdaQuery().isNotNull(BatchEmailPromotionQueue::getEmail));
        return BeanCopyUtils.copyListProperties(batchEmailPromotionQueues, BatchEmailPromotionQueueVo::new);
    }

    @Override
    public Boolean addTask2NewsEmailQueen(AliyunSendMailDto aliyunSendMailDto) {
        List<String> toEmailList = new ArrayList<>(Arrays.asList(aliyunSendMailDto.getToEmails()));
        List<InvalidEmail> invalidEmails = invalidEmailMapper.selectList(Wrappers.lambdaQuery(InvalidEmail.class));
        if (GeneralTool.isNotEmpty(invalidEmails)) {
            toEmailList.removeAll(invalidEmails.stream().map(InvalidEmail::getEmail).collect(Collectors.toList()));
        }
        //已发送
        List<BatchSendingEmail> batchSendingEmails = batchSendingEmailMapper.selectList(Wrappers.<BatchSendingEmail>lambdaQuery().eq(BatchSendingEmail::getFkTableName, TableEnum.NEWS.key).eq(BatchSendingEmail::getFkTableId, aliyunSendMailDto.getNewsId()));
        if (GeneralTool.isNotEmpty(batchSendingEmails)) {
            toEmailList.removeAll(batchSendingEmails.stream().map(BatchSendingEmail::getEmail).collect(Collectors.toList()));
        }
        //退订
        List<String> unsubscribeEmailList = unsubscribeEmailMapper.getUnsubscribeEmail(aliyunSendMailDto.getType());
        if (GeneralTool.isNotEmpty(unsubscribeEmailList)) {
            toEmailList.removeAll(unsubscribeEmailList);
        }
        // 已在待发送新闻邮件队列
        List<BatchEmailPromotionQueue> batchEmailPromotionQueues = batchEmailPromotionQueueMapper.selectList(Wrappers.lambdaQuery(BatchEmailPromotionQueue.class).in(BatchEmailPromotionQueue::getEmail, toEmailList).eq(BatchEmailPromotionQueue::getFkNewsId, aliyunSendMailDto.getNewsId()));
       if (GeneralTool.isNotEmpty(batchEmailPromotionQueues)){
           toEmailList.removeAll(batchEmailPromotionQueues.stream().map(BatchEmailPromotionQueue::getEmail).collect(Collectors.toList()));
       }
        //  插入队列表
        List<BatchEmailPromotionQueue> emailQueues = toEmailList.stream().distinct().map(email -> {
            BatchEmailPromotionQueue msg = new BatchEmailPromotionQueue(email, aliyunSendMailDto.getNewsId(), aliyunSendMailDto.getType());
            utilService.setCreateInfo(msg);
            return msg;
        }).collect(Collectors.toList());
        saveBatch(emailQueues);
        return true;
    }

    @Override
    public Boolean deleteMsgFromQueue(Long id) {
        if (GeneralTool.isEmpty(id))
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        batchEmailPromotionQueueMapper.deleteById(id);
        return true;
    }
}
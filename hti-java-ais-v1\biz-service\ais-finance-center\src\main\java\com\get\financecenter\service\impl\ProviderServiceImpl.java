package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ProviderMapper;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.ProviderVo;
import com.get.financecenter.entity.Provider;
import com.get.financecenter.service.IDeleteService;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.service.IProviderService;
import com.get.financecenter.service.IProviderTypeService;
import com.get.financecenter.utils.MyStringUtils;
import com.get.financecenter.dto.ProviderDto;
import com.get.financecenter.dto.query.ProviderQueryDto;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/18 10:11
 * @verison: 1.0
 * @description:
 */
@Service
public class ProviderServiceImpl extends BaseServiceImpl<ProviderMapper, Provider> implements IProviderService {
    @Resource
    private ProviderMapper providerMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IProviderTypeService providerTypeService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IMediaAndAttachedService attachedService;

    @Override
    public ProviderVo findProviderById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Provider provider = providerMapper.selectById(id);
        if (GeneralTool.isEmpty(provider)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ProviderVo providerVo = BeanCopyUtils.objClone(provider, ProviderVo::new);
        //111111
        Result<String> resultCountryName = institutionCenterClient.getCountryNameById(provider.getFkAreaCountryId());
        Result<String> resultStateName = institutionCenterClient.getStateNameById(provider.getFkAreaStateId());
        Result<String> resultCityName = institutionCenterClient.getCityNameById(provider.getFkAreaCityId());
        if (resultCountryName.isSuccess()) {
            providerVo.setCountryName(resultCountryName.getData());
        }
        if (resultCityName.isSuccess()) {
            providerVo.setCityName(resultCityName.getData());
        }
        if (resultStateName.isSuccess()) {
            providerVo.setStateName(resultStateName.getData());
        }
//        String companyName = permissionCenterClient.getCompanyNameById(provider.getFkCompanyId());
        Result<String> result = permissionCenterClient.getCompanyNameById(provider.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            providerVo.setCompanyName(result.getData());
        }
        String providerTypeName = providerTypeService.getProviderTypeNameById(provider.getFkProviderTypeId());
        //111111

        providerVo.setProviderTypeName(providerTypeName);
        return providerVo;
    }

    @Override
    public Long addProvider(ProviderDto providerDto) {
        if (GeneralTool.isEmpty(providerDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Provider provider = BeanCopyUtils.objClone(providerDto, Provider::new);
        utilService.updateUserInfoToEntity(provider);
        providerMapper.insert(provider);
        //获取设置自动生成得供应商编号
        provider.setNum(MyStringUtils.getProviderNum(provider.getId()));
        providerMapper.updateById(provider);
        return provider.getId();
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (providerMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }

        //验证能否删除
        deleteService.deleteValidateProvider(id);
        providerMapper.deleteById(id);
    }

    @Override
    public ProviderVo updateProvider(ProviderDto providerDto) {
        if (providerDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        Provider result = providerMapper.selectById(providerDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Provider provider = BeanCopyUtils.objClone(providerDto, Provider::new);
        utilService.updateUserInfoToEntity(provider);
        providerMapper.updateById(provider);
        return findProviderById(providerDto.getId());
    }

    @Override
    public List<ProviderVo> getProviders(ProviderQueryDto providerQueryDto, Page page) {
        //查询条件-公司ids
        if (GeneralTool.isNotEmpty(providerQueryDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(providerQueryDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        //封装列表操作
        List<Provider> providers = getProviderList(providerQueryDto, page);
        List<ProviderVo> convertDatas = new ArrayList<>();
        //城市id集合
        Set<Long> areaCityIds = new HashSet<>();
        //州省id集合
        Set<Long> areaStateIds = new HashSet<>();
        //国家id集合
        Set<Long> areaCountryIds = new HashSet<>();
        //所属公司id集合
        Set<Long> companyIds = new HashSet<>();
        //所属公司id集合
        Set<Long> providerTypeIds = new HashSet<>();
        //获取各自集合的值
        for (Provider provider : providers) {
            areaCountryIds.add(provider.getFkAreaCountryId());
            areaStateIds.add(provider.getFkAreaStateId());
            areaCityIds.add(provider.getFkAreaCityId());
            companyIds.add(provider.getFkCompanyId());
            providerTypeIds.add(provider.getFkProviderTypeId());
        }
        //feign调用获取对应map
        Map<Long, String> countryNameMap = getCountryNameMap(areaCountryIds);
        Map<Long, String> stateNameMap = getStateNameMap(areaStateIds);
        Map<Long, String> cityNameMap = getCityNameMap(areaCityIds);
        Map<Long, String> companyNameMap = getCompanyNameMap(companyIds);
        Map<Long, String> providerTypeNameMap = getProviderTypeNameMap(providerTypeIds);

        for (Provider provider : providers) {
            ProviderVo providerVo = BeanCopyUtils.objClone(provider, ProviderVo::new);
            //feign调用返回的map 根据key-id获取对应value-名称 设置返回给前端
            providerVo.setCountryName(countryNameMap.get(providerVo.getFkAreaCountryId()));
            providerVo.setStateName(stateNameMap.get(providerVo.getFkAreaStateId()));
            providerVo.setCityName(cityNameMap.get(providerVo.getFkAreaCityId()));
            providerVo.setCompanyName(companyNameMap.get(providerVo.getFkCompanyId()));
            //通过id查询对应供应商类型名称并设置
            providerVo.setProviderTypeName(providerTypeNameMap.get(providerVo.getFkProviderTypeId()));
            convertDatas.add(providerVo);
        }
        return convertDatas;
    }

    private List<Provider> getProviderList(ProviderQueryDto providerVo, Page page) {
//        Example example = new Example(Provider::new);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//
//        //不选所属公司时
//        if (GeneralTool.isEmpty(providerVo) || GeneralTool.isEmpty(providerVo.getFkCompanyId())) {
//            List<Long> companyIds = getCompanyIds();
//            criteria.andIn("fkCompanyId", companyIds);
//        }
//        if (GeneralTool.isNotEmpty(providerVo)) {
//            //查询条件-所属公司
//            if (GeneralTool.isNotEmpty(providerVo.getFkCompanyId())) {
//                PermissionUtils.validateCompany(providerVo.getFkCompanyId());
//                criteria.andEqualTo("fkCompanyId", providerVo.getFkCompanyId());
//            }
//            //查询条件-供应商类型
//            if (GeneralTool.isNotEmpty(providerVo.getFkProviderTypeId())) {
//                criteria.andEqualTo("fkProviderTypeId", providerVo.getFkProviderTypeId());
//            }
//            //查询条件-供应商名称/编号
//            if (GeneralTool.isNotEmpty(providerVo.getKeyWord())) {
//                criteria1.andLike("name", "%" + providerVo.getKeyWord() + "%");
//                criteria1.orLike("num", "%" + providerVo.getKeyWord() + "%");
//            }
//            //查询条件-供应商类型
//            if (GeneralTool.isNotEmpty(providerVo.getFkProviderTypeId())) {
//                criteria.andEqualTo("fkProviderTypeId", providerVo.getFkProviderTypeId());
//            }
//            //查询条件-创建时间(开始)
//            if (GeneralTool.isNotEmpty(providerVo.getBeginTime())) {
//                criteria.andGreaterThanOrEqualTo("gmtCreate", providerVo.getBeginTime());
//            }
//            //查询条件-创建时间(结束)
//            if (GeneralTool.isNotEmpty(providerVo.getEndTime())) {
//                criteria.andLessThanOrEqualTo("gmtCreate", providerVo.getEndTime());
//            }
//            //查询条件-国家
//            if (GeneralTool.isNotEmpty(providerVo.getFkAreaCountryId())) {
//                criteria.andEqualTo("fkAreaCountryId", providerVo.getFkAreaCountryId());
//            }
//            //查询条件-州省
//            if (GeneralTool.isNotEmpty(providerVo.getFkAreaStateId())) {
//                criteria.andEqualTo("fkAreaStateId", providerVo.getFkAreaStateId());
//            }
//            //查询条件-城市
//            if (GeneralTool.isNotEmpty(providerVo.getFkAreaCityId())) {
//                criteria.andEqualTo("fkAreaCityId", providerVo.getFkAreaCityId());
//            }
//        }
//        example.and(criteria1);
//        example.orderBy("gmtCreate").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<Provider> providers = providerMapper.selectByExample(example);
//        page.restPage(providers);

        LambdaQueryWrapper<Provider> wrapper = new LambdaQueryWrapper();
        //不选所属公司时
        if (GeneralTool.isEmpty(providerVo) || GeneralTool.isEmpty(providerVo.getFkCompanyId())) {
//            List<Long> companyIds = getCompanyIds();
            wrapper.in(Provider::getFkCompanyId, SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId()));
        }
        if (GeneralTool.isNotEmpty(providerVo)) {
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(providerVo.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(providerVo.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.eq(Provider::getFkCompanyId, providerVo.getFkCompanyId());
            }
            //查询条件-所属公司 多选
            if (GeneralTool.isNotEmpty(providerVo.getFkCompanyIds())) {
                wrapper.in(Provider::getFkCompanyId, providerVo.getFkCompanyIds());
            }
            //查询条件-供应商类型
            if (GeneralTool.isNotEmpty(providerVo.getFkProviderTypeId())) {
                wrapper.eq(Provider::getFkProviderTypeId, providerVo.getFkProviderTypeId());
            }
            //查询条件-供应商名称/编号
            if (GeneralTool.isNotEmpty(providerVo.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(Provider::getName, providerVo.getKeyWord()).or()
                                .like(Provider::getNum, providerVo.getKeyWord()));
            }
            //查询条件-供应商类型
            if (GeneralTool.isNotEmpty(providerVo.getFkProviderTypeId())) {
                wrapper.eq(Provider::getFkProviderTypeId, providerVo.getFkProviderTypeId());
            }
            //查询条件-创建时间(开始)
            if (GeneralTool.isNotEmpty(providerVo.getBeginTime())) {
                wrapper.ge(Provider::getGmtCreate, providerVo.getBeginTime());
            }
            //查询条件-创建时间(结束)
            if (GeneralTool.isNotEmpty(providerVo.getEndTime())) {
                wrapper.le(Provider::getGmtCreate, providerVo.getEndTime());
            }
            //查询条件-国家
            if (GeneralTool.isNotEmpty(providerVo.getFkAreaCountryId())) {
                wrapper.eq(Provider::getFkAreaCountryId, providerVo.getFkAreaCountryId());
            }
            //查询条件-州省
            if (GeneralTool.isNotEmpty(providerVo.getFkAreaStateId())) {
                wrapper.eq(Provider::getFkAreaStateId, providerVo.getFkAreaStateId());
            }
            //查询条件-城市
            if (GeneralTool.isNotEmpty(providerVo.getFkAreaCityId())) {
                wrapper.eq(Provider::getFkAreaCityId, providerVo.getFkAreaCityId());
            }
        }
        wrapper.orderByDesc(Provider::getGmtCreate);

        IPage<Provider> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<Provider> providers = pages.getRecords();
        page.setAll((int) pages.getTotal());

        return providers;
    }

    @Override
    public List<FMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.FINANCE_PROVIDER.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<FMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_PROVIDER.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }


    @Override
    public List<ProviderVo> getProviderList() {

        List<Provider> providers = this.list();
        List<ProviderVo> providerVos = BeanCopyUtils.copyListProperties(providers, ProviderVo::new);
        return providerVos;
    }

    @Override
    public List<BaseSelectEntity> getProviderSelect() {
        return providerMapper.getProviderSelect();
    }

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取登录人对应所有公司id集合
     * @Param []
     * <AUTHOR>
     */
    private List<Long> getCompanyIds() {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        return companyIds;
    }

    /**
     * @Description :feign调用一次查出全部对应城市名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getCityNameMap(Set<Long> areaCityIds) {
        areaCityIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应城市名称
        Result<Map<Long, String>> result = institutionCenterClient.getCityNamesByIds(areaCityIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @Description :feign调用一次查出全部对应州省名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getStateNameMap(Set<Long> areaStateIds) {
        areaStateIds.removeIf(Objects::isNull);
//        //feign调用一次查出全部对应州省名称
        Result<Map<Long, String>> result = institutionCenterClient.getStateNamesByIds(areaStateIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @Description :feign调用一次查出全部对应国家名称
     * @Param [events]
     * <AUTHOR>
     */
    private Map<Long, String> getCountryNameMap(Set<Long> areaCountryIds) {
        areaCountryIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应国家名称
        Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(areaCountryIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return Maps.newHashMap();
    }

    /**
     * @Description :feign调用一次 查出全部对应公司名称
     * @Param [companyIds]
     * <AUTHOR>
     */
    private Map<Long, String> getCompanyNameMap(Set<Long> companyIds) {
        companyIds.removeIf(Objects::isNull);
        //feign调用一次查出全部对应公司名称
//        return permissionCenterClient.getCompanyNamesByIds(companyIds);
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return null;
    }

    /**
     * 根据ids查找对应供应商类型名称
     *
     * @param providerTypeIds
     * @return
     */
    private Map<Long, String> getProviderTypeNameMap(Set<Long> providerTypeIds) {
        providerTypeIds.removeIf(Objects::isNull);
        //根据ids查找对应供应商类型名称
        return providerTypeService.getProviderTypeNameByIds(providerTypeIds);
    }
}

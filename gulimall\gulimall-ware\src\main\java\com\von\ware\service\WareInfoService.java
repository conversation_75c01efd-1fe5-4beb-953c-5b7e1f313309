package com.von.ware.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.von.common.utils.PageUtils;
import com.von.ware.entity.WareInfoEntity;
import com.von.ware.vo.FareVo;

import java.util.Map;

/**
 * 仓库信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 23:45:29
 */
public interface WareInfoService extends IService<WareInfoEntity> {

    PageUtils queryPage(Map<String, Object> params);

    FareVo getFare(Long addrId);

    void test();
}


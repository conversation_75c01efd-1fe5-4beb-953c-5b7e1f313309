package com.get.pmpcenter.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/3/26
 * @Version 1.0
 * @apiNote:
 */
@Slf4j
public class PercentageUtil {

    // 计算金额的百分比
    public static BigDecimal calculatePercentage(String percentage, BigDecimal amount) {
        if (Objects.isNull(amount)) {
            return null;
        }
        BigDecimal percentValue = new BigDecimal(percentage).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);
        return amount.multiply(percentValue).setScale(2, RoundingMode.HALF_UP);
    }

    // 判断修改后的金额是否不超过原金额的 80%,超出限制返回false
    public static boolean isWithinLimit(BigDecimal originalAmount, BigDecimal modifiedAmount, String rate) {
        if (Objects.isNull(originalAmount)) {
            originalAmount = BigDecimal.ZERO;
        }
        if (Objects.isNull(modifiedAmount)) {
            modifiedAmount = BigDecimal.ZERO;
        }
        BigDecimal percentValue = new BigDecimal(rate).divide(new BigDecimal("100"));
        BigDecimal limit = originalAmount.multiply(percentValue);
        return modifiedAmount.compareTo(limit) <= 0;
    }

}

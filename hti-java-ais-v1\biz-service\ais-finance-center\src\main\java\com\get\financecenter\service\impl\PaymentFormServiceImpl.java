package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.DataConverter;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.financecenter.dao.BankAccountMapper;
import com.get.financecenter.dao.PayablePlanSettlementAgentAccountMapper;
import com.get.financecenter.dao.PayablePlanSettlementInstallmentMapper;
import com.get.financecenter.dao.PaymentFormItemMapper;
import com.get.financecenter.dao.PaymentFormMapper;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.financecenter.vo.*;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.entity.FComment;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.entity.PaymentFormItem;
import com.get.financecenter.service.AsyncExportService;
import com.get.financecenter.service.ICommentService;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.service.IPaymentFeeTypeService;
import com.get.financecenter.service.IPaymentFormService;
import com.get.financecenter.dto.*;
import com.get.financecenter.dto.PaymentFormAgentUpdateDto;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.vo.AgentContractAccountVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.feign.ISaleCenterClient;
import com.google.common.collect.Maps;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/23
 * @TIME: 14:46
 * @Description:
 **/
@Service
public class PaymentFormServiceImpl extends BaseServiceImpl<PaymentFormMapper, PaymentForm> implements IPaymentFormService {
    @Resource
    private PaymentFormMapper paymentFormMapper;
    @Resource
    private AsyncExportService asyncExportService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IInstitutionCenterClient iInstitutionCenterClient;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private UtilService utilService;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;
    @Resource
    private ICommentService commentService;
    @Resource
    private IInstitutionCenterClient feignInstitutionService;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private IPaymentFeeTypeService paymentFeeTypeService;
    @Resource
    @Lazy
    private BankAccountMapper bankAccountMapper;
    @Resource
    private PaymentFormItemMapper paymentFormItemMapper;
    @Resource
    private IExchangeRateService exchangeRateService;
    @Resource
    private PayablePlanSettlementAgentAccountMapper payablePlanSettlementAgentAccountMapper;

    @Override
    public LambdaQueryWrapper<PaymentForm> getWrapper(PaymentFormDto paymentFormDto) {
        LambdaQueryWrapper<PaymentForm> wrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(paymentFormDto.getFkCompanyId())) {
            wrapper.eq(PaymentForm::getFkCompanyId, paymentFormDto.getFkCompanyId());
        }

        if (GeneralTool.isNotEmpty(paymentFormDto.getFkCompanyIds())) {
            //查询条件-公司 查看的公司权限公司权限
//            if (!SecureUtil.validateCompanys(paymentFormVo.getFkCompanyIds())) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
//            }
            wrapper.in(PaymentForm::getFkCompanyId, paymentFormDto.getFkCompanyIds());
        }

        if (GeneralTool.isNotEmpty(paymentFormDto.getFkTypeKey())) {
            wrapper.eq(PaymentForm::getFkTypeKey, paymentFormDto.getFkTypeKey());
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getPaymentDateStart())) {
            wrapper.ge(PaymentForm::getPaymentDate, paymentFormDto.getPaymentDateStart());
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getPaymentDateEnd())) {
            wrapper.le(PaymentForm::getPaymentDate, paymentFormDto.getPaymentDateEnd());
        }

        if (GeneralTool.isNotEmpty(paymentFormDto.getFkTypeKey()) && GeneralTool.isNotEmpty(paymentFormDto.getTargetName())) {
            if (TableEnum.SALE_AGENT.key.equals(paymentFormDto.getFkTypeKey())) {
                //111111
                Result<List<Long>> resulttargetId = saleCenterClient.getAgentIds(paymentFormDto.getTargetName());
                List<Long> targetId = new ArrayList<>();
                if (resulttargetId.isSuccess()) {
                    targetId = resulttargetId.getData();
                }
                if (GeneralTool.isEmpty(targetId)) {
                    targetId.add(0L);
                }
                wrapper.in(PaymentForm::getFkTypeTargetId, targetId);
            }else if (TableEnum.INSTITUTION_PROVIDER.key.equals(paymentFormDto.getFkTypeKey())){
                List<Long> providerIds = iInstitutionCenterClient.getInstitutionProviderIds(paymentFormDto.getTargetName());
                if (GeneralTool.isNotEmpty(providerIds)) {
                    wrapper.in(PaymentForm::getFkTypeTargetId, providerIds);
                }
            }else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(paymentFormDto.getFkTypeKey())){
                List<Long> businessProviderIds = saleCenterClient.getBusinessProviderId(paymentFormDto.getTargetName()).getData();
                if (GeneralTool.isNotEmpty(businessProviderIds)) {
                    wrapper.in(PaymentForm::getFkTypeTargetId, businessProviderIds);
                }
            }
        } else if (GeneralTool.isEmpty(paymentFormDto.getFkTypeKey()) && GeneralTool.isNotEmpty(paymentFormDto.getTargetName())) {
            Result<List<Long>> resulttargetId = saleCenterClient.getAgentIds(paymentFormDto.getTargetName());
            List<Long> targetId = new ArrayList<>();
            if (resulttargetId.isSuccess()) {
                targetId = resulttargetId.getData();
            }
            if (GeneralTool.isEmpty(targetId)) {
                targetId.add(0L);
            }
            List<Long> institutionProviderIds = iInstitutionCenterClient.getInstitutionProviderIds(paymentFormDto.getTargetName());
            if (GeneralTool.isNotEmpty(institutionProviderIds)) {
                targetId.addAll(institutionProviderIds);
            }
            List<Long> businessProviderIds = saleCenterClient.getBusinessProviderId(paymentFormDto.getTargetName()).getData();
            if (GeneralTool.isNotEmpty(businessProviderIds)) {
                targetId.addAll(businessProviderIds);
            }
            wrapper.in(PaymentForm::getFkTypeTargetId, targetId);
        }
//        if (GeneralTool.isNotEmpty(paymentFormDto.getKeyWord())) {
//            wrapper.and(wrapper_ ->
//                    wrapper_.like(PaymentForm::getNumSystem, paymentFormDto.getKeyWord()).or()
//                            .like(PaymentForm::getNumBank, paymentFormDto.getKeyWord()));
//        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getNumSystem())) {
            wrapper.and(wrapper_ ->
                    wrapper_.like(PaymentForm::getNumSystem, paymentFormDto.getNumSystem())
            );
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getNumBank())){
            wrapper.and(wrapper_ ->
                    wrapper_.like(PaymentForm::getNumBank, paymentFormDto.getNumBank())
            );
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getStartTime())) {
            wrapper.ge(PaymentForm::getGmtCreate, paymentFormDto.getStartTime());
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getEndTime())) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try {
                Date gmtTime = sdf.parse(dateFormat(paymentFormDto.getEndTime()));
                wrapper.le(PaymentForm::getGmtCreate, gmtTime);
            } catch (ParseException e) {
                e.printStackTrace();
                throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
            }

        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getSummary())) {
            wrapper.like(PaymentForm::getSummary, paymentFormDto.getSummary());
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getFkPaymentFeeTypeId())) {
            wrapper.eq(PaymentForm::getFkPaymentFeeTypeId, paymentFormDto.getFkPaymentFeeTypeId());
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getAmountStart())) {
            wrapper.apply("amount + service_fee >= {0}", paymentFormDto.getAmountStart());
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getAmountEnd())) {
            wrapper.apply("amount + service_fee <= {0}", paymentFormDto.getAmountEnd());
        }


        wrapper.orderByDesc(PaymentForm::getStatus).last(",CASE WHEN num_bank = 'HKR20220425' THEN 0 ELSE 1 END DESC,gmt_create DESC");
        return wrapper;
    }

    @Override
    public List<PaymentFormVo> packageData(List<PaymentFormVo> collect, PaymentFormDto paymentFormDto, String local) {
        //付款单Id
        Set<Long> fkPayFormIds = collect.stream().map(PaymentFormVo::getId).collect(Collectors.toSet());

        collect = paymentFormMapper.getPayFormListByBindingStatus(paymentFormDto.getBindingStatus(), fkPayFormIds);
        if (GeneralTool.isEmpty(fkPayFormIds)){
            fkPayFormIds.add(0L);
        }
        Map<Long, String> companyMap = getCompanyMap();

        //对应记录ids
        Set<Long> fkTypeTargetIds = collect.stream().filter(f->TableEnum.SALE_AGENT.key.equals(f.getFkTypeKey()))
                .map(PaymentFormVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toSet());
        //根据代理ids查找对应代理名称map
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkTypeTargetIds)) {
            //111111
            agentNamesByIds = saleCenterClient.getAgentNamesByIds(fkTypeTargetIds).getData();
        }
        Set<Long> providerIds = collect.stream().filter(f->TableEnum.INSTITUTION_PROVIDER.key.equals(f.getFkTypeKey()))
                .map(PaymentFormVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> providerByIds = iInstitutionCenterClient.getInstitutionProviderNamesByIds(providerIds).getData();

        Set<Long> businessProviderIds = collect.stream().filter(f->TableEnum.SALE_BUSINESS_PROVIDER.key.equals(f.getFkTypeKey()))
                .map(PaymentFormVo::getFkTypeTargetId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Long, String> businessProviderByIds = saleCenterClient.getBusinessProviderNameByIds(businessProviderIds).getData();

        //付款费用类型ids
        Set<Long> fkPaymentFeeTypeIds = collect.stream().map(PaymentFormVo::getFkPaymentFeeTypeId).filter(Objects::nonNull).collect(Collectors.toSet());
        //根据付款费用类型ids查询名称map
        Map<Long, String> typeNameByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkPaymentFeeTypeIds)) {
            typeNameByIds = paymentFeeTypeService.getTypeNameByIds(fkPaymentFeeTypeIds);
        }

//        Set<String> currencyTypeNums = collect.stream().map(PaymentFormDto::getFkCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());
        Map<String, String>  currencyNamesByNums = currencyTypeService.getAllCurrencyTypeNames();
//        Map<String, String> currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        Map<Long, Agent> agentsByIds = saleCenterClient.getAgentsByIds(fkTypeTargetIds).getData();
        setName(companyMap, collect, agentsByIds, agentNamesByIds, typeNameByIds, currencyNamesByNums,local,providerByIds,businessProviderByIds);
        return collect;
    }

    @Override
    public void batchUpdate(List<PaymentForm> paymentForms) {
        if (GeneralTool.isNotEmpty(paymentForms)) {
            updateBatchById(paymentForms);
        }
    }

    @Override
    public List<PaymentFormVo> datas(PaymentFormDto paymentFormDto, Page page) {
        if (GeneralTool.isEmpty(paymentFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(paymentFormDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        LambdaQueryWrapper<PaymentForm> wrapper = getWrapper(paymentFormDto);
        IPage<PaymentForm> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        page.setAll((int) pages.getTotal());
        List<PaymentForm> paymentForms = pages.getRecords();
        List<PaymentFormVo> collect = BeanCopyUtils.copyListProperties(paymentForms, PaymentFormVo::new);
        String locale = SecureUtil.getLocale();
        if (GeneralTool.isEmpty(collect)){
            return new ArrayList<>();
        }
        return packageData(collect, paymentFormDto,locale);
    }

    @Override
    public Map<Long, PaymentForm> getAgentIds(Set<Long> ids) {
        Map<Long, PaymentForm> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<PaymentForm> paymentForms = paymentFormMapper.selectBatchIds(ids);
        for (PaymentForm paymentForm : paymentForms) {
            map.put(paymentForm.getId(), paymentForm);
        }
        return map;
    }

    @Override
    public void exportPaymentFormExcel(PaymentFormDto paymentFormDto) {
        if (GeneralTool.isEmpty(paymentFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isNotEmpty(paymentFormDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(paymentFormDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        com.get.core.secure.UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        asyncExportService.asyncExportPaymentFormExcel(paymentFormDto, headerMap, user, locale);
//        List<PaymentForm> paymentForms = paymentFormMapper.selectList(getWrapper(paymentFormVo));
//        List<PaymentFormDto> collect = BeanCopyUtils.copyListProperties(paymentForms, PaymentFormDto::new);
//        packageData(collect,paymentFormVo);
//        List<PaymentFormExportDto> paymentFormExportDtos = new ArrayList<>();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        for (PaymentFormDto paymentFormDto : collect) {
//            PaymentFormExportDto paymentFormExportDto = BeanCopyUtils.objClone(paymentFormDto, PaymentFormExportDto::new);
//            if (GeneralTool.isNotEmpty(paymentFormDto.getStatus())) {
//                if (paymentFormDto.getStatus().equals(1)) {
//                    paymentFormExportDto.setStatus("有效");
//                } else {
//                    paymentFormExportDto.setStatus("作废");
//                }
//            }
//            if (GeneralTool.isNotEmpty(paymentFormDto.getGmtCreate())) {
//                paymentFormExportDto.setGmtCreateDate(sdf.format(paymentFormDto.getGmtCreate()));
//            }
//            paymentFormExportDtos.add(paymentFormExportDto);
//        }
//        Map<String, Object> param = new HashMap<>();
//        param.put("list", paymentFormExportDtos);
//        TemplateExcelUtils.downLoadExcel("付款单列表", "PaymentFormInfo.xlsx", param, response);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Long add(PaymentFormDto paymentFormDto) {
        if (GeneralTool.isEmpty(paymentFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        PaymentForm paymentForm = BeanCopyUtils.objClone(paymentFormDto, PaymentForm::new);
        utilService.updateUserInfoToEntity(paymentForm);
        paymentForm.setStatus(1);
        paymentFormMapper.insert(paymentForm);
        //设置系统编号
        String payFormNum = GetStringUtils.getPayFormNum(paymentForm.getId());
        paymentForm.setNumSystem(payFormNum);
        paymentFormMapper.updateById(paymentForm);

        //如果应付计划id不为空，则 将新增的付款单绑定这个应付计划
        if (GeneralTool.isNotEmpty(paymentFormDto.getFkPayablePlanId())) {
            Result<com.get.salecenter.vo.PayablePlanVo> result = saleCenterClient.getPayablePlanDetail(paymentFormDto.getFkPayablePlanId());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            com.get.financecenter.vo.PayablePlanVo payablePlanVo = BeanCopyUtils.objClone(result.getData(), com.get.financecenter.vo.PayablePlanVo::new);
            if (!ProjectExtraEnum.UNSETTLED.key.equals(payablePlanVo.getStatusSettlement())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("PAYABLE_PLAN_PROGRESS"));
            }
            PaymentFormItem paymentFormItem = new PaymentFormItem();
            paymentFormItem.setFkPaymentFormId(paymentForm.getId());
            paymentFormItem.setFkPayablePlanId(paymentFormDto.getFkPayablePlanId());
            paymentFormItem.setAmountPayment(paymentForm.getAmount());
            paymentFormItem.setExchangeRatePayable(new BigDecimal(1));
            paymentFormItem.setAmountPayable(paymentForm.getAmount());
            paymentFormItem.setAmountExchangeRate(new BigDecimal(0));
            paymentFormItem.setExchangeRateHkd(paymentForm.getExchangeRateHkd());
            paymentFormItem.setAmountHkd(paymentForm.getAmountHkd());
            paymentFormItem.setExchangeRateRmb(paymentForm.getExchangeRateRmb());
            paymentFormItem.setAmountRmb(paymentForm.getAmountRmb());
            utilService.updateUserInfoToEntity(paymentFormItem);
            paymentFormItemMapper.insertSelective(paymentFormItem);
        }
        return paymentForm.getId();
    }

    @Override
    public PaymentFormVo update(PaymentFormDto paymentFormDto) {
        if (GeneralTool.isEmpty(paymentFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //校验付款单金额是否小于所绑定的子单
        List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectList(Wrappers.<PaymentFormItem>query()
                .lambda().eq(PaymentFormItem::getFkPaymentFormId, paymentFormDto.getId()));
        BigDecimal totalAmount = new BigDecimal(0);
        if (GeneralTool.isNotEmpty(paymentFormItems)) {
            for (PaymentFormItem paymentFormItem : paymentFormItems) {
                totalAmount = totalAmount.add(paymentFormItem.getAmountPayment());
            }
            if (paymentFormDto.getAmount().compareTo(totalAmount) < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("payment_form_amount_less_binding_amount"));
            }
        }
        PaymentForm paymentForm = BeanCopyUtils.objClone(paymentFormDto, PaymentForm::new);
        utilService.updateUserInfoToEntity(paymentForm);
        paymentFormMapper.updateByIdWithNull(paymentForm);
        return findPaymentFormById(paymentFormDto.getId());
    }

    @Override
    public PaymentFormVo findPaymentFormById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PaymentForm paymentForm = paymentFormMapper.selectById(id);
        PaymentFormVo dto = BeanCopyUtils.objClone(paymentForm, PaymentFormVo::new);
        if (GeneralTool.isNotEmpty(dto.getFkCompanyId())) {
            Result<String> result = permissionCenterClient.getCompanyNameById(dto.getFkCompanyId());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                dto.setFkCompanyName(result.getData());
            }
        }
        if (GeneralTool.isNotEmpty(dto.getFkTypeKey())) {
            dto.setFkTypeKeyName(TableEnum.getValue(dto.getFkTypeKey()));
        }
        if (GeneralTool.isNotEmpty(dto.getFkTypeTargetId())) {
            String targetName = null;
            //111111
            if (TableEnum.INSTITUTION_PROVIDER.key.equals(paymentForm.getFkTypeKey())) {
                targetName = feignInstitutionService.getInstitutionProviderName(dto.getFkTypeTargetId()).getData();
            } else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(paymentForm.getFkTypeKey())) {
                targetName = saleCenterClient.getBusinessProviderNameById(dto.getFkTypeTargetId());
            } else if (TableEnum.SALE_AGENT.key.equals(paymentForm.getFkTypeKey())) {
                targetName = saleCenterClient.getAgentNameById(dto.getFkTypeTargetId()).getData();
            }
            dto.setTargetName(targetName);
        }
        if (GeneralTool.isNotEmpty(dto.getFkCurrencyTypeNum())) {
            dto.setFkCurrencyTypeName(currencyTypeService.getCurrencyNameByNum(dto.getFkCurrencyTypeNum()));
        }
        if (GeneralTool.isNotEmpty(dto.getFkBankAccountId())) {
            dto.setFkBankAccountName(saleCenterClient.getContractBankAccountNameById(dto.getFkBankAccountId(),dto.getFkTypeKey()).getData());
//            vo.setFkBankAccountName(bankAccountMapper.getBankAccountNameById(vo.getFkBankAccountId()));
        }
        if (GeneralTool.isNotEmpty(dto.getFkBankAccountIdCompany())) {
            dto.setFkBankAccountCompanyName(bankAccountMapper.getBankAccountNameById(dto.getFkBankAccountIdCompany()));
        }
        if (GeneralTool.isNotEmpty(paymentForm.getFkPaymentFeeTypeId())) {
            String typeName = paymentFeeTypeService.getTypeNameById(paymentForm.getFkPaymentFeeTypeId());
            if (GeneralTool.isNotEmpty(typeName)) {
                dto.setFkPaymentFeeTypeName(typeName);
            }
        }
//        Example example = new Example(PaymentFormItem.class);
//        example.createCriteria().andEqualTo("fkPaymentFormId", id);
//        List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectByExample(example);
        List<PaymentFormItem> paymentFormItems = paymentFormItemMapper.selectList(Wrappers.<PaymentFormItem>query().lambda().eq(PaymentFormItem::getFkPaymentFormId, id));
        //付款金额（拆分/总）
        BigDecimal amountPayable = new BigDecimal(0);
        for (PaymentFormItem paymentFormItem : paymentFormItems) {
            amountPayable = amountPayable.add(paymentFormItem.getAmountPayment().add(paymentFormItem.getServiceFee()).add(DataConverter.bigDecimalNullConvert(paymentFormItem.getAmountExchangeRate())));
        }
        dto.setAmountPayment(amountPayable);
        dto.setDiffAmount(dto.getAmount().add(dto.getServiceFee()).add(DataConverter.bigDecimalNullConvert(dto.getAmountExchangeRate()))
                .subtract(amountPayable).setScale(2, RoundingMode.UP));
        return dto;
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        paymentFormMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Set<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<PaymentForm> paymentForms = paymentFormMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(paymentForms)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        for (PaymentForm paymentForm:paymentForms)
        {
            paymentForm.setStatus(0);
        }
        this.saveOrUpdateBatch(paymentForms);
    }

    @Override
    public List<FMediaAndAttachedVo> addMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_PAYMENT_FORM.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }

    @Override
    public List<FMediaAndAttachedVo> getMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.FINANCE_PAYMENT_FORM.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public String getCurrencyByFormId(Long formId) {
        if (GeneralTool.isEmpty(formId)) {
            return null;
        }
        PaymentForm paymentForm = paymentFormMapper.selectById(formId);
        if (GeneralTool.isEmpty(paymentForm)) {
            return null;
        }
        return paymentForm.getFkCurrencyTypeNum();
    }

    @Override
    public List<SelItem> getCurrencyByFormIds(Set<Long> formIds) {
        if (GeneralTool.isEmpty(formIds)) {
            return Collections.emptyList();
        }
        return paymentFormMapper.getCurrencyByFormIds(formIds);
    }

    @Override
    public BigDecimal getAmountByFormId(Long formId) {
        if (GeneralTool.isEmpty(formId)) {
            return null;
        }
        PaymentForm paymentForm = paymentFormMapper.selectById(formId);
        if (GeneralTool.isEmpty(paymentForm)) {
            return null;
        }
        return paymentForm.getAmount();
    }

    @Override
    public List<Long> getFormByCompanyId(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)) {
            return null;
        }
//        Example example = new Example(PaymentForm.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId", fkCompanyId);
//        List<PaymentForm> paymentForms = paymentFormMapper.selectByExample(example);
        List<PaymentForm> paymentForms = paymentFormMapper.selectList(Wrappers.<PaymentForm>query().lambda().eq(PaymentForm::getFkCompanyId, fkCompanyId));
        if (GeneralTool.isEmpty(paymentForms)) {
            return null;
        }
        return paymentForms.stream().map(PaymentForm::getId).collect(Collectors.toList());
    }

    @Override
    public Long editComment(CommentDto commentDto) {
        FComment comment = BeanCopyUtils.objClone(commentDto, FComment::new);
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                comment.setFkTableName(TableEnum.FINANCE_PAYMENT_FORM.key);
                commentService.updateComment(comment);
            } else {
                comment.setFkTableName(TableEnum.FINANCE_PAYMENT_FORM.key);
                commentService.addComment(comment);
            }
        }
        return comment.getId();
    }

    @Override
    public List<FCommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.FINANCE_PAYMENT_FORM.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public List<Map<String, Object>> findTypeKeySelect() {
        return TableEnum.enumsTranslation2Arrays(TableEnum.PAY_TARGET_TYPE);
    }

    @Override
    public List<BaseSelectEntity> findTypeTargetSelect(String tableName, Long companyId) {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        if (TableEnum.SALE_AGENT.key.equals(tableName)) {
            //111111
            Result<List<BaseSelectEntity>> result = saleCenterClient.getAgentSelect(companyId);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            baseSelectEntities = result.getData();
        }else if (TableEnum.INSTITUTION_PROVIDER.key.equals(tableName)){
            baseSelectEntities = iInstitutionCenterClient.getInstitutionSelectByCompanyId(companyId);
        }else if(TableEnum.SALE_BUSINESS_PROVIDER.key.equals(tableName)){
            baseSelectEntities = saleCenterClient.getBusinessObjectSelection(companyId).getData();
        }
        baseSelectEntities.removeIf(Objects::isNull);
        return baseSelectEntities;
    }

    /**
     * Author Cream
     * Description : //根据名称获取对象下拉
     * Date 2023/2/20 11:06
     * Params:
     * Return
     */
    @Override
    public List<BaseSelectEntity> getTargetSelectByName(String tableName, String targetName) {
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
        if (TableEnum.SALE_AGENT.key.equals(tableName)) {
            baseSelectEntities =  saleCenterClient.getAgentByTargetName(targetName);
        }else if (TableEnum.SALE_BUSINESS_PROVIDER.key.equals(tableName)){
            baseSelectEntities = saleCenterClient.getBusinessProviderByTargetName(targetName);
        }else if (TableEnum.INSTITUTION_PROVIDER.key.equals(tableName)){
            baseSelectEntities = iInstitutionCenterClient.getInstitutionProviderByTargetName(targetName);
        }
        return baseSelectEntities;
    }

    @Override
    public List<PaymentFormVo> getPayFormList(Long planId) {
        if (GeneralTool.isEmpty(planId)) {
            return null;
        }
        return paymentFormMapper.getPayFormList(planId);
    }

    /**
     * feign根据应付计划ids获取所绑定的付款单子项
     *
     * @Date 16:52 2021/12/2
     * <AUTHOR>
     */
    @Override
    public List<PaymentFormVo> getPayFormListFeignByPlanIds(Set<Long> planIds) {
        if (GeneralTool.isEmpty(planIds)) {
            return null;
        }
        return paymentFormMapper.getPayFormListFeignByPlanIds(planIds);
    }

    /**
     * 修改付款单代理
     *
     * @Date 18:09 2022/12/21
     * <AUTHOR>
     */
    @Override
    @Transactional
    public void updatePaymentFormAgent(PaymentFormAgentUpdateDto paymentFormAgentUpdateDto) {
        PaymentForm paymentForm = paymentFormMapper.selectById(paymentFormAgentUpdateDto.getFkPaymentFormId());
        paymentForm.setFkBankAccountId(paymentFormAgentUpdateDto.getTargetBankAccountId());
        paymentForm.setFkTypeTargetId(paymentFormAgentUpdateDto.getAgentId());
        utilService.setUpdateInfo(paymentForm);
        paymentFormMapper.updateById(paymentForm);
        //修改佣金结算账户
        AgentContractAccountVo agentContractAccountVo = saleCenterClient.getAgentContractAccountByAccountIds(Collections.singletonList(paymentFormAgentUpdateDto.getTargetBankAccountId())).getData().get(0);
        payablePlanSettlementAgentAccountMapper.updateCommissionSettlement(paymentFormAgentUpdateDto, agentContractAccountVo.getFkCurrencyTypeNum());
    }

    /**
     * 获取付款单
     * @param paymentFormId
     * @return
     */
    @Override
    public PaymentForm getPayFormById(Long paymentFormId) {
        if (Objects.isNull(paymentFormId)) {
            return null;
        }
        return paymentFormMapper.selectById(paymentFormId);
    }

    @Override
    public void updatePaymentFormDate(PaymentFormDateDto paymentFormDateDto) {
        if (GeneralTool.isEmpty(paymentFormDateDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        LambdaQueryWrapper<PaymentForm> paymentFormLambdaQueryWrapper = new LambdaQueryWrapper<>();
        paymentFormLambdaQueryWrapper.in(PaymentForm::getId, paymentFormDateDto.getIds());
        PaymentForm paymentForm = new PaymentForm();
        paymentForm.setPaymentDate(paymentFormDateDto.getPaymentDate());
        paymentFormMapper.update(paymentForm, paymentFormLambdaQueryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatchPaymentForms(ServiceFeePaymentFormDto serviceFeePaymentFormDto) {
        List<PaymentFormParamDto> paymentFormParamVoList = serviceFeePaymentFormDto.getPaymentFormParamVoList();
        if (GeneralTool.isEmpty(paymentFormParamVoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        // 所选服务费记录的币种
        Set<String> currencyTypeNums = paymentFormParamVoList.stream().map(PaymentFormParamDto::getFkPayableCurrencyNum)
                .filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());

        Map<String, BigDecimal> hkdRate = Maps.newHashMap();
        Map<String, BigDecimal> cnyRate = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            hkdRate = exchangeRateService.getLastExchangeRate(currencyTypeNums, "HKD");
            cnyRate = exchangeRateService.getLastExchangeRate(currencyTypeNums, "CNY");
        }

        for (PaymentFormParamDto param : paymentFormParamVoList) {
            // 币种
            String currencyNum = param.getFkPayableCurrencyNum();
            // 金额
            BigDecimal paymentAmount = param.getPayableAmount();
            // 服务费
            BigDecimal paymentFee = BigDecimal.ZERO;

            // 创建付款单
            PaymentForm paymentForm = new PaymentForm();
            paymentForm.setFkCompanyId(serviceFeePaymentFormDto.getFkCompanyId());
            paymentForm.setFkTypeKey("m_agent");
            paymentForm.setFkTypeTargetId(param.getFkAgentId());
            paymentForm.setFkBankAccountId(param.getFkBankAccountId());
            paymentForm.setFkBankAccountIdCompany(serviceFeePaymentFormDto.getFkBankAccountId());
            paymentForm.setPaymentDate(serviceFeePaymentFormDto.getPaymentDate());
            paymentForm.setFkCurrencyTypeNum(currencyNum);
            paymentForm.setExchangeRate(new BigDecimal("1.000000"));
            paymentForm.setAmount(paymentAmount);
            paymentForm.setExchangeRateHkd(GeneralTool.isNotEmpty(hkdRate.get(currencyNum)) ? hkdRate.get(currencyNum) : new BigDecimal("1.0000"));
            paymentForm.setAmountHkd(paymentAmount.add(paymentFee).multiply(paymentForm.getExchangeRateHkd()));
            paymentForm.setExchangeRateRmb(GeneralTool.isNotEmpty(cnyRate.get(currencyNum)) ? cnyRate.get(currencyNum) : new BigDecimal("1.0000"));
            paymentForm.setAmountRmb(paymentAmount.add(paymentFee).multiply(paymentForm.getExchangeRateRmb()));
            paymentForm.setServiceFee(paymentFee);
            paymentForm.setStatus(1);
            utilService.setCreateInfo(paymentForm);
            paymentFormMapper.insert(paymentForm);
            if (GeneralTool.isNotEmpty(paymentForm)) {
                String payFormNum = GetStringUtils.getPayFormNum(paymentForm.getId());
                paymentForm.setNumSystem(payFormNum);
                paymentFormMapper.updateById(paymentForm);
            }

            // 创建付款单子项
            PaymentFormItem item = new PaymentFormItem();
            item.setFkPaymentFormId(paymentForm.getId());
            item.setFkPayablePlanId(param.getFkPayablePlanId());
            item.setAmountPayment(paymentAmount);
            item.setServiceFee(paymentFee);
            item.setAmountExchangeRate(BigDecimal.ZERO);
            item.setExchangeRatePayable(new BigDecimal("1.000000"));
            item.setAmountPayable(paymentAmount.add(paymentFee).multiply(item.getExchangeRatePayable()));
            item.setExchangeRateHkd(paymentForm.getExchangeRateHkd());
            item.setAmountHkd(paymentAmount.add(paymentFee).multiply(item.getExchangeRateHkd()));
            item.setExchangeRateRmb(paymentForm.getExchangeRateRmb());
            item.setAmountRmb(paymentAmount.add(paymentFee).multiply(item.getExchangeRateRmb()));
            utilService.setCreateInfo(item);
            paymentFormItemMapper.insert(item);
        }

        if (GeneralTool.isNotEmpty(serviceFeePaymentFormDto.getPayablePlanIds())) {
            Set<Long> planIds = serviceFeePaymentFormDto.getPayablePlanIds();
            // 获取有服务费在途结算的应付计划
            List<PayablePlanSettlementInstallment> installmentList = payablePlanSettlementInstallmentMapper.selectList(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                    .in(PayablePlanSettlementInstallment::getFkPayablePlanId, planIds)
                    .and(w -> w.eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_PROCESSING.key)
                            .or().eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_COMPLETE.key)
                    )
            );
            // 获取有服务费在途结算的应付计划ids
            Set<Long> installmentPayablePlanIds = installmentList.stream().map(PayablePlanSettlementInstallment::getFkPayablePlanId).collect(Collectors.toSet());
            Set<Long> planIdsCopy = new HashSet<>(planIds);
            // 做差集
            planIdsCopy.removeAll(installmentPayablePlanIds);
            // 第一步不算在途，如果第一步有分期表数据，在做实付时需要删除，因为已经直接实付结算
            if (GeneralTool.isNotEmpty(planIdsCopy)) {
                payablePlanSettlementInstallmentMapper.delete(Wrappers.<PayablePlanSettlementInstallment>lambdaQuery()
                        .in(PayablePlanSettlementInstallment::getFkPayablePlanId, planIdsCopy)
                        .eq(PayablePlanSettlementInstallment::getStatus, 0)
                        .eq(PayablePlanSettlementInstallment::getStatusSettlement, 0));
            }
        }
        return true;
    }

    /**
     * 澳小保创建付款单
     *
     * @param insurancePaymentFormDto
     * @return
     */
    @Override
    @Transactional
    public List<InsurancePaymentFormVo> createInsurancePaymentForm(InsurancePaymentFormDto insurancePaymentFormDto) {
        Result<List<com.get.salecenter.vo.PayablePlanVo>> result = saleCenterClient.getPayablePlanDetailsByIds(insurancePaymentFormDto.getFkPayablePlanIds());
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<com.get.salecenter.vo.PayablePlanVo> payablePlanVos = result.getData();
        if (result.getData() == null) {
            throw new GetServiceException("no_payable_plan_found");
        }
        List<InsurancePaymentFormVo> insurancePaymentFormVoList = new ArrayList<>();
        //根据币种分组
        Map<String, List<com.get.salecenter.vo.PayablePlanVo>> planCurrencyMap = payablePlanVos.stream().collect(Collectors.groupingBy(com.get.salecenter.vo.PayablePlanVo::getFkCurrencyTypeNum));
        for (Map.Entry<String, List<com.get.salecenter.vo.PayablePlanVo>> currency : planCurrencyMap.entrySet()) {
            List<com.get.salecenter.vo.PayablePlanVo> payablePlanVoList = currency.getValue();
            BigDecimal amount = payablePlanVoList.stream().map(com.get.salecenter.vo.PayablePlanVo::getPayableAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            PaymentForm paymentForm = new PaymentForm();
            paymentForm.setFkCompanyId(3L);
            paymentForm.setFkTypeKey(TableEnum.SALE_AGENT.key);
            paymentForm.setFkTypeTargetId(insurancePaymentFormDto.getAgentId());
            paymentForm.setFkBankAccountId(insurancePaymentFormDto.getFkBankAccountId());
            paymentForm.setFkBankAccountIdCompany(insurancePaymentFormDto.getFkBankAccountIdCompany());
            paymentForm.setExchangeRate(BigDecimal.ONE);
            paymentForm.setNumBank(insurancePaymentFormDto.getNumBank());
            paymentForm.setPaymentDate(insurancePaymentFormDto.getPaymentDate());
            paymentForm.setFkCurrencyTypeNum(currency.getKey());
            paymentForm.setAmount(amount);
            paymentForm.setServiceFee(BigDecimal.ZERO);
            paymentForm.setExchangeRateHkd(exchangeRateService.getRateByCurrency(paymentForm.getFkCurrencyTypeNum(), "HKD"));
            paymentForm.setAmountHkd(amount.add(paymentForm.getServiceFee()).multiply(paymentForm.getExchangeRateHkd()));
            paymentForm.setExchangeRateRmb(exchangeRateService.getRateByCurrency(paymentForm.getFkCurrencyTypeNum(), "CNY"));
            paymentForm.setAmountRmb(amount.add(paymentForm.getServiceFee()).multiply(paymentForm.getExchangeRateRmb()));
            paymentForm.setSummary(insurancePaymentFormDto.getSummary());
            paymentForm.setStatus(1);
            utilService.setCreateInfo(paymentForm);
            paymentFormMapper.insert(paymentForm);
            //设置系统编号
            String payFormNum = GetStringUtils.getPayFormNum(paymentForm.getId());
            paymentForm.setNumSystem(payFormNum);
            paymentFormMapper.updateById(paymentForm);

            //保存附件
            if (GeneralTool.isNotEmpty(insurancePaymentFormDto.getMediaAndAttachedDtoList())) {
                List<MediaAndAttachedDto> mediaAndAttachedDtos = BeanCopyUtils.copyListProperties(insurancePaymentFormDto.getMediaAndAttachedDtoList(), MediaAndAttachedDto::new);
                mediaAndAttachedDtos.forEach(mediaAndAttachedDto -> {
                    mediaAndAttachedDto.setFkTableId(paymentForm.getId());
                });
                addMedia(mediaAndAttachedDtos);
            }

            for (com.get.salecenter.vo.PayablePlanVo payablePlanVo : payablePlanVoList) {
                PaymentFormItem paymentFormItem = new PaymentFormItem();
                paymentFormItem.setFkPaymentFormId(paymentForm.getId());
                paymentFormItem.setFkPayablePlanId(payablePlanVo.getId());
                paymentFormItem.setAmountPayment(payablePlanVo.getPayableAmount());
                paymentFormItem.setServiceFee(BigDecimal.ZERO);
                paymentFormItem.setExchangeRatePayable(BigDecimal.ONE);
                paymentFormItem.setAmountPayable(payablePlanVo.getPayableAmount());
                paymentFormItem.setAmountExchangeRate(BigDecimal.ONE);
                paymentFormItem.setExchangeRateHkd(paymentForm.getExchangeRateHkd());
                paymentFormItem.setAmountHkd(payablePlanVo.getPayableAmount().multiply(paymentForm.getExchangeRateHkd()));
                paymentFormItem.setExchangeRateRmb(paymentForm.getExchangeRateRmb());
                paymentFormItem.setAmountRmb(payablePlanVo.getPayableAmount().multiply(paymentForm.getExchangeRateRmb()));
                utilService.setCreateInfo(paymentFormItem);
                paymentFormItemMapper.insert(paymentFormItem);

                InsurancePaymentFormVo insurancePaymentFormVo = new InsurancePaymentFormVo();
                insurancePaymentFormVo.setPaymentFormItemId(paymentFormItem.getId());
                insurancePaymentFormVo.setFkPayablePlanId(payablePlanVo.getId());
                insurancePaymentFormVoList.add(insurancePaymentFormVo);
            }

        }
        return insurancePaymentFormVoList;
    }

    private void setName(Map<Long, String> companyMap, List<PaymentFormVo> paymentFormVos,
                         Map<Long, Agent> agentsByIds,
                         Map<Long, String> agentNamesByIds, Map<Long, String> typeNameByIds,
                         Map<String, String> currencyNamesByNums, String local, Map<Long, String> providerByIds, Map<Long, String> businessProviderByIds) {
        Long fkAgentId;
        String nameNote;
        for (PaymentFormVo paymentFormVo : paymentFormVos) {
            paymentFormVo.setFkTypeName(TableEnum.getValue(paymentFormVo.getFkTypeKey(),local));
            if (GeneralTool.isNotEmpty(paymentFormVo.getFkCompanyId())) {
                paymentFormVo.setFkCompanyName(companyMap.get(paymentFormVo.getFkCompanyId()));
            }
            if (GeneralTool.isNotEmpty(agentNamesByIds) && TableEnum.SALE_AGENT.key.equals(paymentFormVo.getFkTypeKey())) {
                String agentName = agentNamesByIds.get(paymentFormVo.getFkTypeTargetId());
                paymentFormVo.setFkTypeTargetName(agentName);
//                fkAgentId = paymentFormDto.getFkTypeTargetId();
//                if (agentsByIds.containsKey(fkAgentId)) {
//                    nameNote = agentsByIds.get(fkAgentId).getNameNote();
//                    if (StringUtils.isNotBlank(nameNote)) {
//                        paymentFormDto.setFkTypeTargetName(paymentFormDto.getFkTypeTargetName() + "(" + nameNote + ")");
//                    }
//                }
            }else if (GeneralTool.isNotEmpty(providerByIds) && TableEnum.INSTITUTION_PROVIDER.key.equals(paymentFormVo.getFkTypeKey())){
                String providerName = providerByIds.get(paymentFormVo.getFkTypeTargetId());
                paymentFormVo.setFkTypeTargetName(providerName);
            }else if (GeneralTool.isNotEmpty(businessProviderByIds) && TableEnum.SALE_BUSINESS_PROVIDER.key.equals(paymentFormVo.getFkTypeKey())){
                String businessProviderName = businessProviderByIds.get(paymentFormVo.getFkTypeTargetId());
                paymentFormVo.setFkTypeTargetName(businessProviderName);
            }
            if (GeneralTool.isNotEmpty(paymentFormVo.getFkPaymentFeeTypeId())) {
                String typeNameBy = typeNameByIds.get(paymentFormVo.getFkPaymentFeeTypeId());
                paymentFormVo.setFkPaymentFeeTypeName(typeNameBy);
            }
            paymentFormVo.setFkCurrencyTypeName(currencyNamesByNums.get(paymentFormVo.getFkCurrencyTypeNum()));
        }
    }

    private Map<Long, String> getCompanyMap() {
//        ListResponseBo responseBo = permissionCenterClient.getAllCompanyDto();
//        JsonConfig config = new JsonConfig();
//        config.setExcludes(new String[]{"departmentTree", "totalNum"});
//        JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas(), config);
//        List<CompanyTreeVo> companyTreeDtos = JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
//        //初始为5的map
//        Map<String, String> companyMap = new HashMap<>(5);
//        if (GeneralTool.isNotEmpty(companyTreeDtos)) {
//            companyMap = companyTreeDtos.stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));
//        }

        Map<Long, String> companyMap = new HashMap<>(5);
        //Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            List<com.get.permissioncenter.vo.tree.CompanyTreeVo> companyTreeVos = result.getData();
            List<CompanyTreeVo> companyTreeDtoList = BeanCopyUtils.copyListProperties(companyTreeVos, com.get.financecenter.vo.CompanyTreeVo::new);
            if (GeneralTool.isNotEmpty(companyTreeDtoList)) {
                companyMap = companyTreeDtoList.stream().collect(Collectors.toMap(com.get.financecenter.vo.CompanyTreeVo::getId, com.get.financecenter.vo.CompanyTreeVo::getShortName));
            }
        }

        return companyMap;
    }

    private String dateFormat(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cd = Calendar.getInstance();
        try {
            cd.setTime(sdf.parse(date));
        } catch (ParseException e) {
            e.printStackTrace();
            throw new GetServiceException(LocaleMessageUtils.getMessage("DATE_FORMAT_CONVERSION_ERROR"));
        }
        //增加一天
        cd.add(Calendar.DATE, 1);
        return sdf.format(cd.getTime());
    }

}

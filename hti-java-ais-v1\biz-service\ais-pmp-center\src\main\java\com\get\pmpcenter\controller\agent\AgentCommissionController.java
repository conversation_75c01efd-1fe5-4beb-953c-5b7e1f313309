package com.get.pmpcenter.controller.agent;

import com.get.common.result.ResponseBo;
import com.get.pmpcenter.dto.agent.SaveAgentCommissionDto;
import com.get.pmpcenter.dto.agent.SavePlanAndCommissionDto;
import com.get.pmpcenter.service.AgentCommissionService;
import com.get.pmpcenter.service.InstitutionProviderCommissionService;
import com.get.pmpcenter.vo.agent.ExtendAgentCommissionVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionListVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 代理佣金明细管理
 */
@Slf4j
@Api(tags = "代理佣金明细管理")
@RestController
@RequestMapping("/agentCommission")
public class AgentCommissionController {

    @Autowired
    private AgentCommissionService agentCommissionService;
    @Autowired
    private InstitutionProviderCommissionService providerCommissionService;

    @ApiOperation(value = "继承模板下的佣金方案明细", notes = "根据继承列表的type和id获取继承模板下的佣金方案明细")
    @GetMapping("/getExtendAgentCommissionList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "类型:1-代理佣金明细;2-学校提供商模板明细", required = true),
            @ApiImplicitParam(name = "companyId", value = "分公司ID", required = true),
            @ApiImplicitParam(name = "agentCommissionTypeId", value = "代理等级ID"),
            @ApiImplicitParam(name = "id", value = "代理佣金方案ID/学校提供商佣金方案:如果type=1表示代理佣金方案ID，type=2表示学校提供商佣金方案ID", required = true),
    })
    public ResponseBo<ExtendAgentCommissionVo> getExtendAgentCommissionList(Integer type, Long id, Long companyId,Long agentCommissionTypeId) {
        return new ResponseBo<>(agentCommissionService.getExtendAgentCommissionList(type, id, companyId,agentCommissionTypeId));
    }

    @ApiOperation(value = "佣金方案明细列表", notes = "根据代理佣金方案id获取佣金方案明细列表")
    @GetMapping("/getAgentCommissionDetail")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planId", value = "代理佣金方案ID", required = true),
    })
    public ResponseBo<ExtendAgentCommissionVo> getAgentCommissionDetail(Long planId) {
        return new ResponseBo<>(agentCommissionService.getAgentCommissionDetail(planId,false,true,null));
    }


    @ApiOperation(value = "学校佣金明细原始列表-原始单项佣金(采购佣金)", notes = "根据学校佣金方案ID获取学校佣金明细原始列表-原始单项佣金(采购佣金)")
    @GetMapping("/getCommissionList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "providerCommissionPlanId", value = "学校佣金方案ID,对应列表的fkInstitutionProviderCommissionPlanId", required = false),
    })
    public ResponseBo<List<ProviderCommissionListVo.CommissionInfo>> getCommissionList(Long providerCommissionPlanId) {
        return new ResponseBo<>(providerCommissionService.getCommissionList(providerCommissionPlanId));
    }

    @ApiOperation(value = "学校佣金明细原始列表-原始组合佣金", notes = "根据学校佣金方案ID获取学校佣金明细原始列表-原始组合佣金")
    @GetMapping("/getCombinationList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "providerCommissionPlanId", value = "学校佣金方案ID,对应列表的fkInstitutionProviderCommissionPlanId", required = false),
    })
    public ResponseBo<List<ProviderCommissionListVo.CombinationInfo>> getCombinationList(Long providerCommissionPlanId) {
        return new ResponseBo<>(providerCommissionService.getCombinationList(providerCommissionPlanId));
    }

    @ApiOperation(value = "学校佣金明细原始列表-原始Bonus佣金", notes = "根据学校佣金方案ID获取学校佣金明细原始列表-原始Bonus佣金")
    @GetMapping("/getBonusInfoList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "providerCommissionPlanId", value = "学校佣金方案ID,对应列表的fkInstitutionProviderCommissionPlanId", required = false),
    })
    public ResponseBo<List<ProviderCommissionListVo.BonusInfo>> getBonusInfoList(Long providerCommissionPlanId) {
        return new ResponseBo<>(providerCommissionService.getBonusInfoList(providerCommissionPlanId));
    }

    @ApiOperation(value = "保存代理佣金方案（方案+明细）")
    @PostMapping("/saveAgentCommission")
    public ResponseBo<Long> saveAgentCommissionPlan(@RequestBody @Valid SavePlanAndCommissionDto agentCommissionDto) {
        return new ResponseBo<>(agentCommissionService.saveAgentCommission(agentCommissionDto));
    }

    @ApiOperation(value = "编辑代理佣金方案明细")
    @PostMapping("/updateAgentCommission")
    public ResponseBo<Long> updateAgentCommission(@RequestBody @Valid SaveAgentCommissionDto agentCommissionDto) {
        return new ResponseBo<>( agentCommissionService.updateAgentCommission(agentCommissionDto));
    }

}

#服务器端口
server:
  port: 1098

#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: partnerdb
      datasource:
        partnerdb:
          url: ${get.datasource.dev.partnerdb.url}
          username: ${get.datasource.dev.partnerdb.username}
          password: ${get.datasource.dev.partnerdb.password}
        saledb:
          url: ${get.datasource.dev.saledb.url}
          username: ${get.datasource.dev.saledb.username}
          password: ${get.datasource.dev.saledb.password}
        permissiondb:
          url: ${get.datasource.dev.permissiondb.url}
          username: ${get.datasource.dev.permissiondb.username}
          password: ${get.datasource.dev.permissiondb.password}
        institutiondb:
          url: ${get.datasource.dev.institutiondb.url}
          username: ${get.datasource.dev.institutiondb.username}
          password: ${get.datasource.dev.institutiondb.password}
        systemdb:
          url: ${get.datasource.dev.systemdb.url}
          username: ${get.datasource.dev.systemdb.username}
          password: ${get.datasource.dev.systemdb.password}
        appfiledb:
          url: ${get.datasource.dev.appfiledb.url}
          username: ${get.datasource.dev.appfiledb.username}
          password: ${get.datasource.dev.appfiledb.password}
        pmp2db:
          url: ${get.datasource.dev.pmp2db.url}
          username: ${get.datasource.dev.pmp2db.username}
          password: ${get.datasource.dev.pmp2db.password}
        platformdb:
          url: ${get.datasource.dev.platformdb.url}
          username: ${get.datasource.dev.platformdb.username}
          password: ${get.datasource.dev.platformdb.password}
package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.AgentCommissionPlanInstitution;
import com.get.pmpcenter.vo.common.AgentPlanInstitutionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentCommissionPlanInstitutionMapper extends BaseMapper<AgentCommissionPlanInstitution> {

    /**
     * 根据学校id查询代理计划
     * @param institutionId
     * @return
     */
    List<AgentPlanInstitutionVo> getAgentPlanInstitution(@Param("institutionId") Long institutionId);
}

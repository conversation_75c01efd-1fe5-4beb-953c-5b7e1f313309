package com.get.reportcenter.service;


import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2022/3/2 17:55
 */
public interface IExportService {
    /**
     * 成功客户列表导出
     *
     * @param queryKey
     * @return
     * @Date 15:22 2022/3/2
     * <AUTHOR>
     */
//    List<StudentOfferItemReportModel> successfullyExportedCustomerList(ReportStudentOfferItemDto queryKey);

    /**
     * 导出竞赛报名名册
     * @param response
     * @param competitionRegistrationListVo
     */
//    void exportCompetitionRegistrationExcel(HttpServletResponse response, CompetitionRegistrationListDto competitionRegistrationListVo);

    void exportLeaveApplicationFormExcel(HttpServletResponse response, LeaveApplicationFormQueryDto leaveApplicationFormVo);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.reportcenter.dao.sale.StudentOfferItemMapper">

  <select id="getStudentOfferItemDtoList" resultType="com.get.reportcenter.vo.StudentOfferItemReportModel">


    SELECT
    msoi.id_gea_finance,
    msoi.id AS appId,
    YEAR ( iii.gmt_create ) AS 'year',
    UPPER(LEFT(MONTHNAME(iii.gmt_create),3)) AS successTime,
    msoi.gmt_create AS applicationTime,
    CONCAT('Q', QUARTER(iii.gmt_create)) AS succQ,
    s.id AS rrId,
    CASE

    WHEN IFNULL( s.name_en, '' )= '' THEN
    s.NAME ELSE CONCAT( s.NAME, '（', s.name_en, '）' )
    <PERSON><PERSON> rr,
    a.id AS agentId,
    a.NAME AS agentSource,
    uas.NAME AS agentRegion,
    stu.id AS studentId,
    stu.NAME AS studentName,
    stu.num_gea AS ourId,
    CONCAT( stu.first_name, stu.last_name ) AS studentPinYin,
    uac.num AS countryNum,
    msoi.fk_institution_id AS schoolId,
    CONCAT(
    i.NAME,
    IF
    (
    i.name_chn IS NULL
    OR i.name_chn = '',
    '',
    CONCAT( "（", i.name_chn, "）" ))) AS schoolName,
    CONCAT(
    msoi.old_institution_name,
    IF
    (
    msoi.old_institution_full_name IS NULL
    OR msoi.old_institution_full_name = '',
    '',
    CONCAT( "（", msoi.old_institution_full_name, "）" ))) AS oldInstitutionName,
    msoi.old_course_custom_name,
    uit.schoolType,
    CONCAT(
    mip.NAME,
    IF
    (
    mip.name_chn IS NULL
    OR mip.name_chn = '',
    '',
    CONCAT( "（", mip.name_chn, "）" ))) AS 'group',
    msoi.fk_institution_course_id AS courseId,
    CONCAT(
    mic.NAME,
    IF
    (
    mic.name_chn IS NULL
    OR mic.name_chn = '',
    '',
    CONCAT( "（", mic.name_chn, "）" ))) AS course,
    course.courseType,
    course.majorType,
    msoi.student_id AS stdId,
    CONCAT(
    mich.NAME,
    IF
    (
    mich.name_chn IS NULL
    OR mich.name_chn = '',
    '',
    CONCAT( "（", mich.name_chn, "）" ))) AS chop,
    YEAR ( msoi.defer_opening_time ) AS intakeYear,
    UPPER(LEFT(MONTHNAME(msoi.defer_opening_time),3)) AS intakeMth,
    CONCAT('Q', QUARTER(msoi.defer_opening_time)) AS intakeQ
--     aTuitionAmount.tuition_amount,
--     aRate.commission_rate,
--     aRate.net_rate,
--     aCommissionAmount.commission_amount + aFixedAmount.fixed_amount AS amount,
--     aBonusAmount.bonus_amount AS additionalAmount,
--     aCommissionAmount.commission_amount + aFixedAmount.fixed_amount + aBonusAmount.bonus_amount AS totalAmt,
--
--     agentRate.commission_rate AS agCommissionRate,
--     agentRate.split_rate AS agSplitRate,
--     agentCommissionAmount.commission_amount + agenFixedAmount.fixed_amount AS agCommissionAmount,
--     agenBonusAmount.bonus_amount AS agBonusAmount,
--     agentCommissionAmount.commission_amount + agenFixedAmount.fixed_amount + agenBonusAmount.bonus_amount AS totalAgentAmt
    FROM
    ais_sale_center.m_student_offer_item AS msoi
    LEFT JOIN ais_permission_center.m_staff AS s ON s.id = msoi.fk_staff_id
    LEFT JOIN ais_sale_center.m_agent AS a ON a.id = msoi.fk_agent_id
    LEFT JOIN ais_institution_center.u_area_state AS uas ON uas.id = a.fk_area_state_id
    LEFT JOIN ais_sale_center.m_student AS stu ON stu.id = msoi.fk_student_id
    LEFT JOIN ais_institution_center.u_area_country AS uac ON uac.id = msoi.fk_area_country_id
    LEFT JOIN ais_institution_center.m_institution AS i ON i.id = msoi.fk_institution_id

    LEFT JOIN (
    SELECT
    ii.id,
    GROUP_CONCAT(
    IF
    (
    uit.type_name_chn IS NULL
    OR uit.type_name_chn = '',
    '',
    CONCAT( "（", uit.type_name_chn, "）" ))) AS schoolType
    FROM
    ais_institution_center.m_institution AS ii
    LEFT JOIN ais_institution_center.u_institution_type AS uit ON uit.id = iI.fk_institution_type_id
    GROUP BY
    ii.id
    ) AS uit ON uit.id = i.id

    LEFT JOIN ais_institution_center.m_institution_provider AS mip ON mip.id = msoi.fk_institution_provider_id
    LEFT JOIN ais_institution_center.m_institution_course AS mic ON mic.id = msoi.fk_institution_course_id

    LEFT JOIN (
    SELECT ii.id,MAX(rsois.gmt_create) AS gmt_create FROM m_student_offer_item AS ii
    LEFT JOIN r_student_offer_item_step AS rsois ON rsois.fk_student_offer_item_id = ii.id
    LEFT JOIN ais_sale_center.u_student_offer_item_step AS usois
    ON usois.id = rsois.fk_student_offer_item_step_id
    WHERE usois.step_key = 'STEP_OFFER_SELECTION'
    GROUP BY ii.id
    ) AS iii ON iii.id = msoi.id


    -- 课程类型名 等级名
    LEFT JOIN (
    SELECT
    micc.id,
    GROUP_CONCAT(
    CONCAT(
    uct.type_name,
    IF
    (
    uct.type_name_chn IS NULL
    OR uct.type_name_chn = '',
    '',
    CONCAT( "（", uct.type_name_chn, "）" )))
    ) AS courseType,
    GROUP_CONCAT(
    CONCAT(
    uml.level_name,
    IF
    (
    uml.level_name_chn IS NULL
    OR uml.level_name_chn = '',
    '',
    CONCAT( "（", uml.level_name_chn, "）" )))
    ) AS majorType
    FROM
    ais_institution_center.m_institution_course AS micc
    LEFT JOIN ais_institution_center.r_institution_course_type AS rict ON rict.fk_institution_course_id = micc.id
    LEFT JOIN ais_institution_center.u_course_type AS uct ON uct.id = rict.fk_course_type_id
    LEFT JOIN ais_institution_center.r_institution_course_major_level AS ricml ON ricml.fk_institution_course_id =
    micc.id
    LEFT JOIN ais_institution_center.u_major_level AS uml ON uml.id = fk_major_level_id
    GROUP BY
    micc.id
    ) AS course ON course.id = mic.id
    LEFT JOIN ais_institution_center.m_institution_channel AS mich ON mich.id = msoi.fk_institution_channel_id

--     -- 应收学费
--     LEFT JOIN (
--     SELECT msoii.id, AVG(mrpp.tuition_amount) AS tuition_amount FROM ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_receivable_plan AS mrpp ON mrpp.fk_type_target_id = msoii.id AND mrpp.fk_type_key =
--     'm_student_offer_item' AND mrpp.status != 0
--     WHERE mrpp.tuition_amount IS NOT NULL
--     GROUP BY msoii.id
--     ) AS aTuitionAmount ON aTuitionAmount.id = msoi.id
--
--     -- 应收费率
--     LEFT JOIN (
--     SELECT msoii.id, AVG(mrpp.commission_rate) AS commission_rate, AVG(mrpp.net_rate) AS net_rate FROM
--     ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_receivable_plan AS mrpp ON mrpp.fk_type_target_id = msoii.id AND mrpp.fk_type_key =
--     'm_student_offer_item' AND mrpp.status != 0
--     WHERE mrpp.commission_rate IS NOT NULL
--     GROUP BY msoii.id
--     ) AS aRate ON aRate.id = msoi.id
--
--     -- 应收佣金金额
--     LEFT JOIN (
--     SELECT msoii.id, AVG(mrpp.commission_amount) AS commission_amount FROM ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_receivable_plan AS mrpp ON mrpp.fk_type_target_id = msoii.id AND mrpp.fk_type_key =
--     'm_student_offer_item' AND mrpp.status != 0
--     WHERE mrpp.commission_amount IS NOT NULL
--     GROUP BY msoii.id
--     ) AS aCommissionAmount ON aCommissionAmount.id = msoi.id
--
--     -- 应收固定金额
--     LEFT JOIN (
--     SELECT msoii.id, AVG(mrpp.fixed_amount) AS fixed_amount FROM ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_receivable_plan AS mrpp ON mrpp.fk_type_target_id = msoii.id AND mrpp.fk_type_key =
--     'm_student_offer_item' AND mrpp.status != 0
--     WHERE mrpp.fixed_amount IS NOT NULL
--     GROUP BY msoii.id
--     ) AS aFixedAmount ON aFixedAmount.id = msoi.id
--
--     -- 应收奖励
--     LEFT JOIN (
--     SELECT msoii.id, AVG(mrpp.bonus_amount) AS bonus_amount FROM ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_receivable_plan AS mrpp ON mrpp.fk_type_target_id = msoii.id AND mrpp.fk_type_key =
--     'm_student_offer_item' AND mrpp.status != 0
--     WHERE mrpp.bonus_amount IS NOT NULL
--     GROUP BY msoii.id
--     ) AS aBonusAmount ON aBonusAmount.id = msoi.id
--
--
--     -- 应付费率
--     LEFT JOIN (
--     SELECT msoii.id, AVG(mppp.commission_rate) AS commission_rate, AVG( mppp.split_rate) AS split_rate FROM
--     ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_payable_plan AS mppp ON mppp.fk_type_target_id = msoii.id AND mppp.fk_type_key =
--     'm_student_offer_item' AND mppp.status != 0
--     WHERE mppp.commission_rate IS NOT NULL
--     GROUP BY msoii.id
--     ) AS agentRate ON agentRate.id = msoi.id
--
--     -- 应付佣金金额
--     LEFT JOIN (
--     SELECT msoii.id, AVG( mppp.commission_amount) AS commission_amount FROM ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_payable_plan AS mppp ON mppp.fk_type_target_id = msoii.id AND mppp.fk_type_key =
--     'm_student_offer_item' AND mppp.status != 0
--     WHERE mppp.commission_amount IS NOT NULL
--     GROUP BY msoii.id
--     ) AS agentCommissionAmount ON agentCommissionAmount.id = msoi.id
--
--     -- 应付固定金额
--     LEFT JOIN (
--     SELECT msoii.id, AVG( mppp.fixed_amount) AS fixed_amount FROM ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_payable_plan AS mppp ON mppp.fk_type_target_id = msoii.id AND mppp.fk_type_key =
--     'm_student_offer_item' AND mppp.status != 0
--     WHERE mppp.fixed_amount IS NOT NULL
--     GROUP BY msoii.id
--     ) AS agenFixedAmount ON agenFixedAmount.id = msoi.id
--
--     -- 应付奖励
--     LEFT JOIN (
--     SELECT msoii.id, AVG( mppp.bonus_amount) AS bonus_amount FROM ais_sale_center.m_student_offer_item AS msoii
--     INNER JOIN ais_sale_center.m_payable_plan AS mppp ON mppp.fk_type_target_id = msoii.id AND mppp.fk_type_key =
--     'm_student_offer_item' AND mppp.status != 0
--     WHERE mppp.bonus_amount IS NOT NULL
--     GROUP BY msoii.id
--     ) AS agenBonusAmount ON agenBonusAmount.id = msoi.id


    INNER JOIN (


    SELECT
    soi.id AS id
    FROM
    ais_sale_center.m_student_offer_item AS soi
    LEFT JOIN
    ais_sale_center.m_student_offer AS so
    ON
    soi.fk_student_offer_id = so.id
    INNER JOIN
    ais_sale_center.m_student AS s
    ON
    so.fk_student_id = s.id
    LEFT JOIN ais_sale_center.m_agent AS ma ON ma.id = so.fk_agent_id
    LEFT JOIN ais_sale_center.s_contact_person AS scp ON scp.fk_table_id = ma.id AND scp.fk_table_name = 'm_agent'
    LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = so.fk_staff_id
    LEFT JOIN
    ais_institution_center.m_institution AS i
    ON
    soi.fk_institution_id = i.id
    LEFT JOIN
    ais_institution_center.m_institution_course AS ic
    ON
    soi.fk_institution_course_id = ic.id
    LEFT JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = soi.fk_student_offer_item_step_id

    WHERE
    1=1
    <if test="studentOfferItemDto.fkCompanyId != null and studentOfferItemDto.fkCompanyId !=''">
      AND s.fk_company_id = #{studentOfferItemDto.fkCompanyId}
      </if>
<!--    <if test="studentOfferItemDto.studentName != null and studentOfferItemDto.studentName !=''">-->
<!--      AND CONCAT(s.`name`,IF(s.first_name is null and s.last_name is null,'',CONCAT("（",s.first_name,"-->
<!--      ",s.last_name,"）"))) like concat("%",#{studentOfferItemDto.studentName},"%")-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.fkInstitutionId != null and studentOfferItemDto.fkInstitutionId !=''">-->
<!--      AND i.id = #{studentOfferItemDto.fkInstitutionId}-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.startTime != null and studentOfferItemDto.startTime.toString() !=''">-->
<!--      AND DATE_FORMAT( soi.opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferItemDto.startTime},-->
<!--      '%Y-%m-%d' )-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.endTime != null and studentOfferItemDto.endTime.toString() !=''">-->
<!--      AND DATE_FORMAT(soi.opening_time,'%Y-%m-%d') <![CDATA[<= ]]>-->
<!--      DATE_FORMAT(#{studentOfferItemDto.endTime},'%Y-%m-%d')-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.studentIds != null and studentOfferItemDto.studentIds !=''">-->
<!--      AND so.fk_student_id IN ${studentOfferItemDto.studentIds}-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.fkStudentOfferItemStepId != null and studentOfferItemDto.fkStudentOfferItemStepId != ''">-->
<!--      AND soi.fk_student_offer_item_step_id = #{studentOfferItemDto.fkStudentOfferItemStepId}-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.statusList != null and studentOfferItemDto.statusList.size()>0">-->
<!--      AND so.status IN-->
<!--      <foreach collection="studentOfferItemDto.statusList" item="status" index="index" open="(" separator=","-->
<!--               close=")">-->
<!--        #{status}-->
<!--      </foreach>-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.fkInstitutionCourseId != null and studentOfferItemDto.fkInstitutionCourseId !=''">-->
<!--      AND soi.fk_institution_course_id = #{studentOfferItemDto.fkInstitutionCourseId}-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.courseName != null and studentOfferItemDto.courseName !=''">-->
<!--      AND ic.name LIKE CONCAT('%', #{studentOfferItemDto.courseName}, '%')-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.schoolName != null and studentOfferItemDto.schoolName !=''">-->
<!--      AND (i.name LIKE CONCAT('%', #{studentOfferItemDto.schoolName}, '%') OR i.name_chn LIKE CONCAT('%',-->
<!--      #{studentOfferItemDto.schoolName}, '%') )-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.agentName != null and studentOfferItemDto.agentName !=''">-->
<!--      AND ma.name LIKE CONCAT('%', #{studentOfferItemDto.agentName}, '%')-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.bdName != null and studentOfferItemDto.bdName !=''">-->
<!--      AND (ms.name LIKE CONCAT('%', #{studentOfferItemDto.bdName}, '%') OR ms.name_en LIKE CONCAT('%',-->
<!--      #{studentOfferItemDto.bdName}, '%'))-->
<!--    </if>-->
<!--    <if test="studentOfferItemDto.fkAreaCountryId != null and studentOfferItemDto.fkAreaCountryId !=''">-->
<!--      AND soi.fk_area_country_id = #{studentOfferItemDto.fkAreaCountryId}-->
<!--    </if>-->
    <if test="studentOfferItemDto.stepOrderList != null and studentOfferItemDto.stepOrderList.size()>0">
      AND usois.step_order IN
      <foreach collection="studentOfferItemDto.stepOrderList" item="stepOrder" index="index" open="(" separator=","
               close=")">
        #{stepOrder}
      </foreach>
      </if>
    group by
    soi.id
    ) aa ON aa.id = msoi.id
    where msoi.status = 1
        <if test="studentOfferItemDto.startTime != null and studentOfferItemDto.startTime.toString() !=''">
          AND DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferItemDto.startTime},
          '%Y-%m-%d' )
      </if>
        <if test="studentOfferItemDto.endTime != null and studentOfferItemDto.endTime.toString() !=''">
          AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]>
          DATE_FORMAT(#{studentOfferItemDto.endTime},'%Y-%m-%d')
      </if>

  </select>
  <select id="getReceiptFormItemDtoListByOfferItem"
          resultType="com.get.financecenter.vo.ReceiptFormItemVo">
    SELECT
    msoi.id AS fkStudentOfferItemId,
    mrfi.*,
    mrf.fk_currency_type_num,
    uba.bank_name
    FROM
    ais_sale_center.m_student_offer_item AS msoi
    INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msoi.id
    AND mrp.fk_type_key = 'm_student_offer_item'
    AND mrp.STATUS != 0
    INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
    INNER JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id AND mrf.STATUS != 0
    INNER JOIN ais_finance_center.u_bank_account AS uba ON uba.id =  mrf.fk_bank_account_id
    -- 	   INNER JOIN 成功的学习计划查询结果
    INNER JOIN (
    SELECT
    soi.id AS id
    FROM
    ais_sale_center.m_student_offer_item AS soi
    LEFT JOIN
    ais_sale_center.m_student_offer AS so
    ON
    soi.fk_student_offer_id = so.id
    INNER JOIN
    ais_sale_center.m_student AS s
    ON
    so.fk_student_id = s.id
    INNER JOIN ais_sale_center.m_agent AS ma ON ma.id = so.fk_agent_id
    LEFT JOIN ais_sale_center.s_contact_person AS scp ON scp.fk_table_id = ma.id AND scp.fk_table_name = 'm_agent'
    INNER JOIN ais_permission_center.m_staff AS ms ON ms.id = so.fk_staff_id
    LEFT JOIN
    ais_institution_center.m_institution AS i
    ON
    soi.fk_institution_id = i.id
    LEFT JOIN
    ais_institution_center.m_institution_course AS ic
    ON
    soi.fk_institution_course_id = ic.id
    INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = soi.fk_student_offer_item_step_id

    WHERE
    1=1
    <if test="studentOfferItemDto.fkCompanyId != null and studentOfferItemDto.fkCompanyId !=''">
      AND s.fk_company_id = #{studentOfferItemDto.fkCompanyId}
      </if>
    <!--    <if test="studentOfferItemDto.studentName != null and studentOfferItemDto.studentName !=''">-->
    <!--      AND CONCAT(s.`name`,IF(s.first_name is null and s.last_name is null,'',CONCAT("（",s.first_name,"-->
    <!--      ",s.last_name,"）"))) like concat("%",#{studentOfferItemDto.studentName},"%")-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.fkInstitutionId != null and studentOfferItemDto.fkInstitutionId !=''">-->
    <!--      AND i.id = #{studentOfferItemDto.fkInstitutionId}-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.startTime != null and studentOfferItemDto.startTime.toString() !=''">-->
    <!--      AND DATE_FORMAT( soi.opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferItemDto.startTime},-->
    <!--      '%Y-%m-%d' )-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.endTime != null and studentOfferItemDto.endTime.toString() !=''">-->
    <!--      AND DATE_FORMAT(soi.opening_time,'%Y-%m-%d') <![CDATA[<= ]]>-->
    <!--      DATE_FORMAT(#{studentOfferItemDto.endTime},'%Y-%m-%d')-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.studentIds != null and studentOfferItemDto.studentIds !=''">-->
    <!--      AND so.fk_student_id IN ${studentOfferItemDto.studentIds}-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.fkStudentOfferItemStepId != null and studentOfferItemDto.fkStudentOfferItemStepId != ''">-->
    <!--      AND soi.fk_student_offer_item_step_id = #{studentOfferItemDto.fkStudentOfferItemStepId}-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.statusList != null and studentOfferItemDto.statusList.size()>0">-->
    <!--      AND so.status IN-->
    <!--      <foreach collection="studentOfferItemDto.statusList" item="status" index="index" open="(" separator=","-->
    <!--               close=")">-->
    <!--        #{status}-->
    <!--      </foreach>-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.fkInstitutionCourseId != null and studentOfferItemDto.fkInstitutionCourseId !=''">-->
    <!--      AND soi.fk_institution_course_id = #{studentOfferItemDto.fkInstitutionCourseId}-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.courseName != null and studentOfferItemDto.courseName !=''">-->
    <!--      AND ic.name LIKE CONCAT('%', #{studentOfferItemDto.courseName}, '%')-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.schoolName != null and studentOfferItemDto.schoolName !=''">-->
    <!--      AND (i.name LIKE CONCAT('%', #{studentOfferItemDto.schoolName}, '%') OR i.name_chn LIKE CONCAT('%',-->
    <!--      #{studentOfferItemDto.schoolName}, '%') )-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.agentName != null and studentOfferItemDto.agentName !=''">-->
    <!--      AND ma.name LIKE CONCAT('%', #{studentOfferItemDto.agentName}, '%')-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.bdName != null and studentOfferItemDto.bdName !=''">-->
    <!--      AND (ms.name LIKE CONCAT('%', #{studentOfferItemDto.bdName}, '%') OR ms.name_en LIKE CONCAT('%',-->
    <!--      #{studentOfferItemDto.bdName}, '%'))-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.fkAreaCountryId != null and studentOfferItemDto.fkAreaCountryId !=''">-->
    <!--      AND soi.fk_area_country_id = #{studentOfferItemDto.fkAreaCountryId}-->
    <!--    </if>-->
    <if test="studentOfferItemDto.stepOrderList != null and studentOfferItemDto.stepOrderList.size()>0">
      AND usois.step_order IN
      <foreach collection="studentOfferItemDto.stepOrderList" item="stepOrder" index="index" open="(" separator=","
               close=")">
        #{stepOrder}
      </foreach>
      </if>
    group by
    soi.id
    ) aa ON aa.id = msoi.id
    where msoi.status = 1
  </select>
  <select id="getPaymentFormItemDtoListByOfferItem" resultType="com.get.financecenter.vo.PaymentFormItemVo">
    SELECT
      msoi.id AS fkStudentOfferItemId,
      mpfi.*,
    mpf.fk_currency_type_num
    FROM
      ais_sale_center.m_student_offer_item AS msoi
        INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id
        AND mpp.fk_type_key = 'm_student_offer_item'
        AND mpp.STATUS != 0
	INNER JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.fk_payable_plan_id = mpp.id
      INNER JOIN ais_finance_center.m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
      AND mpf.STATUS != 0
    -- 	   INNER JOIN 成功的学习计划查询结果
    INNER JOIN (
    SELECT
    soi.id AS id
    FROM
    ais_sale_center.m_student_offer_item AS soi
    LEFT JOIN
    ais_sale_center.m_student_offer AS so
    ON
    soi.fk_student_offer_id = so.id
    INNER JOIN
    ais_sale_center.m_student AS s
    ON
    so.fk_student_id = s.id
    INNER JOIN ais_sale_center.m_agent AS ma ON ma.id = so.fk_agent_id
    LEFT JOIN ais_sale_center.s_contact_person AS scp ON scp.fk_table_id = ma.id AND scp.fk_table_name = 'm_agent'
    INNER JOIN ais_permission_center.m_staff AS ms ON ms.id = so.fk_staff_id
    LEFT JOIN
    ais_institution_center.m_institution AS i
    ON
    soi.fk_institution_id = i.id
    LEFT JOIN
    ais_institution_center.m_institution_course AS ic
    ON
    soi.fk_institution_course_id = ic.id
    INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = soi.fk_student_offer_item_step_id

    WHERE
    1=1
    <if test="studentOfferItemDto.fkCompanyId != null and studentOfferItemDto.fkCompanyId !=''">
      AND s.fk_company_id = #{studentOfferItemDto.fkCompanyId}
      </if>
    <!--    <if test="studentOfferItemDto.studentName != null and studentOfferItemDto.studentName !=''">-->
    <!--      AND CONCAT(s.`name`,IF(s.first_name is null and s.last_name is null,'',CONCAT("（",s.first_name,"-->
    <!--      ",s.last_name,"）"))) like concat("%",#{studentOfferItemDto.studentName},"%")-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.fkInstitutionId != null and studentOfferItemDto.fkInstitutionId !=''">-->
    <!--      AND i.id = #{studentOfferItemDto.fkInstitutionId}-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.startTime != null and studentOfferItemDto.startTime.toString() !=''">-->
    <!--      AND DATE_FORMAT( soi.opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{studentOfferItemDto.startTime},-->
    <!--      '%Y-%m-%d' )-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.endTime != null and studentOfferItemDto.endTime.toString() !=''">-->
    <!--      AND DATE_FORMAT(soi.opening_time,'%Y-%m-%d') <![CDATA[<= ]]>-->
    <!--      DATE_FORMAT(#{studentOfferItemDto.endTime},'%Y-%m-%d')-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.studentIds != null and studentOfferItemDto.studentIds !=''">-->
    <!--      AND so.fk_student_id IN ${studentOfferItemDto.studentIds}-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.fkStudentOfferItemStepId != null and studentOfferItemDto.fkStudentOfferItemStepId != ''">-->
    <!--      AND soi.fk_student_offer_item_step_id = #{studentOfferItemDto.fkStudentOfferItemStepId}-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.statusList != null and studentOfferItemDto.statusList.size()>0">-->
    <!--      AND so.status IN-->
    <!--      <foreach collection="studentOfferItemDto.statusList" item="status" index="index" open="(" separator=","-->
    <!--               close=")">-->
    <!--        #{status}-->
    <!--      </foreach>-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.fkInstitutionCourseId != null and studentOfferItemDto.fkInstitutionCourseId !=''">-->
    <!--      AND soi.fk_institution_course_id = #{studentOfferItemDto.fkInstitutionCourseId}-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.courseName != null and studentOfferItemDto.courseName !=''">-->
    <!--      AND ic.name LIKE CONCAT('%', #{studentOfferItemDto.courseName}, '%')-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.schoolName != null and studentOfferItemDto.schoolName !=''">-->
    <!--      AND (i.name LIKE CONCAT('%', #{studentOfferItemDto.schoolName}, '%') OR i.name_chn LIKE CONCAT('%',-->
    <!--      #{studentOfferItemDto.schoolName}, '%') )-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.agentName != null and studentOfferItemDto.agentName !=''">-->
    <!--      AND ma.name LIKE CONCAT('%', #{studentOfferItemDto.agentName}, '%')-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.bdName != null and studentOfferItemDto.bdName !=''">-->
    <!--      AND (ms.name LIKE CONCAT('%', #{studentOfferItemDto.bdName}, '%') OR ms.name_en LIKE CONCAT('%',-->
    <!--      #{studentOfferItemDto.bdName}, '%'))-->
    <!--    </if>-->
    <!--    <if test="studentOfferItemDto.fkAreaCountryId != null and studentOfferItemDto.fkAreaCountryId !=''">-->
    <!--      AND soi.fk_area_country_id = #{studentOfferItemDto.fkAreaCountryId}-->
    <!--    </if>-->
    <if test="studentOfferItemDto.stepOrderList != null and studentOfferItemDto.stepOrderList.size()>0">
      AND usois.step_order IN
      <foreach collection="studentOfferItemDto.stepOrderList" item="stepOrder" index="index" open="(" separator=","
               close=")">
        #{stepOrder}
      </foreach>
      </if>
    group by
    soi.id
    ) aa ON aa.id = msoi.id
    where msoi.status = 1
  </select>


</mapper>
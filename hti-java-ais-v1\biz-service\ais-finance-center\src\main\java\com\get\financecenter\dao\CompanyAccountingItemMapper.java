package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.CompanyAccountingDto;
import com.get.financecenter.dto.CompanyAccountingItemDto;
import com.get.financecenter.entity.CompanyAccountingItem;
import com.get.financecenter.vo.CompanyAccountingItemVo;
import com.get.financecenter.vo.CompanyAccountingVo;
import com.get.permissioncenter.entity.Company;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 账套科目Mapper
 */
@Mapper
public interface CompanyAccountingItemMapper extends BaseMapper<CompanyAccountingItem> {

    List<CompanyAccountingItemVo> getCompanyAccountingItem(IPage<CompanyAccountingItem> pages,@Param("companyAccountingItemDto") CompanyAccountingItemDto companyAccountingItemDto);

    List<CompanyAccountingVo> getCompanyAccountingDatas(IPage<Company> pages,@Param("companyAccountingDto") CompanyAccountingDto companyAccountingDto,@Param("companyIds") List<Long> companyIds);

    List<Long> selectBatchFkAccountingItemIds(@Param("deleteFkAccountingItemIds")List<Long> deleteFkAccountingItemIds, @Param("fkCompanyId") Long fkCompanyId);

    List<Long> getCompanyProfitAndLossItemBind(@Param("fkCompanyId") Long fkCompanyId);
}


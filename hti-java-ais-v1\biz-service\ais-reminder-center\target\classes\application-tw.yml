#服务器端口
server:
  port: 8093

#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: reminderdb
      datasource:
        reminderdb:
          url: *****************************************************************************************************************************
          username: root
          password: get_sig)TpqIx6rr_2024
        reminderdb-doris:
          url: *****************************************************************************************************************************
          username: root
          password: HTITEST_ROOT_DORIS@AJL3W03D_2024

  mail:
    port: 465
    host: smtp.exmail.qq.com
    username: <EMAIL>
    password: GZEtLhkf6WzpcPd9
    default-encoding: utf-8
    protocol: smtp
    properties:
      mail:
        username: <EMAIL>
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: true
            socketFactory:
              port: 465
              class: javax.net.ssl.SSLSocketFactory
    financeMail: <EMAIL>

emailSwitch: ${emailSwitch}
accessKeyId: ${accessKeyId}
accessKeySecret: ${accessKeySecret}
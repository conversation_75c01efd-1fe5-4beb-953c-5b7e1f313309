<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.UBannerTypeMapper">

    <resultMap id="BaseResultMap" type="com.get.aisplatformcenterap.entity.UBannerTypeEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="fkPlatformId" column="fk_platform_id" jdbcType="BIGINT"/>
            <result property="fkPlatformCode" column="fk_platform_code" jdbcType="VARCHAR"/>
            <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
            <result property="typeKey" column="type_key" jdbcType="VARCHAR"/>
            <result property="length" column="length" jdbcType="INTEGER"/>
            <result property="width" column="width" jdbcType="INTEGER"/>
            <result property="viewOrder" column="view_order" jdbcType="INTEGER"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, fk_company_id,
        fk_platform_id,
        fk_platform_code,type_name,type_name_chn,
        type_key,length,width,
        view_order,gmt_create,gmt_create_user,
        gmt_modified,gmt_modified_user
    </sql>
</mapper>

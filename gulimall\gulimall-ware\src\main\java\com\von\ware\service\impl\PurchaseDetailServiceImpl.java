package com.von.ware.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.von.common.utils.PageUtils;
import com.von.common.utils.Query;
import com.von.ware.dao.PurchaseDetailDao;
import com.von.ware.entity.PurchaseDetailEntity;
import com.von.ware.service.PurchaseDetailService;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service("purchaseDetailService")
public class PurchaseDetailServiceImpl extends ServiceImpl<PurchaseDetailDao, PurchaseDetailEntity> implements PurchaseDetailService {

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<PurchaseDetailEntity> wrapper = new QueryWrapper<>();
        String key = ObjectUtil.isNull(params.get("key")) ? "" : params.get("key").toString();
        String status = ObjectUtil.isNull(params.get("status")) ? "" : params.get("status").toString();
        String wareId = ObjectUtil.isNull(params.get("wareId")) ? "" : params.get("wareId").toString();
        if (StrUtil.isNotBlank(key)) {
            wrapper.eq("purchase_id", key).or()
                    .eq("sku_id", key);
        }
        if (StrUtil.isNotBlank(status)) {
            wrapper.eq("status", status);
        }
        if (StrUtil.isNotBlank(wareId)) {
            wrapper.eq("ware_id", wareId);
        }


        IPage<PurchaseDetailEntity> page = this.page(
                new Query<PurchaseDetailEntity>().getPage(params),
                wrapper
        );

        return new PageUtils(page);
    }

}
package com.get.aisplatformcenter.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.MLiveMapper;
import com.get.aisplatformcenter.mapper.SMediaAndAttachedMapper;
import com.get.aisplatformcenter.service.MLiveService;
import com.get.aisplatformcenter.util.TencentCloudUtils;
import com.get.aisplatformcenterap.dto.MLiveDto;
import com.get.aisplatformcenterap.dto.MLivePutAwayParamsDto;
import com.get.aisplatformcenterap.entity.MLiveEntity;
import com.get.aisplatformcenterap.entity.SMediaAndAttachedEntity;
import com.get.aisplatformcenterap.vo.FileArray;
import com.get.aisplatformcenterap.vo.MLiveVo;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【m_live】的数据库操作Service实现
* @createDate 2024-11-28 19:30:56
*/
@Service
public class MLiveServiceImpl extends ServiceImpl<MLiveMapper, MLiveEntity>
    implements MLiveService {
    @Autowired
    private MLiveMapper mLiveMapper;
    @Autowired
    private UtilService utilService;

    @Autowired
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Autowired
    private TencentCloudUtils tencentCloudUtils;

    @Override
    public List<MLiveVo> searchPage(MLiveDto params, Page page) {
        IPage<MLiveVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setMMageAddress(urlBase);
        List<MLiveVo> agentDtos = mLiveMapper.searchPage(pages, params);

        page.setAll((int) pages.getTotal());

        return agentDtos;
    }

    @Override
    public Long saveOrUpdateMlive(MLiveDto mlivedto) {
        if (mlivedto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        if(mlivedto.getLiveTimeEnd()==null){
            // 获取当前日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(mlivedto.getLiveTimeStart());
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH)+1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            // 组合日期和时间
            LocalDateTime endOfDayDateTime = LocalDateTime.of(year,month,day, 23,59,59);
            Timestamp endOfDayTimestamp = Timestamp.valueOf(endOfDayDateTime);
            mlivedto.setLiveTimeEnd(endOfDayTimestamp);
        }

        MLiveEntity mlivepo = BeanCopyUtils.objClone(mlivedto, MLiveEntity::new);

        if(mlivedto.getLiveStatus()!=null){
            mlivepo.setLiveStatus(mlivedto.getLiveStatus());
        }else {
            if(mlivepo.getFkLiveTypeId()!=null && mlivepo.getFkLiveTypeId().longValue()==1){
                mlivepo.setLiveStatus(5);
            }else {
                mlivepo.setLiveStatus(0);
            }

        }

        utilService.updateUserInfoToEntity(mlivepo);

        UserInfo user = GetAuthInfo.getUser();
        mlivepo.setFkCompanyId(user.getFkCompanyId());
        if(mlivepo.getId()==null){
            mlivepo.setStatus(1);
            if(mlivepo.getFkLiveTypeId().longValue()==2){//live
                if(mlivepo.getLiveStatus()==null){
                    mlivepo.setLiveStatus(0);
                }

            } else if (mlivepo.getFkLiveTypeId().longValue()==1) {//recordedVideo
                if(mlivepo.getLiveStatus()==null){
                    mlivepo.setLiveStatus(5);
                }
            }


            mLiveMapper.insert(mlivepo);

            if(mlivedto.getFileGuid()!=null && !"".equals(mlivedto.getFileGuid())){
                SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                sMediaAndAttachedEntity.setFkFileGuid(mlivedto.getFileGuid());
                sMediaAndAttachedEntity.setFkTableName("m_live");
                sMediaAndAttachedEntity.setFkTableId(mlivepo.getId());
                sMediaAndAttachedEntity.setTypeKey("m_live_logo");
                sMediaAndAttachedEntity.setRemark("直播logo");
                sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                sMediaAndAttachedEntity.setGmtCreate(new Date());
                sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
            }
            if(mlivedto.getFileLiveGuid()!=null && mlivedto.getFileLiveGuid().length>0){
                String[] fileArry= mlivedto.getFileLiveGuid();
               int size= fileArry.length;
                for(int i=0; i<size; i++){
                    String fileguid=fileArry[i];
                    SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                    sMediaAndAttachedEntity.setFkFileGuid(fileguid);
                    sMediaAndAttachedEntity.setFkTableName("m_live");
                    sMediaAndAttachedEntity.setFkTableId(mlivepo.getId());
                    sMediaAndAttachedEntity.setTypeKey("m_live_file");
                    sMediaAndAttachedEntity.setRemark("直播文件");
                    sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                    sMediaAndAttachedEntity.setGmtCreate(new Date());
                    sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                }


            }

        }else{
            if(mlivedto.getFileGuid()!=null && !"".equals(mlivedto.getFileGuid())){
                SMediaAndAttachedEntity attPo=sMediaAndAttachedMapper.selectOne(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                        .eq(SMediaAndAttachedEntity::getFkTableName, "m_live")
                        .eq(SMediaAndAttachedEntity::getFkTableId,mlivepo.getId())
                        .eq(SMediaAndAttachedEntity::getTypeKey, "m_live_logo")
                );
                if(attPo==null){
                    SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                    sMediaAndAttachedEntity.setFkFileGuid(mlivedto.getFileGuid());
                    sMediaAndAttachedEntity.setFkTableName("m_live");
                    sMediaAndAttachedEntity.setFkTableId(mlivepo.getId());
                    sMediaAndAttachedEntity.setTypeKey("m_live_logo");
                    sMediaAndAttachedEntity.setRemark("直播logo");
                    sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                    sMediaAndAttachedEntity.setGmtCreate(new Date());

                    sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);

                }else{
                    if(StringUtils.isEmpty(attPo.getFkFileGuid()) ||  !mlivedto.getFileGuid().equals(attPo.getFkFileGuid()) ){
                        sMediaAndAttachedMapper.deleteById(attPo.getId());

                        SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                        sMediaAndAttachedEntity.setFkFileGuid(mlivedto.getFileGuid());
                        sMediaAndAttachedEntity.setFkTableName("m_live");
                        sMediaAndAttachedEntity.setFkTableId(mlivepo.getId());
                        sMediaAndAttachedEntity.setTypeKey("m_live_logo");
                        sMediaAndAttachedEntity.setRemark("直播logo");
                        sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                        sMediaAndAttachedEntity.setGmtCreate(new Date());

                        sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);

                    }
                }

            }

            List<SMediaAndAttachedEntity> listLiveFile= sMediaAndAttachedMapper.selectList(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                    .eq(SMediaAndAttachedEntity::getFkTableName, "m_live")
                    .eq(SMediaAndAttachedEntity::getFkTableId,mlivepo.getId())
                    .eq(SMediaAndAttachedEntity::getTypeKey, "m_live_file")
            );
            Map<String,SMediaAndAttachedEntity> mapLiveFile=new HashMap<String,SMediaAndAttachedEntity>();
            if(ObjectUtils.isNotEmpty(listLiveFile)){
                mapLiveFile=listLiveFile.stream().collect(Collectors.toMap(o->o.getFkFileGuid(),o->o,(v1,v2)->v1));
            }

            if(mlivedto.getFileLiveGuid()!=null && mlivedto.getFileLiveGuid().length>0){
                String[] fileArry= mlivedto.getFileLiveGuid();
                for(int i=0; i<fileArry.length; i++){
                    String fileguid=fileArry[i];

                    SMediaAndAttachedEntity attPo=mapLiveFile.get(fileguid);

                    if(attPo==null) {
                        SMediaAndAttachedEntity sMediaAndAttachedEntity = new SMediaAndAttachedEntity();
                        sMediaAndAttachedEntity.setFkFileGuid(fileguid);
                        sMediaAndAttachedEntity.setFkTableName("m_live");
                        sMediaAndAttachedEntity.setFkTableId(mlivepo.getId());
                        sMediaAndAttachedEntity.setTypeKey("m_live_file");
                        sMediaAndAttachedEntity.setRemark("直播文件");
                        sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                        sMediaAndAttachedEntity.setGmtCreate(new Date());
                        sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                    } else  {
                        mapLiveFile.remove(fileguid);
                    }
                }
                if(mapLiveFile.size()>0){
                    Set<String> keyFileguidArr=mapLiveFile.keySet();
                    for(String fileguid:keyFileguidArr){
                        SMediaAndAttachedEntity deletefile=mapLiveFile.get(fileguid);
                        sMediaAndAttachedMapper.deleteById(deletefile.getId());
                    }

                }


            }





            mLiveMapper.updateById(mlivepo);
        }


        return mlivepo.getId();
    }

    @Override
    public MLiveVo getMliveDetail(long id) {
        MLiveVo resultdto = new MLiveVo();
        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        MLiveDto params=new MLiveDto();
        params.setId(id);
        params.setMMageAddress(urlBase);
        resultdto = mLiveMapper.selectByDetail(params);

        SMediaAndAttachedEntity attPo=sMediaAndAttachedMapper.selectOne(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                .eq(SMediaAndAttachedEntity::getFkTableName, "m_live")
                .eq(SMediaAndAttachedEntity::getTypeKey, "m_live_logo")
                .eq(SMediaAndAttachedEntity::getFkTableId, id)
        );
        if(attPo!=null){
            resultdto.setFileGuid(attPo.getFkFileGuid());
        }

        List<FileArray> fileArrays= sMediaAndAttachedMapper.selectFileArrays(params);
        if(ObjectUtils.isNotEmpty(fileArrays)){
            resultdto.setLiveFile(fileArrays);
        }
        return resultdto;
    }

    @Override
    public Long putAwayMessage(MLivePutAwayParamsDto vo) {
        if (vo == null) {
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","数据为空!"));
        }


        if(vo.getType()==1) {//上架
            mLiveMapper.updateByIds(vo.getType(),vo.getIds());

        }else if (vo.getType()==0) {//下架
           /* LambdaQueryWrapper<MLiveEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(MLiveEntity::getStatus, 2);
            int num=mLiveMapper.selectCount(wrapper);
            if(num<=1){
                throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                        LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","在架消息，最少保留一条在架!"));
            }*/

            mLiveMapper.updateByIds(vo.getType(),vo.getIds());
        }
        return 0l;
    }

    @Override
    public boolean removeByIdInfo(Long id) {
        boolean result = true;

        mLiveMapper.deleteById(id);
        List<SMediaAndAttachedEntity> attArrList=sMediaAndAttachedMapper.selectList(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                .eq(SMediaAndAttachedEntity::getFkTableName, "m_live")
                .eq(SMediaAndAttachedEntity::getFkTableId, id)
        );

        if(!ObjectUtils.isEmpty(attArrList)){
            for(SMediaAndAttachedEntity tmppo:attArrList){
                sMediaAndAttachedMapper.deleteById(tmppo.getId());
            }

        }





        return result;
    }


}





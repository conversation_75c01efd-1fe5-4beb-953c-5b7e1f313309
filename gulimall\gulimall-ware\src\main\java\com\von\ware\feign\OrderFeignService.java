package com.von.ware.feign;

import com.von.common.utils.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @Date 2024/12/29 20:20
 * @Version 1.0
 */
@FeignClient("gulimall-order")
public interface OrderFeignService {

    @GetMapping("/order/order/getOrderByOrderSn/{orderSn}")
    R getOrderByOrderSn(@PathVariable("orderSn") String orderSn);

}

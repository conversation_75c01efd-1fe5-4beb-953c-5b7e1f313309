package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeSkillMapper;
import com.get.resumecenter.vo.ResumeSkillVo;
import com.get.resumecenter.entity.ResumeSkill;
import com.get.resumecenter.service.IResumeSkillService;
import com.get.resumecenter.service.ISkillTypeService;
import com.get.resumecenter.dto.ResumeSkillDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 15:29
 * @Description: 技能管理实现类
 **/
@Service
public class ResumeSkillServiceImpl implements IResumeSkillService {
    @Resource
    private ResumeSkillMapper skillMapper;
    @Resource
    private ISkillTypeService skillTypeService;
    @Resource
    private UtilService utilService;


    @Override
    public List<ResumeSkillVo> getResumeSkillListDto(Long resumeId) {
        if (GeneralTool.isEmpty(resumeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
//        Example example = new Example(ResumeSkill.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
        List<ResumeSkill> resumeSkills = skillMapper.selectList(Wrappers.<ResumeSkill>lambdaQuery().eq(ResumeSkill::getFkResumeId, resumeId));
        List<ResumeSkillVo> collect = resumeSkills.stream()
                .map(resumeSkill -> BeanCopyUtils.objClone(resumeSkill, ResumeSkillVo::new)).collect(Collectors.toList());
        for (ResumeSkillVo resumeSkillVo : collect) {
            if (GeneralTool.isNotEmpty(resumeSkillVo.getFkSkillTypeId())) {
                String typeName = skillTypeService.getNameByTypeId(resumeSkillVo.getFkSkillTypeId());
                resumeSkillVo.setFkSkillTypeName(typeName);
            }
        }
        return collect;
    }

    @Override
    public ResumeSkillVo getResumeSkillById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeSkill resumeSkill = skillMapper.selectById(id);
        return BeanCopyUtils.objClone(resumeSkill, ResumeSkillVo::new);
    }

    @Override
    public Long addResumeSkill(ResumeSkillDto skillVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(skillVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeSkill resumeSkill = BeanCopyUtils.objClone(skillVo, ResumeSkill::new);
        utilService.updateUserInfoToEntity(resumeSkill);
        int i = skillMapper.insertSelective(resumeSkill);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return resumeSkill.getId();

    }

    @Override
    public ResumeSkillVo updateResumeSkill(ResumeSkillDto skillVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(skillVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ResumeSkill resumeSkill = BeanCopyUtils.objClone(skillVo, ResumeSkill::new);
        utilService.updateUserInfoToEntity(resumeSkill);
        skillMapper.updateById(resumeSkill);
        return getResumeSkillById(resumeSkill.getId());
    }

    @Override
    public void deleteResumeSkill(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        skillMapper.deleteById(id);
    }
}

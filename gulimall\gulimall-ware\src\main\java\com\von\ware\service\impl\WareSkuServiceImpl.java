package com.von.ware.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.von.common.constant.RabbitInfo;
import com.von.common.to.mq.OrderTo;
import com.von.common.to.mq.StockDetailTo;
import com.von.common.to.mq.StockLockedTo;
import com.von.common.utils.PageUtils;
import com.von.common.utils.Query;
import com.von.common.utils.R;
import com.von.common.vo.SkuHasStockVo;
import com.von.ware.dao.WareSkuDao;
import com.von.ware.entity.PurchaseDetailEntity;
import com.von.ware.entity.WareOrderTaskDetailEntity;
import com.von.ware.entity.WareOrderTaskEntity;
import com.von.ware.entity.WareSkuEntity;
import com.von.ware.enums.WareOrderTaskDetailLockStatus;
import com.von.ware.exception.NoStockException;
import com.von.ware.exception.OrderNoExistException;
import com.von.ware.exception.RemoteFailedException;
import com.von.ware.feign.OrderFeignService;
import com.von.ware.feign.ProductFeignService;
import com.von.ware.service.WareOrderTaskDetailService;
import com.von.ware.service.WareOrderTaskService;
import com.von.ware.service.WareSkuService;
import com.von.ware.to.WareSkuTo;
import com.von.ware.vo.OrderItemVo;
import com.von.ware.vo.OrderVo;
import com.von.ware.vo.SkuWareHasStockVo;
import com.von.ware.vo.WareSkuLockVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@Service("wareSkuService")
@RequiredArgsConstructor
@Slf4j
public class WareSkuServiceImpl extends ServiceImpl<WareSkuDao, WareSkuEntity> implements WareSkuService {

    private final WareOrderTaskService wareOrderTaskService;

    private final WareOrderTaskDetailService wareOrderTaskDetailService;

    private final ProductFeignService productFeignService;

    private final RabbitTemplate rabbitTemplate;

    private final OrderFeignService orderFeignService;

    /**
     * MQ库存自动解锁
     *
     * @param stockLockedTo
     */
    @Override
    public void unlockStock(StockLockedTo stockLockedTo) throws IOException {
        log.info("收到库存解锁消息");
        StockDetailTo stockDetailTo = stockLockedTo.getDetailTo();
        // 子表ID
        Long stockDetailId = stockDetailTo.getId();
        WareOrderTaskDetailEntity wareOrderTaskDetailEntity = this.wareOrderTaskDetailService.getById(stockDetailId);

        // 主表ID
        Long stockLockedToId = stockLockedTo.getId();
        // 查询订单号
        WareOrderTaskEntity wareOrderTaskEntity = this.wareOrderTaskService.getById(stockLockedToId);
        String orderSn = wareOrderTaskEntity.getOrderSn();
        // 根据订单号查询订单状态
        R getOrderByOrderSnR = this.orderFeignService.getOrderByOrderSn(orderSn);
        if (getOrderByOrderSnR.isOk()) {
            OrderVo orderVo = getOrderByOrderSnR.getData(new TypeReference<OrderVo>() {});
            if ((ObjectUtil.isNotNull(orderVo) || orderVo.getStatus() == 4)
                    && WareOrderTaskDetailLockStatus.LOCK.getCode().equals(wareOrderTaskDetailEntity.getLockStatus())) {
                // 库存解锁逻辑
                this.unlockStock(wareOrderTaskDetailEntity);
            }
        } else {
            // 远程查询库存失败, 抛出异常
            throw new RemoteFailedException();
        }

    }

    /**
     * 库存解锁逻辑
     * 库存接解锁和更新更做单状态
     *
     * @param wareOrderTaskDetailEntity
     */
    private void unlockStock(WareOrderTaskDetailEntity wareOrderTaskDetailEntity) {
        // 订单逻辑发生错误或者订单被去取消, 解锁库存
        this.baseMapper.unLockStock(wareOrderTaskDetailEntity.getSkuId(), wareOrderTaskDetailEntity.getWareId(), wareOrderTaskDetailEntity.getSkuNum());
        // 更新库存工作单状态
        WareOrderTaskDetailEntity taskDetailEntity = new WareOrderTaskDetailEntity();
        taskDetailEntity.setId(wareOrderTaskDetailEntity.getId());
        // 变为已解锁状态
        taskDetailEntity.setLockStatus(WareOrderTaskDetailLockStatus.UNLOCK.getCode());
        this.wareOrderTaskDetailService.updateById(taskDetailEntity);
    }

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<WareSkuEntity> wrapper = new QueryWrapper<>();
        String skuId = ObjectUtil.isNull(params.get("skuId")) ? "" : String.valueOf(params.get("skuId"));
        String wareId = ObjectUtil.isNull(params.get("wareId")) ? "" : String.valueOf(params.get("wareId"));
        if (StrUtil.isNotBlank(skuId)) {
            wrapper.eq("sku_id", skuId);
        }
        if (StrUtil.isNotBlank(wareId)) {
            wrapper.eq("ware_id", wareId);
        }

        IPage<WareSkuEntity> page = this.page(
                new Query<WareSkuEntity>().getPage(params),
                wrapper
        );

        return new PageUtils(page);
    }

    @Override
    public void addStock(List<PurchaseDetailEntity> finishDetailList) {
        List<WareSkuEntity> insertData = new ArrayList<>();
        finishDetailList.forEach(purchaseDetailEntity -> {
            WareSkuEntity wareSkuEntity = new WareSkuEntity();
            wareSkuEntity.setSkuId(purchaseDetailEntity.getSkuId());
            wareSkuEntity.setStock(purchaseDetailEntity.getSkuNum());
            wareSkuEntity.setWareId(purchaseDetailEntity.getWareId());
            if (isExist(purchaseDetailEntity)) {
                // 更新
                this.baseMapper.updateStock(wareSkuEntity);
            } else {
                insertData.add(wareSkuEntity);
            }
        });
        if (CollectionUtil.isNotEmpty(insertData)) {
            Set<Long> skuIdSet = insertData.stream()
                    .map(WareSkuEntity::getSkuId)
                    .collect(Collectors.toSet());
            // TODO 远程查询Sku戴尔名字, 如果失败, 整个事物无需回滚
            // 1. 自己catch异常
            // TODO 还可以用什么办法让异常出现以后不回滚
            try {
                R r = this.productFeignService.findListByIds(skuIdSet);
                Object dataObj = r.get("data");
                List<WareSkuTo> wareSkuToList = ObjectUtil.isNull(dataObj) ? new ArrayList<>() : (List) dataObj;
                Map<Long, WareSkuTo> wareSkuToMap = wareSkuToList.stream().collect(Collectors.toMap(WareSkuTo::getSkuId, wareSkuTo -> wareSkuTo));
                insertData.forEach(data -> {
                    WareSkuTo wareSkuTo = wareSkuToMap.get(data.getSkuId());
                    data.setSkuName(ObjectUtil.isNull(wareSkuTo) ? null : wareSkuTo.getSkuName());
                });
            } catch (Exception e) {

            }
            this.saveBatch(insertData);
        }
    }

    @Override
    public R test() {
        HashSet<Long> set = new HashSet<>();
        set.add(1L);
        set.add(2L);
        set.add(3L);

        return this.productFeignService.findListByIds(set);
    }

    /**
     * 查询是否存在库存
     *
     * TODO 这里可以批量查询后进行分组处理
     *  注意, SKu可以有多条数据, 所以查询完要用stream流进进行分组过滤
     *
     * @param skuIds
     * @return
     */
    @Override
    public List<SkuHasStockVo> getSkuHasStock(List<Long> skuIds) {
        if (CollectionUtil.isEmpty(skuIds)) {
            return null;
        }
        // TODO 发送多条sql语句, 不规范
        // TODO 可以只用一个map进行处理, 少一个循环调用
        List<SkuHasStockVo> skuHasStockVoList = skuIds.stream().filter(skuId -> {
            Long stock = this.baseMapper.getSkuHasStock(skuId);
            return ObjectUtil.isNotNull(stock) && stock > 0;
        }).map(skuId -> {
            SkuHasStockVo skuHasStockVo = new SkuHasStockVo();
            skuHasStockVo.setHasStock(true);
            skuHasStockVo.setSkuId(skuId);
            return skuHasStockVo;
        }).collect(Collectors.toList());

        return skuHasStockVoList;
    }

    /**
     * 订单库存锁定
     *
     * @param wareSkuLockVo
     * @return
     */
    @Override
    @Transactional
    public Boolean orderLockStock(WareSkuLockVo wareSkuLockVo) {
        // 保存库存工作单数据, 方便回滚
        WareOrderTaskEntity wareOrderTaskEntity = new WareOrderTaskEntity();
        wareOrderTaskEntity.setOrderSn(wareSkuLockVo.getOrderSn());
        this.wareOrderTaskService.save(wareOrderTaskEntity);

        List<OrderItemVo> orderItemVoList = wareSkuLockVo.getLocks();
        // TODO 这里可以做批量查询
        // 找到每个商品在哪一个仓库里面有库存, 构造数据
        List<SkuWareHasStockVo> skuWareHasStockVoList = orderItemVoList.stream().map(orderItemVo -> {
            SkuWareHasStockVo skuWareHasStockVo = new SkuWareHasStockVo();
            Long skuId = orderItemVo.getSkuId();
            skuWareHasStockVo.setSkuId(skuId);
            skuWareHasStockVo.setNum(orderItemVo.getCount());
            // 查找存在这个sku仓库id
            List<Long> wareIdList = this.baseMapper.listWareIdHasSkuStock(skuId);
            if (CollectionUtil.isEmpty(wareIdList)) {
                throw new NoStockException(skuId);
            }

            skuWareHasStockVo.setWareId(wareIdList);
            return skuWareHasStockVo;
        }).collect(Collectors.toList());
        // 库存锁定
        for (SkuWareHasStockVo skuWareHasStockVo : skuWareHasStockVoList) {
            Boolean skuLockResult = false;
            Long skuId = skuWareHasStockVo.getSkuId();
            for (Long wareId : skuWareHasStockVo.getWareId()) {
                // 成功就返回1, 否则就是0
                Long count = this.baseMapper.lockSkuStock(skuId, wareId, skuWareHasStockVo.getNum());
                if (count == 1) {
                    skuLockResult = true;
                    // 保存库存工作单详情, 并给MQ发送消息
                    WareOrderTaskDetailEntity wareOrderTaskDetailEntity = new WareOrderTaskDetailEntity();
                    wareOrderTaskDetailEntity.setSkuId(skuId);
                    wareOrderTaskDetailEntity.setSkuNum(skuWareHasStockVo.getNum());
                    wareOrderTaskDetailEntity.setTaskId(wareOrderTaskEntity.getId());
                    wareOrderTaskDetailEntity.setWareId(wareId);
                    // 设置锁定状态
                    wareOrderTaskDetailEntity.setLockStatus(WareOrderTaskDetailLockStatus.LOCK.getCode());
                    this.wareOrderTaskDetailService.save(wareOrderTaskDetailEntity);
                    // 发送库存锁定消息到延迟队列
                    // 要发送的内容
                    StockLockedTo stockLockedTo = new StockLockedTo();
                    stockLockedTo.setId(wareOrderTaskEntity.getId());
                    StockDetailTo detailTo = new StockDetailTo();
                    BeanUtils.copyProperties(wareOrderTaskDetailEntity, detailTo);
                    // 如果只发详情id，那么如果出现异常数据库回滚了 // 这个地方需要斟酌，都在事务里了，其实没必要
                    stockLockedTo.setDetailTo(detailTo);
                    this.rabbitTemplate.convertAndSend(RabbitInfo.Stock.exchange, RabbitInfo.Stock.delayRoutingKey, stockLockedTo);

                    break;
                }
            }
            if (!skuLockResult) {
                // 没有库存
                throw new NoStockException(skuId);
            }
        }
        return true;
    }

    /**
     * 库存解锁
     *
     * @param orderTo
     */
    @Override
    @Transactional
    public void unlockStock(OrderTo orderTo) {
        // 根据订单号查询主表数据
        String orderSn = orderTo.getOrderSn();
        WareOrderTaskEntity wareOrderTaskEntity = this.wareOrderTaskService.getByOrderSn(orderSn);
        if (ObjectUtil.isNull(wareOrderTaskEntity)) {
            throw new OrderNoExistException();
        }

        // 查询子表未解锁的库存
        QueryWrapper<WareOrderTaskDetailEntity> wareOrderTaskDetailEntityQueryWrapper = new QueryWrapper<WareOrderTaskDetailEntity>()
                .eq("task_id", wareOrderTaskEntity.getId())
                .eq("lock_status", WareOrderTaskDetailLockStatus.LOCK.getCode());
        List<WareOrderTaskDetailEntity> wareOrderTaskDetailEntityList = this.wareOrderTaskDetailService.list(wareOrderTaskDetailEntityQueryWrapper);
        for (WareOrderTaskDetailEntity wareOrderTaskDetailEntity : wareOrderTaskDetailEntityList) {
            // 库存解锁
            this.unlockStock(wareOrderTaskDetailEntity);
        }
    }


    /**
     * 判断库存存在
     *
     * @param purchaseDetailEntity
     * @return
     */
    private Boolean isExist(PurchaseDetailEntity purchaseDetailEntity) {
        List<WareSkuEntity> wareSkuEntityList = this.list(new QueryWrapper<WareSkuEntity>()
                .eq("sku_id", purchaseDetailEntity.getSkuId())
                .eq("ware_id", purchaseDetailEntity.getWareId())
        );
        return CollectionUtil.isNotEmpty(wareSkuEntityList);
    }

}
package com.get.financecenter.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum ShowModeEnum {
        // 显示对应科目的发生额
        CODE("Code", "显示对应科目的发生额"),
        // 显示对应科目及其展开的二级科目统计的发生额
        EXPAND("Expand", "显示对应科目及其展开的二级科目统计的发生额"),
        // 显示累计的发生额
        SUM("Sum", "显示累计的发生额");

        private String value;
        private String description;

        ShowModeEnum(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static String getDescription(String value) {
            for (ShowModeEnum mode : ShowModeEnum.values()) {
                if (mode.value.equals(value)) {
                    return mode.description;
                }
            }
            return null;
        }

    /**
     * 获取所有枚举值作为选择列表
     */
    public static List<Object> getOptions() {
        return Arrays.stream(values())
                .map(type ->{
                    ArrayList<Object> mode = new ArrayList<>();
                    mode.add(type.value);
                    mode.add(type.description);
                    return mode;
                }).collect(Collectors.toList());
    }

}

package com.get.reportcenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.vo.PaymentFormItemVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.reportcenter.vo.StudentOfferItemReportModel;
import com.get.reportcenter.entity.ReportStudentOfferItem;
import com.get.reportcenter.dto.ReportStudentOfferItemDto;
import com.get.salecenter.dto.StudentOfferItemDto;
import org.apache.ibatis.annotations.Param;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface StudentOfferItemMapper extends BaseMapper<ReportStudentOfferItem> {


    /**
     * 成功客户导出 基础信息
     *
     * @Date 21:45 2022/3/2
     * <AUTHOR>
     */
    List<StudentOfferItemReportModel> getStudentOfferItemDtoList(@Param("studentOfferItemDto") ReportStudentOfferItemDto studentOfferItemVo);

    /**
     * 成功客户导出 学习计划的收款信息
     *
     * @Date 21:45 2022/3/2
     * <AUTHOR>
     */
    List<ReceiptFormItemVo> getReceiptFormItemDtoListByOfferItem(@Param("studentOfferItemDto") StudentOfferItemDto studentOfferItemDto);

    /**
     * 成功客户导出 学习计划的付款信息
     *
     * @Date 21:45 2022/3/2
     * <AUTHOR>
     */
    List<PaymentFormItemVo> getPaymentFormItemDtoListByOfferItem(@Param("studentOfferItemDto") StudentOfferItemDto studentOfferItemDto);
}
package com.get.pmpcenter.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.pmpcenter.dto.agent.SaveAgentCommissionDto;
import com.get.pmpcenter.dto.agent.SavePlanAndCommissionDto;
import com.get.pmpcenter.dto.agent.UpdateAgentCommissionPlanDto;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.entity.*;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.enums.LogEventEnum;
import com.get.pmpcenter.enums.LogTableEnum;
import com.get.pmpcenter.enums.LogTypeEnum;
import com.get.pmpcenter.event.publisher.CommissionChangedEventPublisher;
import com.get.pmpcenter.mapper.*;
import com.get.pmpcenter.service.*;
import com.get.pmpcenter.utils.EntityCompareUtil;
import com.get.pmpcenter.utils.GeneratorLogsUtil;
import com.get.pmpcenter.utils.ListComparisonUtil;
import com.get.pmpcenter.utils.PercentageUtil;
import com.get.pmpcenter.vo.agent.AgentCommissionListVo;
import com.get.pmpcenter.vo.agent.AgentCommissionPlanVo;
import com.get.pmpcenter.vo.agent.ExtendAgentCommissionVo;
import com.get.pmpcenter.vo.common.CommissionMajorLevelVo;
import com.get.pmpcenter.vo.common.CountryVo;
import com.get.pmpcenter.vo.common.InstitutionVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionListVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionServiceImpl extends ServiceImpl<AgentCommissionMapper, AgentCommission> implements AgentCommissionService {

    @Autowired
    private InstitutionProviderCommissionService providerCommissionService;
    @Autowired
    private AgentCommissionPlanService commissionPlanService;
    @Autowired
    private AgentCommissionMapper agentCommissionMapper;
    @Autowired
    private AgentCommissionMajorLevelCustomService majorLevelCustomService;
    @Autowired
    private AgentCommissionPlanInstitutionService planInstitutionService;
    @Autowired
    private AgentCommissionPlanInstitutionMapper commissionPlanInstitutionMapper;
    @Autowired
    private InstitutionProviderCommissionPlanMapper providerCommissionPlanMapper;
    @Autowired
    private AgentCommissionMajorLevelCustomMapper agentCommissionMajorLevelCustomMapper;
    @Autowired
    private MajorLevelCustomMapper levelCustomMapper;
    @Autowired
    private CommissionChangedEventPublisher commissionChangedEventPublisher;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Autowired
    private AgentCommissionPlanCompanyMapper planCompanyMapper;
    @Autowired
    private InstitutionProviderCommissionMapper providerCommissionMapper;
    @Autowired
    private InstitutionProviderCommissionMajorLevelCustomService commissionMajorLevelCustomService;
    @Autowired
    private AgentCommissionTypeMapper agentCommissionTypeMapper;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionMapper providerCommissionPlanInstitutionMapper;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    private static final ObjectMapper objectMapper = new ObjectMapper();


    @Override
    public ExtendAgentCommissionVo getExtendAgentCommissionList(Integer type, Long id, Long companyId, Long agentCommissionTypeId) {
        if (type.equals(2)) {
            //模板明细
            ExtendAgentCommissionVo vo = new ExtendAgentCommissionVo();
            ProviderCommissionListVo providerCommissionList = providerCommissionService.getProviderCommissionList(id);
            AgentCommissionListVo list = convertProviderToAgent(providerCommissionList, type, id, companyId, agentCommissionTypeId);
            BeanCopyUtils.copyProperties(list, vo);
            InstitutionProviderCommissionPlan providerCommissionPlan = providerCommissionPlanMapper.selectById(id);
            if (Objects.nonNull(providerCommissionPlan)) {
                List<InstitutionVo> agentInstitutionList = commissionPlanService.getAgentInstitutionList(type, providerCommissionPlan.getFkInstitutionProviderId(), id);
                vo.setInstitutionList(agentInstitutionList);
                vo.setInstitutionIds(agentInstitutionList.stream().map(InstitutionVo::getInstitutionId).distinct().collect(Collectors.toList()));
            }
            //方案基本信息
            UpdateAgentCommissionPlanDto planDto = new UpdateAgentCommissionPlanDto();
            BeanCopyUtils.copyProperties(providerCommissionPlan, planDto);
            planDto.setFkInstitutionProviderCommissionPlanId(providerCommissionPlan.getId());
            planDto.setIsGobal(1);
            planDto.setIsActive(1);
            planDto.setId(null);
//            vo.setAgentCombinationList(new ArrayList<>());
            vo.setAgentBonusList(new ArrayList<>());
            vo.setUpdateAgentCommissionPlanDto(planDto);
            return vo;
        }
        ExtendAgentCommissionVo detail = getAgentCommissionDetail(id, true, false, agentCommissionTypeId);
        return initAgentCommissionList(detail);
    }

    @Override
    public ExtendAgentCommissionVo getAgentCommissionDetail(Long id, Boolean initCommission, Boolean checkPermission, Long agentCommissionTypeId) {
        AgentCommissionPlan plan = commissionPlanService.getById(id);
        if (Objects.isNull(plan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        ExtendAgentCommissionVo result = new ExtendAgentCommissionVo();

        UpdateAgentCommissionPlanDto planDto = new UpdateAgentCommissionPlanDto();
        BeanCopyUtils.copyProperties(plan, planDto);
        planDto.setIsGobal(0);
        planDto.setIsActive(1);
        planDto.setId(null);
        result.setUpdateAgentCommissionPlanDto(planDto);

        List<Long> institutionIds = commissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                        .eq(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, id))
                .stream().map(AgentCommissionPlanInstitution::getFkInstitutionId).collect(Collectors.toList());

        //回显比例
        String feedbackRate = getRate(1, SecureUtil.getFkCompanyId(), plan.getFkInstitutionProviderCommissionPlanId());
        if (initCommission && Objects.nonNull(agentCommissionTypeId) && agentCommissionTypeId > 0) {
            AgentCommissionType agentCommissionType = agentCommissionTypeMapper.selectById(agentCommissionTypeId);
            if (Objects.nonNull(agentCommissionType) && StringUtils.isNotBlank(agentCommissionType.getCommissionRateDefault())) {
//                feedbackRate = agentCommissionType.getCommissionRate().toString();
                feedbackRate = getCountryRate(agentCommissionType.getCommissionRateDefault(), plan.getFkInstitutionProviderCommissionPlanId());
            }
        }
        //学校列表
        List<InstitutionVo> agentInstitutionList = commissionPlanService.getAgentInstitutionList(1, plan.getFkInstitutionProviderId(), plan.getId());
        result.setInstitutionList(agentInstitutionList);
        result.setInstitutionIds(institutionIds);
        //单项佣金方案--有修改标记的佣金方案需要同步
//        Boolean syncProviderCommission = plan.getIsInstitutionProviderCommissionModify().equals(1) ? Boolean.TRUE : Boolean.FALSE;
        result.setAgentCommissionInfoList(getAgentCommissionList(id, initCommission, feedbackRate,
                plan.getFkInstitutionProviderCommissionPlanId(), Boolean.TRUE,
                plan.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode()) ? true : false));
        //组合佣金方案
        result.setAgentCombinationList(getCombinationList(id, initCommission, feedbackRate));
        //bonus佣金方案
        result.setAgentBonusList(getBonusInfoList(id, initCommission, feedbackRate));

        //查询方案锁定和审核权限
        if (checkPermission) {
            AgentCommissionPlanVo agentCommissionPlanVo = new AgentCommissionPlanVo();
            BeanCopyUtils.copyProperties(plan, agentCommissionPlanVo);
            AgentCommissionPlanVo planPermission = commissionPlanService.checkAgentCommissionPlanPermission(agentCommissionPlanVo);
            result.setLockPermission(planPermission.getLockPermission());
            result.setApprovalPermission(planPermission.getApprovalPermission());
            result.setIsLocked(Objects.nonNull(plan.getIsLocked()) ? plan.getIsLocked() : 0);
            result.setApprovalStatus(Objects.nonNull(plan.getApprovalStatus()) ? plan.getApprovalStatus() : ApprovalStatusEnum.UN_COMMITTED.getCode());
        }
        //方案下的学校是否有多个方案
        result.setHasManyPlans(commissionPlanService.hasManyPlans(id, institutionIds));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAgentCommission(SavePlanAndCommissionDto agentCommissionDto) {
        UserInfo user = SecureUtil.getUser();
        //如果学校有绑定了,要提示是否要解绑另外一个
        List<Long> institutionIds = agentCommissionDto.getInstitutionIds();
        if (CollectionUtils.isEmpty(institutionIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SCHOOL_REQUIRED", "学校不能为空,请先在合同端完善学校信息"));

        }
        UpdateAgentCommissionPlanDto planDto = agentCommissionDto.getUpdateAgentCommissionPlanDto();
        planDto.setId(null);
        planDto.setIsMain(1);
        //如果没有代理类型的,IsGobal就是等于1,说明是模板,如果有代理类型的,就是等于0,说明是有代理等级的方案
        planDto.setIsGobal(1);
        if (Objects.nonNull(planDto.getFkAgentCommissionTypeId()) && planDto.getFkAgentCommissionTypeId() > 0) {
            planDto.setIsGobal(0);
        }
        if (agentCommissionDto.getType().equals(2)) {
            //判断学校合同商那边有没有在保存前删掉一些单项佣金，若有则要剔除已经删掉的那些
            List<AgentCommissionListVo.AgentCommissionInfo> commissionInfoList = agentCommissionDto.getAgentCommissionInfoList();
            List<ProviderCommissionListVo.CommissionInfo> providerCommissionList = providerCommissionService.getCommissionList(agentCommissionDto.getId());
            if (CollectionUtils.isNotEmpty(commissionInfoList) && CollectionUtils.isNotEmpty(providerCommissionList)) {
                List<Long> providerCommissionLevelIds = providerCommissionList.stream().map(ProviderCommissionListVo.CommissionInfo::getLevelId).collect(Collectors.toList());
                List<Long> agentCommissionLevelIds = commissionInfoList.stream().map(AgentCommissionListVo.AgentCommissionInfo::getLevelId).collect(Collectors.toList());
                if (providerCommissionLevelIds.size() < agentCommissionLevelIds.size()) {
                    List<Long> removeIds = agentCommissionLevelIds.stream()
                            .filter(id -> !providerCommissionLevelIds.contains(id)).collect(Collectors.toList());
                    commissionInfoList.removeIf(item -> removeIds.contains(item.getLevelId()));
                }
            }
            planDto.setFkInstitutionProviderCommissionPlanId(agentCommissionDto.getId());
        } else {
            AgentCommissionPlan templatePlan = commissionPlanService.getById(agentCommissionDto.getId());
            if (Objects.nonNull(templatePlan)) {
                planDto.setFkInstitutionProviderCommissionPlanId(templatePlan.getFkInstitutionProviderCommissionPlanId());
            }
        }
        //有代理类型的情况下，学校方案模板只能被一种类型继承，不能多种
        if (Objects.nonNull(agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId())) {
            Set<Long> typeSet = commissionPlanService.getBaseMapper().selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                            .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, planDto.getFkInstitutionProviderCommissionPlanId())
                            .eq(AgentCommissionPlan::getIsGobal, 0)
                            .isNotNull(AgentCommissionPlan::getFkAgentCommissionTypeId)
                            .exists(Objects.nonNull(agentCommissionDto.getUpdateAgentCommissionPlanDto().getCompanyId()),
                                    "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id " +
                                            "= m_agent_commission_plan.id and fk_company_id = " + agentCommissionDto.getUpdateAgentCommissionPlanDto().getCompanyId()))
                    .stream().map(AgentCommissionPlan::getFkAgentCommissionTypeId).collect(Collectors.toSet());
            if (typeSet.contains(agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId())) {
//                throw new GetServiceException("已存在该类型的佣金方案，请勿重复添加");
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_DUPLICATE_PLAN", "已存在该类型的佣金方案，请勿重复添加"));
            }
        }
        List<AgentCommissionPlan> unBindPlans = new ArrayList<>();
//        List<AgentCommissionPlanInstitution> currentPlanInstitutions = commissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
//                .in(AgentCommissionPlanInstitution::getFkInstitutionId, institutionIds));
//        if (CollectionUtils.isNotEmpty(currentPlanInstitutions)) {
//            if (Objects.isNull(agentCommissionDto.getUnbind())) {
//                agentCommissionDto.setUnbind(Boolean.FALSE);
//            }
//            List<Long> currentPlanIds = currentPlanInstitutions.stream().map(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId).distinct().collect(Collectors.toList());
//            unBindPlans = commissionPlanService.getBaseMapper().selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
//                    .eq(AgentCommissionPlan::getIsMain, 1)
//                    .eq(Objects.nonNull(agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId()),
//                            AgentCommissionPlan::getFkAgentCommissionTypeId, agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId())
//                    .eq(Objects.nonNull(agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId()) && agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId() > 0, AgentCommissionPlan::getIsGobal, 0)
//                    .eq(Objects.nonNull(agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId()) && agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId() > 0, AgentCommissionPlan::getFkAgentCommissionTypeId, agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId())
//                    .eq(Objects.isNull(agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId()) || agentCommissionDto.getUpdateAgentCommissionPlanDto().getFkAgentCommissionTypeId() < 1, AgentCommissionPlan::getIsGobal, 1)
//                    .exists(Objects.nonNull(agentCommissionDto.getUpdateAgentCommissionPlanDto().getCompanyId()),
//                            "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id = m_agent_commission_plan.id and fk_company_id = " + agentCommissionDto.getUpdateAgentCommissionPlanDto().getCompanyId())
//                    .in(AgentCommissionPlan::getId, currentPlanIds));
//            if (CollectionUtils.isNotEmpty(unBindPlans) && !agentCommissionDto.getUnbind()) {
//                return 0L;
//            }
//        }
        //保存佣金方案
        Long planId = commissionPlanService.updateAgentCommissionPlan(planDto);
        //保存佣金明细-单项
        saveCommissionList(agentCommissionDto.getAgentCommissionInfoList(), planId, user.getLoginId());
        //保存佣金明细-组合
        saveCombinationList(agentCommissionDto.getAgentCombinationList(), planId, user.getLoginId());
        //保存佣金明细-bonus
        saveBonusInfoList(agentCommissionDto.getAgentBonusList(), planId, user.getLoginId());
        //保存学校
        planInstitutionService.saveAgentCommissionPlanInstitution(agentCommissionDto.getInstitutionIds(), planId);
        LogDto logDto = GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT_PLAN, LogTableEnum.AGENT_PLAN,
                planId, LogEventEnum.ADD_PLAN, user.getLoginId(), "", null);
        commissionChangedEventPublisher.publishCommissionChangedEvent(Arrays.asList(logDto));

        if (CollectionUtils.isNotEmpty(unBindPlans) && agentCommissionDto.getUnbind()) {
            //解除其他的绑定
            unBindPlans.stream().forEach(agentCommissionPlan -> {
                agentCommissionPlan.setIsMain(0);
                agentCommissionPlan.setGmtModified(new Date());
                agentCommissionPlan.setGmtModifiedUser(SecureUtil.getLoginId());
            });
            commissionPlanService.updateBatchById(unBindPlans);
        }
        return planId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long updateAgentCommission(SaveAgentCommissionDto agentCommissionDto) {
        AgentCommissionPlan plan = commissionPlanService.getById(agentCommissionDto.getAgentCommissionPlanId());
        if (Objects.isNull(plan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        if (CollectionUtils.isEmpty(agentCommissionDto.getInstitutionIds())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SCHOOL_REQUIRED", "学校不能为空,请先在合同端完善学校信息"));
        }
        List<AgentCommissionPlan> unBindPlans = new ArrayList<>();
//        List<AgentCommissionPlanInstitution> currentPlanInstitutions = commissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
//                .in(AgentCommissionPlanInstitution::getFkInstitutionId, agentCommissionDto.getInstitutionIds()));
//        if (CollectionUtils.isNotEmpty(currentPlanInstitutions)) {
//            if (Objects.isNull(agentCommissionDto.getUnbind())) {
//                agentCommissionDto.setUnbind(Boolean.FALSE);
//            }
//            List<Long> currentPlanIds = currentPlanInstitutions.stream().map(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId).distinct().collect(Collectors.toList());
//            List<AgentCommissionPlanCompany> planCompanies = planCompanyMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanCompany>()
//                    .eq(AgentCommissionPlanCompany::getFkAgentCommissionPlanId, agentCommissionDto.getAgentCommissionPlanId())
//                    .orderByDesc(AgentCommissionPlanCompany::getId)
//            );
//
//            //如果当前方案有代理等级,判断IsGobal=0,如果没有,判断IsGobal=1
//            unBindPlans = commissionPlanService.getBaseMapper().selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
//                    .eq(AgentCommissionPlan::getIsMain, 1)
//                    .ne(AgentCommissionPlan::getId, agentCommissionDto.getAgentCommissionPlanId())
//                    .eq(Objects.nonNull(plan.getFkAgentCommissionTypeId()) && plan.getFkAgentCommissionTypeId() > 0, AgentCommissionPlan::getIsGobal, 0)
//                    .eq(Objects.nonNull(plan.getFkAgentCommissionTypeId()) && plan.getFkAgentCommissionTypeId() > 0, AgentCommissionPlan::getFkAgentCommissionTypeId, plan.getFkAgentCommissionTypeId())
//                    .eq(Objects.isNull(plan.getFkAgentCommissionTypeId()) || plan.getFkAgentCommissionTypeId() < 1, AgentCommissionPlan::getIsGobal, 1)
//                    .exists(CollectionUtils.isNotEmpty(planCompanies),
//                            "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id = m_agent_commission_plan.id " +
//                                    "and fk_company_id = " + planCompanies.get(0).getFkCompanyId())
//                    .in(AgentCommissionPlan::getId, currentPlanIds));
//            if (Objects.isNull(agentCommissionDto.getUnbind())) {
//                agentCommissionDto.setUnbind(Boolean.FALSE);
//            }
//            if (CollectionUtils.isNotEmpty(unBindPlans) && !agentCommissionDto.getUnbind()) {
//                return 0L;
//            }
//        }
        List<LogDto> logs = new ArrayList<>();
        UserInfo user = SecureUtil.getUser();
        Long planId = agentCommissionDto.getAgentCommissionPlanId();
        //保存佣金明细-单项
        List<LogDto> commissionLogs = saveCommissionList(agentCommissionDto.getAgentCommissionInfoList(), planId, user.getLoginId());
        //保存佣金明细-组合
        List<LogDto> combinationLogs = saveCombinationList(agentCommissionDto.getAgentCombinationList(), planId, user.getLoginId());
        //保存佣金明细-bonus
        List<LogDto> bonusLogs = saveBonusInfoList(agentCommissionDto.getAgentBonusList(), planId, user.getLoginId());
        //保存学校
        List<LogDto> institutionLogs = planInstitutionService.saveAgentCommissionPlanInstitution(agentCommissionDto.getInstitutionIds(), planId);
        logs.addAll(institutionLogs);
        logs.addAll(commissionLogs);
        logs.addAll(combinationLogs);
        logs.addAll(bonusLogs);
        commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
        if (CollectionUtils.isNotEmpty(unBindPlans) && agentCommissionDto.getUnbind()) {
            //解除其他的绑定
            unBindPlans.stream().forEach(agentCommissionPlan -> {
                agentCommissionPlan.setIsMain(0);
                agentCommissionPlan.setGmtModified(new Date());
                agentCommissionPlan.setGmtModifiedUser(SecureUtil.getLoginId());
            });
            commissionPlanService.updateBatchById(unBindPlans);
            if (Objects.nonNull(plan) && plan.getIsMain().equals(0)) {
                plan.setGmtModified(new Date());
                plan.setGmtModifiedUser(SecureUtil.getLoginId());
                plan.setIsMain(1);
                commissionPlanService.updateById(plan);
            }
        }

        //修改佣金方案审核状态
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        plan.setIsInstitutionProviderCommissionModify(0);
        //如果方案处于审核中,自动上锁,并且方案状态保持不变
        if (plan.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            plan.setIsLocked(1);
        } else {
            //如果处于非审核中,保存后就变回未提交状态
            plan.setApprovalStatus(ApprovalStatusEnum.UN_COMMITTED.getCode());
        }
        commissionPlanService.updateById(plan);
        return planId;
    }


    private AgentCommissionListVo convertProviderToAgent(ProviderCommissionListVo providerVo, Integer type, Long id, Long companyId, Long agentCommissionTypeId) {
        AgentCommissionListVo agentVo = new AgentCommissionListVo();
        //回显比例
        String feedbackRate = getRate(1, companyId, id);
        //保存最大比例
        String saveRate = getRate(2, companyId, id);
        //判断代理等级回显佣金比例
        if (Objects.nonNull(agentCommissionTypeId) && agentCommissionTypeId > 0) {
            AgentCommissionType agentCommissionType = agentCommissionTypeMapper.selectById(agentCommissionTypeId);
            if (Objects.nonNull(agentCommissionType) && Objects.nonNull(agentCommissionType.getCommissionRateDefault())) {
//                feedbackRate = agentCommissionType.getCommissionRate().toString();
                feedbackRate = getCountryRate(agentCommissionType.getCommissionRateDefault(), id);
            }
        }
        // 转换嵌套的列表字段（commissionList, combinationList, bonusList）
        List<AgentCommissionListVo.AgentCommissionInfo> agentCommissionInfoList = createAgentCommissionInfoList(providerVo.getCommissionList(), feedbackRate, saveRate);
        agentVo.setAgentCommissionInfoList(agentCommissionInfoList);

        String finalFeedbackRate1 = feedbackRate;
        List<AgentCommissionListVo.AgentCombinationInfo> agentCombinationList = providerVo.getCombinationList().stream()
                .map(combination -> {
                    AgentCommissionListVo.AgentCombinationInfo agentCombinationInfo = new AgentCommissionListVo.AgentCombinationInfo();
                    BeanCopyUtils.copyProperties(combination, agentCombinationInfo);
                    agentCombinationInfo.setPackageNameNative(combination.getPackageNameChn());
                    agentCombinationInfo.setPackageKey(Strings.EMPTY);
                    List<AgentCommissionListVo.AgentCommissionInfo> commissionInfoList = createAgentCommissionInfoList(combination.getCommissionInfoList(), finalFeedbackRate1, saveRate);
                    agentCombinationInfo.setAgentCommissionInfos(commissionInfoList);
                    return agentCombinationInfo;
                })
                .collect(Collectors.toList());
        agentVo.setAgentCombinationList(agentCombinationList);

        String finalFeedbackRate = feedbackRate;
        List<AgentCommissionListVo.AgentBonusInfo> agentBonusList = providerVo.getBonusList().stream()
                .map(bonus -> {
                    AgentCommissionListVo.AgentBonusInfo agentBonusInfo = new AgentCommissionListVo.AgentBonusInfo();
                    BeanCopyUtils.copyProperties(bonus, agentBonusInfo);
                    agentBonusInfo.setRemarkNoteNative(bonus.getRemarkNoteChn());
                    agentBonusInfo.setFollowRemarkNoteNative(bonus.getFollowRemarkNoteChn());
                    agentBonusInfo.setTitleNative(bonus.getTitleChn());
                    agentBonusInfo.setFkAgentCommissionPlanId(type.equals(2) ? 0 : id);
                    agentBonusInfo.setId(null);
                    agentBonusInfo.setFollowCommission(PercentageUtil.calculatePercentage(finalFeedbackRate, bonus.getFollowCommission()));
                    agentBonusInfo.setCommission(PercentageUtil.calculatePercentage(finalFeedbackRate, bonus.getCommission()));
                    agentBonusInfo.setOriginalCommission(bonus.getCommission());
                    agentBonusInfo.setOriginalFollowCommission(bonus.getFollowCommission());
                    agentBonusInfo.setFkInstitutionProviderCommissionId(bonus.getId());
                    agentBonusInfo.setOriginalCommissionUnit(bonus.getCommissionUnit());
                    agentBonusInfo.setOriginalFollowCommissionUnit(bonus.getFollowCommissionUnit());
                    return agentBonusInfo;
                })
                .collect(Collectors.toList());
        agentVo.setAgentBonusList(agentBonusList);
        agentVo.setInstitutionIds(providerVo.getInstitutionIds());
        return agentVo;
    }

    public static List<AgentCommissionListVo.AgentCommissionInfo> createAgentCommissionInfoList(List<ProviderCommissionListVo.CommissionInfo> commissionInfoList, String feedbackRate, String saveRate) {
        List<AgentCommissionListVo.AgentCommissionInfo> list = commissionInfoList.stream()
                .map(commission -> {
                    AgentCommissionListVo.AgentCommissionInfo agentCommissionInfo = new AgentCommissionListVo.AgentCommissionInfo();
                    BeanCopyUtils.copyProperties(commission, agentCommissionInfo);
                    agentCommissionInfo.setRemarkNoteNative(commission.getRemarkNoteChn());
                    agentCommissionInfo.setTitleNative(commission.getTitleChn());
                    agentCommissionInfo.setId(null);
                    agentCommissionInfo.setCommission(PercentageUtil.calculatePercentage(feedbackRate, commission.getCommission()));
                    agentCommissionInfo.setFollowCommission(PercentageUtil.calculatePercentage(feedbackRate, commission.getFollowCommission()));
                    agentCommissionInfo.setOriginalCommission(commission.getCommission());
                    agentCommissionInfo.setOriginalFollowCommission(commission.getFollowCommission());
                    agentCommissionInfo.setOriginalCommissionUnit(commission.getCommissionUnit());
                    agentCommissionInfo.setOriginalFollowCommissionUnit(commission.getFollowCommissionUnit());
                    agentCommissionInfo.setFkInstitutionProviderCommissionId(commission.getId());
                    agentCommissionInfo.setFollowRemarkNoteNative(commission.getFollowRemarkNote());
                    agentCommissionInfo.setSaveRate(saveRate);
                    agentCommissionInfo.setCommissionOverLimit(false);
                    agentCommissionInfo.setFollowCommissionOverLimit(false);
                    //判断当前的佣金是否超过限制
                    BigDecimal commissionMaximum = PercentageUtil.calculatePercentage(saveRate, commission.getCommission());
                    if (Objects.nonNull(commissionMaximum)
                            && Objects.nonNull(agentCommissionInfo.getCommission())
                            && commissionMaximum.compareTo(agentCommissionInfo.getCommission()) < 0) {
                        agentCommissionInfo.setCommissionOverLimit(true);
                    }
                    BigDecimal followCommissionMaximum = PercentageUtil.calculatePercentage(saveRate, commission.getFollowCommission());
                    if (Objects.nonNull(followCommissionMaximum)
                            && Objects.nonNull(agentCommissionInfo.getFollowCommission())
                            && followCommissionMaximum.compareTo(agentCommissionInfo.getFollowCommission()) < 0) {
                        agentCommissionInfo.setFollowCommissionOverLimit(true);
                    }
                    return agentCommissionInfo;
                })
                .collect(Collectors.toList());
        return list;
    }

    private List<LogDto> saveCommissionList(List<AgentCommissionListVo.AgentCommissionInfo> agentCommissionInfoList, Long planId, String loginId) {
        //如果全部都是下架，不能保存
        if (agentCommissionInfoList.stream().allMatch(commissionInfo -> commissionInfo.getIsActive().equals(0))) {
//            throw new GetServiceException("请先上架至少一个课程");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_COURSE_REQUIRED", "请先上架至少一个课程"));
        }
//        String rate = getRate(2, SecureUtil.getFkCompanyId());
        List<LogDto> logs = new ArrayList<>();
        List<AgentCommissionMajorLevelCustom> agentCommissionMajorLevelCustoms = new ArrayList<>();
        List<Long> currentIdList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getCommissionType, 1)).stream().map(AgentCommission::getId).collect(Collectors.toList());

        //获取原始佣金-学校提供商，用作后续对比
//        Map<Long, InstitutionProviderCommission> providerCommissionMap = new HashMap<>();
//        List<Long> providerCommissionIds = agentCommissionInfoList.stream().map(AgentCommissionListVo.AgentCommissionInfo::getFkInstitutionProviderCommissionId).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(providerCommissionIds)) {
//            providerCommissionMap = providerCommissionService.list(new LambdaQueryWrapper<InstitutionProviderCommission>()
//                            .in(InstitutionProviderCommission::getId, providerCommissionIds))
//                    .stream()
//                    .collect(Collectors.toMap(InstitutionProviderCommission::getId, item -> item));
//        }

        //编辑的
        List<AgentCommissionListVo.AgentCommissionInfo> updateList = agentCommissionInfoList.stream()
                .filter(commissionInfo -> Objects.nonNull(commissionInfo.getId()) && commissionInfo.getId() > 0)
                .collect(Collectors.toList());

        //新增的
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<Long> updateIds = updateList.stream()
                    .map(AgentCommissionListVo.AgentCommissionInfo::getId)
                    .collect(Collectors.toList());

            agentCommissionInfoList = agentCommissionInfoList.stream()
                    .filter(commissionInfo -> Objects.isNull(commissionInfo.getId())
                            || !updateIds.contains(commissionInfo.getId()) || commissionInfo.getId().equals(0L))
                    .collect(Collectors.toList());
        }
        //删除的
        List<Long> updateIds = updateList.stream().map(AgentCommissionListVo.AgentCommissionInfo::getId).distinct().collect(Collectors.toList());
        currentIdList.removeAll(updateIds);


//        Map<Long, InstitutionProviderCommission> finalProviderCommissionMap = providerCommissionMap;
        List<AgentCommission> updateCommissions = updateList.stream().map(commissionInfo -> {
            AgentCommission commission = agentCommissionMapper.selectById(commissionInfo.getId());
            if (Objects.isNull(commission)) {
                return null;
            }

            //判断金额
//            Boolean checked = checkCommissionAmount(commissionInfo, finalProviderCommissionMap, rate);
//            if (!checked) {
//                throw new GetServiceException(commissionInfo.getTitle() + "佣金比例超出范围,不能超过原始的" + rate + "%,请重新设置");
//            }
            AgentCommission original = new AgentCommission();
            BeanCopyUtils.copyProperties(commission, original);
            BeanCopyUtils.copyProperties(commissionInfo, commission);

            commission.setTitle(commissionInfo.getCustomName());
            commission.setTitleNative(commissionInfo.getCustomNameChn());
            commission.setGmtModified(new Date());
            commission.setGmtModifiedUser(loginId);
            commission.setCommissionType(1);
            commission.setIsActive(commissionInfo.getIsActive());

            boolean change = EntityCompareUtil.compareFields(original, commission);
            if (change) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.AGENT_PLAN,
                        planId, LogEventEnum.UPDATE_COMMISSION, loginId,
                        commission.getTitle(), null));
            }
            return commission;
        }).collect(Collectors.toList());
        updateCommissions.stream().filter(Objects::nonNull);
        if (CollectionUtils.isNotEmpty(updateCommissions)) {
            this.updateBatchById(updateCommissions);
        }
        List<AgentCommission> insertCommissions = agentCommissionInfoList.stream().map(commissionInfo -> {

            //判断金额
//            Boolean checked = checkCommissionAmount(commissionInfo, finalProviderCommissionMap, rate);
//            if (!checked) {
//                throw new GetServiceException(commissionInfo.getTitle() + "佣金比例超出范围,不能超过原始的" + rate + "%,请重新设置");
//            }

            AgentCommission commission = new AgentCommission();
            BeanCopyUtils.copyProperties(commissionInfo, commission);
            commission.setTitle(commissionInfo.getCustomName());
            commission.setTitleNative(commissionInfo.getCustomNameChn());
            commission.setGmtModified(new Date());
            commission.setGmtModifiedUser(loginId);
            commission.setGmtCreate(new Date());
            commission.setGmtCreateUser(loginId);
            commission.setCommissionType(1);
            commission.setFkAgentCommissionPlanId(planId);
            commission.setIsActive(commissionInfo.getIsActive());
            return commission;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertCommissions)) {
            this.saveBatch(insertCommissions);
            //记录日志
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.AGENT_PLAN,
                    planId, LogEventEnum.ADD_COMMISSION, loginId,
                    insertCommissions.size(), null));
            for (int i = 0; i < insertCommissions.size(); i++) {
                AgentCommissionListVo.AgentCommissionInfo agentCommissionInfo = agentCommissionInfoList.get(i);
                AgentCommission commission = insertCommissions.get(i);
                AgentCommissionMajorLevelCustom relation = new AgentCommissionMajorLevelCustom();
                relation.setFkAgentCommissionId(commission.getId());
                relation.setFkMajorLevelCustomId(agentCommissionInfo.getLevelId());
                relation.setGmtModified(new Date());
                relation.setGmtModifiedUser(loginId);
                relation.setGmtCreate(new Date());
                relation.setGmtCreateUser(loginId);
                agentCommissionMajorLevelCustoms.add(relation);
            }
            majorLevelCustomService.saveBatch(agentCommissionMajorLevelCustoms);
        }
        if (CollectionUtils.isNotEmpty(currentIdList)) {
            this.removeByIds(currentIdList);
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.AGENT_PLAN,
                    planId, LogEventEnum.DEL_COMMISSION, loginId,
                    currentIdList.size(), null));
            majorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                    .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, currentIdList));
        }
        return logs;
    }

    private List<LogDto> saveCombinationList(List<AgentCommissionListVo.AgentCombinationInfo> combinationList, Long planId, String loginId) {
        List<LogDto> logs = new ArrayList<>();
        //删除全部
        if (CollectionUtils.isEmpty(combinationList)) {
            List<AgentCommission> agentCommissions = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                    .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                    .eq(AgentCommission::getCommissionType, 3));
            if (CollectionUtils.isNotEmpty(agentCommissions)) {
                List<String> packages = agentCommissions.stream().map(AgentCommission::getPackageKey).distinct().collect(Collectors.toList());
                List<Long> delIds = agentCommissions.stream().map(AgentCommission::getId).collect(Collectors.toList());
                this.removeByIds(delIds);
                majorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                        .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, delIds));
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.AGENT_PLAN,
                        planId, LogEventEnum.DEL_COMBINATION_COMMISSION, loginId,
                        packages.size(), null));
            }
            return logs;
        }
        List<String> newPackageKeys = new ArrayList<>();
        List<AgentCommissionMajorLevelCustom> majorLevelCustoms = new ArrayList<>();
        List<AgentCommissionListVo.AgentCombinationInfo> newCombinationList = combinationList.stream()
                .filter(combinationInfo -> StringUtils.isBlank(combinationInfo.getPackageKey())).collect(Collectors.toList());
        //新增
        for (AgentCommissionListVo.AgentCombinationInfo combinationInfo : newCombinationList) {
            List<AgentCommissionListVo.AgentCommissionInfo> commissionList = combinationInfo.getAgentCommissionInfos();
            String packageKey = UuidUtils.generateUuid() + RandomUtil.randomString(5);
            List<AgentCommission> insertList = commissionList.stream().map(commissionInfo -> {
                AgentCommission commission = new AgentCommission();
                BeanCopyUtils.copyProperties(commissionInfo, commission);
                commission.setTitle(commissionInfo.getCustomName());
                commission.setTitleNative(commissionInfo.getCustomNameChn());
                commission.setGmtModified(new Date());
                commission.setGmtModifiedUser(loginId);
                commission.setGmtCreate(new Date());
                commission.setGmtCreateUser(loginId);
                commission.setCommissionType(3);
                commission.setPackageKey(packageKey);
                commission.setFkAgentCommissionPlanId(planId);
                commission.setPackageNameNative(combinationInfo.getPackageNameNative());
                commission.setPackageName(combinationInfo.getPackageName());
                return commission;
            }).collect(Collectors.toList());
            newPackageKeys.add(packageKey);
            this.saveBatch(insertList);
            for (int i = 0; i < insertList.size(); i++) {
                AgentCommissionListVo.AgentCommissionInfo commissionInfo = commissionList.get(i);
                AgentCommission commission = insertList.get(i);
                AgentCommissionMajorLevelCustom relation = new AgentCommissionMajorLevelCustom();
                relation.setFkAgentCommissionId(commission.getId());
                relation.setFkMajorLevelCustomId(commissionInfo.getLevelId());
                relation.setGmtModified(new Date());
                relation.setGmtModifiedUser(loginId);
                relation.setGmtCreate(new Date());
                relation.setGmtCreateUser(loginId);
                majorLevelCustoms.add(relation);
            }
        }

        if (CollectionUtils.isNotEmpty(newPackageKeys)) {
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.AGENT_PLAN,
                    planId, LogEventEnum.ADD_COMBINATION_COMMISSION, loginId,
                    newPackageKeys.size(), null));
        }

        //编辑
        List<AgentCommissionListVo.AgentCombinationInfo> updateCombinationList = combinationList.stream()
                .filter(combinationInfo -> StringUtils.isNotBlank(combinationInfo.getPackageKey())).collect(Collectors.toList());

        for (AgentCommissionListVo.AgentCombinationInfo combinationInfo : updateCombinationList) {
            AtomicReference<Boolean> change = new AtomicReference<>(false);
            List<Long> newIds = new ArrayList<>();
            List<AgentCommissionListVo.AgentCommissionInfo> commissionList = combinationInfo.getAgentCommissionInfos();
            //找出没有id的,就是新增的
            List<AgentCommission> insertList = commissionList.stream()
                    .filter(commissionInfo -> Objects.isNull(commissionInfo.getId()) || commissionInfo.getId() < 1)
                    .map(commissionInfo -> {
                        AgentCommission commission = new AgentCommission();
                        BeanCopyUtils.copyProperties(commissionInfo, commission);
                        commission.setTitle(commissionInfo.getCustomName());
                        commission.setTitleNative(commissionInfo.getCustomNameChn());
                        commission.setGmtModified(new Date());
                        commission.setGmtModifiedUser(loginId);
                        commission.setGmtCreate(new Date());
                        commission.setGmtCreateUser(loginId);
                        commission.setCommissionType(3);
                        commission.setPackageKey(combinationInfo.getPackageKey());
                        commission.setFkAgentCommissionPlanId(planId);
                        commission.setPackageNameNative(combinationInfo.getPackageNameNative());
                        commission.setPackageName(combinationInfo.getPackageName());
                        if (Objects.isNull(commissionInfo.getFkAgentCommissionPlanId())) {
                            commission.setFkAgentCommissionPlanId(planId);
                        }
                        return commission;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(insertList)) {
                this.saveBatch(insertList);
                List<AgentCommissionListVo.AgentCommissionInfo> insertCommissionList = commissionList.stream()
                        .filter(commissionInfo -> Objects.isNull(commissionInfo.getId()) || commissionInfo.getId() < 1)
                        .collect(Collectors.toList());
                for (int i = 0; i < insertList.size(); i++) {
                    AgentCommissionListVo.AgentCommissionInfo commissionInfo = insertCommissionList.get(i);
                    AgentCommission commission = insertList.get(i);
                    AgentCommissionMajorLevelCustom relation = new AgentCommissionMajorLevelCustom();
                    relation.setFkAgentCommissionId(commission.getId());
                    relation.setFkMajorLevelCustomId(commissionInfo.getLevelId());
                    relation.setGmtModified(new Date());
                    relation.setGmtModifiedUser(loginId);
                    relation.setGmtCreate(new Date());
                    relation.setGmtCreateUser(loginId);
                    majorLevelCustoms.add(relation);
                    newIds.addAll(insertList.stream().map(AgentCommission::getId).distinct().collect(Collectors.toList()));
                    change.set(true);
                }
            } else {
                change.set(false);
            }

            //要编辑的
            Map<Long, Long> levelMap = new HashMap<>();
            List<AgentCommission> updateCommissions = commissionList.stream()
                    .filter(commissionInfo -> Objects.nonNull(commissionInfo.getId()) && commissionInfo.getId() > 0)
                    .map(commissionInfo -> {
                        AgentCommission commission = agentCommissionMapper.selectById(commissionInfo.getId());
                        if (Objects.isNull(commission)) {
                            return null;
                        }

                        AgentCommission original = new AgentCommission();
                        BeanCopyUtils.copyProperties(commission, original);

                        BeanCopyUtils.copyProperties(commissionInfo, commission);
                        commission.setTitle(commissionInfo.getCustomName());
                        commission.setTitleNative(commissionInfo.getCustomNameChn());
                        commission.setGmtModified(new Date());
                        commission.setGmtModifiedUser(loginId);
                        commission.setCommissionType(3);
                        commission.setFkAgentCommissionPlanId(planId);
                        commission.setPackageName(combinationInfo.getPackageName());
                        commission.setPackageKey(combinationInfo.getPackageKey());
                        commission.setPackageNameNative(combinationInfo.getPackageNameNative());
                        levelMap.put(commission.getId(), commissionInfo.getLevelId());

                        if (!change.get()) {
                            change.set(EntityCompareUtil.compareFields(original, commission));
                        }
                        return commission;
                    }).collect(Collectors.toList());
            updateCommissions.stream().filter(Objects::nonNull);
            if (CollectionUtils.isNotEmpty(updateCommissions)) {
                this.updateBatchById(updateCommissions);
                Set<Long> keySet = levelMap.keySet();
                keySet.stream().forEach(levelId -> {
                    AgentCommissionMajorLevelCustom relation = new AgentCommissionMajorLevelCustom();
                    relation.setFkAgentCommissionId(levelId);
                    relation.setFkMajorLevelCustomId(levelMap.get(levelId));
                    relation.setGmtModified(new Date());
                    relation.setGmtModifiedUser(loginId);
                    relation.setGmtCreate(new Date());
                    relation.setGmtCreateUser(loginId);
                    majorLevelCustoms.add(relation);
                });

                //删除原本的关联关系
                majorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                        .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, keySet));
            }
            //删除的
            List<Long> currentIds = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                            .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                            .eq(AgentCommission::getCommissionType, 3)
                            .eq(AgentCommission::getPackageKey, combinationInfo.getPackageKey())
                            .notIn(CollectionUtils.isNotEmpty(newIds), AgentCommission::getId, newIds))
                    .stream().map(AgentCommission::getId).collect(Collectors.toList());
            currentIds.removeAll(updateCommissions.stream().map(AgentCommission::getId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(currentIds)) {
                change.set(true);
                this.removeByIds(currentIds);
                majorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                        .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, currentIds));
            }

            if (change.get()) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.AGENT_PLAN,
                        planId, LogEventEnum.UPDATE_COMBINATION_COMMISSION, loginId,
                        combinationInfo.getPackageName(), null));
            }

        }
        //删除整个package
        List<AgentCommission> currentList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getCommissionType, 3)
                .notIn(CollectionUtils.isNotEmpty(newPackageKeys), AgentCommission::getPackageKey, newPackageKeys));
        if (CollectionUtils.isNotEmpty(currentList)) {
            //找出传过来有packageKey的
            List<String> currentPackageKeys = currentList.stream().map(AgentCommission::getPackageKey).distinct().collect(Collectors.toList());
            currentPackageKeys.removeAll(updateCombinationList.stream().map(AgentCommissionListVo.AgentCombinationInfo::getPackageKey).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(currentPackageKeys)) {
                List<Long> delIds = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                                .eq(AgentCommission::getCommissionType, 3)
                                .in(AgentCommission::getPackageKey, currentPackageKeys))
                        .stream().map(AgentCommission::getId).collect(Collectors.toList());
                this.removeByIds(delIds);
                majorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                        .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, delIds));
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.AGENT_PLAN,
                        planId, LogEventEnum.DEL_COMBINATION_COMMISSION, loginId,
                        currentPackageKeys.size(), null));
            }
        }

        if (CollectionUtils.isNotEmpty(majorLevelCustoms)) {
            majorLevelCustomService.saveBatch(majorLevelCustoms);
        }
        return logs;
    }

    private List<LogDto> saveBonusInfoList(List<AgentCommissionListVo.AgentBonusInfo> bonusList, Long planId, String loginId) {
        List<AgentCommissionListVo.AgentBonusInfo> noLevelList = bonusList.stream().filter(bonus -> CollectionUtils.isEmpty(bonus.getLevelIds())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noLevelList)) {
//            throw new GetServiceException("课程等级信息缺失");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_COURSE_LEVEL_MISSING", "课程等级信息缺失"));
        }
        List<LogDto> logs = new ArrayList<>();
        List<AgentCommission> currentList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getCommissionType, 2));
        if (CollectionUtils.isEmpty(bonusList)) {
            List<Long> delIds = currentList.stream().map(AgentCommission::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delIds)) {
                this.removeByIds(delIds);
                majorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                        .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, delIds));
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.AGENT_PLAN,
                        planId, LogEventEnum.DEL_BONUS_COMMISSION, loginId,
                        delIds.size(), null));
                return logs;
            }
        }
        List<AgentCommissionMajorLevelCustom> majorLevelCustoms = new ArrayList<>();
        List<Long> currentIds = currentList.stream().map(AgentCommission::getId).collect(Collectors.toList());
        //编辑
        List<AgentCommissionListVo.AgentBonusInfo> updateList = bonusList.stream()
                .filter(bonusInfo -> Objects.nonNull(bonusInfo.getId()) && bonusInfo.getId() > 0)
                .collect(Collectors.toList());
        //删除的
        List<Long> updateIds = updateList.stream().map(AgentCommissionListVo.AgentBonusInfo::getId).distinct().collect(Collectors.toList());
        currentIds.removeAll(updateIds);
        //编辑
        List<AgentCommission> updateCommissions = updateList.stream().map(bonusInfo -> {
            AgentCommission commission = agentCommissionMapper.selectById(bonusInfo.getId());
            if (Objects.isNull(commission)) {
                return null;
            }

            AgentCommission original = new AgentCommission();
            BeanCopyUtils.copyProperties(commission, original);

            BeanCopyUtils.copyProperties(bonusInfo, commission);
            commission.setGmtModified(new Date());
            commission.setGmtModifiedUser(loginId);
            commission.setCommissionType(2);
            commission.setFkAgentCommissionPlanId(planId);

            boolean change = EntityCompareUtil.compareFields(original, commission);
            if (change) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.AGENT_PLAN,
                        planId, LogEventEnum.UPDATE_BONUS_COMMISSION, loginId, "", null));
            }

            //对比课程等级
            if (!change) {
                List<Long> newLevelIds = bonusInfo.getLevelIds();
                List<Long> oldLevelIds = majorLevelCustomService.list(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                                .eq(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, commission.getId()))
                        .stream().map(AgentCommissionMajorLevelCustom::getFkMajorLevelCustomId).collect(Collectors.toList());
                boolean equal = ListComparisonUtil.areListsEqual(newLevelIds, oldLevelIds);
                if (!equal) {
                    logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.AGENT_PLAN,
                            planId, LogEventEnum.UPDATE_BONUS_COMMISSION, loginId, "", null));
                }
            }

            bonusInfo.getLevelIds().forEach(levelId -> {
                AgentCommissionMajorLevelCustom relation = new AgentCommissionMajorLevelCustom();
                relation.setFkAgentCommissionId(commission.getId());
                relation.setFkMajorLevelCustomId(levelId);
                relation.setGmtModified(new Date());
                relation.setGmtModifiedUser(loginId);
                relation.setGmtCreate(new Date());
                relation.setGmtCreateUser(loginId);
                majorLevelCustoms.add(relation);
            });
            return commission;
        }).collect(Collectors.toList());
        updateCommissions.stream().filter(Objects::nonNull);
        if (CollectionUtils.isNotEmpty(updateCommissions)) {
            this.updateBatchById(updateCommissions);
            majorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                    .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, updateIds));
        }
        //新增
        List<AgentCommissionListVo.AgentBonusInfo> insertList = bonusList.stream().filter(bonus -> Objects.isNull(bonus.getId()) || bonus.getId() < 1).collect(Collectors.toList());
        List<AgentCommission> insertCommissions = insertList.stream().map(bonusInfo -> {
            AgentCommission commission = new AgentCommission();
            BeanCopyUtils.copyProperties(bonusInfo, commission);
            commission.setGmtModified(new Date());
            commission.setGmtModifiedUser(loginId);
            commission.setGmtCreate(new Date());
            commission.setGmtCreateUser(loginId);
            commission.setCommissionType(2);
            commission.setFkAgentCommissionPlanId(planId);
            return commission;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertCommissions)) {
            this.saveBatch(insertCommissions);
            for (int i = 0; i < insertCommissions.size(); i++) {
                AgentCommissionListVo.AgentBonusInfo commissionInfo = insertList.get(i);
                AgentCommission commission = insertCommissions.get(i);
                commissionInfo.getLevelIds().forEach(levelId -> {
                    AgentCommissionMajorLevelCustom relation = new AgentCommissionMajorLevelCustom();
                    relation.setFkAgentCommissionId(commission.getId());
                    relation.setFkMajorLevelCustomId(levelId);
                    relation.setGmtModified(new Date());
                    relation.setGmtModifiedUser(loginId);
                    relation.setGmtCreate(new Date());
                    relation.setGmtCreateUser(loginId);
                    majorLevelCustoms.add(relation);
                });
            }
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.AGENT_PLAN,
                    planId, LogEventEnum.ADD_BONUS_COMMISSION, loginId,
                    insertCommissions.size(), null));
        }
        if (CollectionUtils.isNotEmpty(currentIds)) {
            this.removeByIds(currentIds);
            majorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                    .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, currentIds));
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.AGENT_PLAN,
                    planId, LogEventEnum.DEL_BONUS_COMMISSION, loginId,
                    currentIds.size(), null));
        }
        if (CollectionUtils.isNotEmpty(majorLevelCustoms)) {
            majorLevelCustomService.saveBatch(majorLevelCustoms);
        }

        return logs;
    }

    private List<AgentCommissionListVo.AgentCommissionInfo> getCommonCommissionList(List<AgentCommission> commissionList, Boolean initCommission, String feedbackRate,
                                                                                    Long providerPlanId, Boolean queryProviderCommission, Boolean approvalPlan) {
//        if (CollectionUtils.isEmpty(commissionList)) {
//            return new ArrayList<>();
//        }
        Boolean providerPlanApprovalPass;
        InstitutionProviderCommissionPlan providerCommissionPlan = providerCommissionPlanMapper.selectById(providerPlanId);
        if (Objects.nonNull(providerCommissionPlan) && providerCommissionPlan.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode())) {
            providerPlanApprovalPass = Boolean.TRUE;
        } else {
            providerPlanApprovalPass = Boolean.FALSE;
        }
        List<CommissionMajorLevelVo> commissionMajorLevelVos = majorLevelCustomService.getCommissionMajorLevelList(commissionList.stream().map(AgentCommission::getId).collect(Collectors.toList()));
        Map<Long, List<CommissionMajorLevelVo>> commissionLevelGroup = commissionMajorLevelVos.stream()
                .collect(Collectors.groupingBy(CommissionMajorLevelVo::getCommissionId));
        List<InstitutionProviderCommission> toAddProviderCommission = new ArrayList<>();
        //获取原始佣金-学校提供商，用作后续对比
        Map<Long, InstitutionProviderCommission> providerCommissionMap;
        Set<Long> providerCommissionMapKeys = new HashSet<>();
        //保存比例
        String saveRate = getRate(2, SecureUtil.getFkCompanyId(), providerPlanId);

        if (queryProviderCommission) {
            //查询合同方案的佣金,如果那边比代理的多,就要将多出的加到代理这边
            providerCommissionMap = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                            .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerPlanId)
                            .eq(InstitutionProviderCommission::getCommissionType, 1))
                    .stream().collect(Collectors.toMap(
                            InstitutionProviderCommission::getId,
                            Function.identity(),
                            (existing, replacement) -> existing));
            providerCommissionMapKeys = providerCommissionMap.keySet();
            List<Long> currentProviderCommissionIds = commissionList.stream().map(AgentCommission::getFkInstitutionProviderCommissionId).distinct().collect(Collectors.toList());
            Set<Long> missingKeys = new HashSet<>(providerCommissionMapKeys);
            missingKeys.removeAll(currentProviderCommissionIds);
            if (!missingKeys.isEmpty()) {
                // 获取 missingKeys 对应的所有值
                toAddProviderCommission = missingKeys.stream()
                        .map(providerCommissionMap::get).filter(Objects::nonNull).collect(Collectors.toList());
            }
        } else {
            providerCommissionMap = new HashMap<>();
        }

        Set<Long> finalProviderCommissionMapKeys = providerCommissionMapKeys;
        List<Long> delIds = new ArrayList<>();
        List<AgentCommissionListVo.AgentCommissionInfo> infoList = commissionList.stream().map(commissionInfo -> {
                    //如果合同端审核通过，代理端要同步新增和删除的
                    if (!finalProviderCommissionMapKeys.contains(commissionInfo.getFkInstitutionProviderCommissionId()) && providerPlanApprovalPass) {
                        delIds.add(commissionInfo.getId());
                        return null;
                    }
                    AgentCommissionListVo.AgentCommissionInfo info = new AgentCommissionListVo.AgentCommissionInfo();
                    BeanCopyUtils.copyProperties(commissionInfo, info);
                    info.setLevelId(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getLevelId());
                    info.setCustomName(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getCustomName());
                    info.setCustomNameChn(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getCustomNameChn());
                    info.setViewOrder(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getViewOrder());
                    info.setOriginalCommission(Objects.nonNull(providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()))
                            ? providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()).getCommission() : null);
                    info.setOriginalFollowCommission(Objects.nonNull(providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()))
                            ? providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()).getFollowCommission() : null);
                    info.setOriginalCommissionUnit(Objects.nonNull(providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()))
                            ? providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()).getCommissionUnit() : null);
                    info.setOriginalFollowCommissionUnit(Objects.nonNull(providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()))
                            ? providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()).getFollowCommissionUnit() : null);
                    if (initCommission) {
                        info.setCommission(PercentageUtil.calculatePercentage(feedbackRate, Objects.nonNull(providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()))
                                ? providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()).getCommission() : null));
                        info.setFollowCommission(PercentageUtil.calculatePercentage(feedbackRate, Objects.nonNull(providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()))
                                ? providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId()).getFollowCommission() : null));
                    }
                    info.setSaveRate(saveRate);
                    //判断当前的佣金是否超过限制,如果审核没通过,都要判断是否超过限制
                    if (approvalPlan && !initCommission) {
                        info.setCommissionOverLimit(Boolean.FALSE);
                        info.setFollowCommissionOverLimit(Boolean.FALSE);
                    } else {
                        info.setCommissionOverLimit(checkCommissionAmountOverLimit(info, providerCommissionMap, saveRate, 1));
                        info.setFollowCommissionOverLimit(checkCommissionAmountOverLimit(info, providerCommissionMap, saveRate, 2));
                    }
                    return info;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(AgentCommissionListVo.AgentCommissionInfo::getViewOrder, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
        //将合同那边多出的加到代理这边
        if (queryProviderCommission && CollectionUtils.isNotEmpty(toAddProviderCommission) && providerPlanApprovalPass) {
            List<CommissionMajorLevelVo> providerCommissionMajorLevelVos = commissionMajorLevelCustomService.getCommissionMajorLevelList(toAddProviderCommission.stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList()));
            Map<Long, List<CommissionMajorLevelVo>> providerCommissionLevelGroup = providerCommissionMajorLevelVos.stream()
                    .collect(Collectors.groupingBy(CommissionMajorLevelVo::getCommissionId));
            List<AgentCommissionListVo.AgentCommissionInfo> toAddCommissionList = toAddProviderCommission.stream()
                    .map(commission -> {
                        AgentCommissionListVo.AgentCommissionInfo info = new AgentCommissionListVo.AgentCommissionInfo();
                        BeanCopyUtils.copyProperties(commission, info);
                        info.setRemarkNoteNative(commission.getRemarkNoteChn());
                        info.setTitleNative(commission.getTitleChn());
                        info.setId(null);
                        info.setIsActive(0);
                        info.setCommission(PercentageUtil.calculatePercentage(feedbackRate, commission.getCommission()));
                        info.setFollowCommission(PercentageUtil.calculatePercentage(feedbackRate, commission.getFollowCommission()));
                        info.setOriginalCommission(commission.getCommission());
                        info.setOriginalFollowCommission(commission.getFollowCommission());
                        info.setFkInstitutionProviderCommissionId(commission.getId());
                        info.setFollowRemarkNoteNative(commission.getFollowRemarkNote());
                        info.setLevelId(Objects.isNull(providerCommissionLevelGroup.get(commission.getId())) ? null : providerCommissionLevelGroup.get(commission.getId()).get(0).getLevelId());
                        info.setCustomName(Objects.isNull(providerCommissionLevelGroup.get(commission.getId())) ? null : providerCommissionLevelGroup.get(commission.getId()).get(0).getCustomName());
                        info.setCustomNameChn(Objects.isNull(providerCommissionLevelGroup.get(commission.getId())) ? null : providerCommissionLevelGroup.get(commission.getId()).get(0).getCustomNameChn());
                        info.setViewOrder(Objects.isNull(providerCommissionLevelGroup.get(commission.getId())) ? null : providerCommissionLevelGroup.get(commission.getId()).get(0).getViewOrder());
                        info.setOriginalCommissionUnit(commission.getCommissionUnit());
                        info.setOriginalFollowCommissionUnit(commission.getFollowCommissionUnit());
                        info.setSaveRate(saveRate);
                        return info;
                    }).collect(Collectors.toList());
            infoList.addAll(toAddCommissionList);
            infoList.stream().sorted(Comparator.comparing(AgentCommissionListVo.AgentCommissionInfo::getViewOrder, Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());
        }
        //删除合同端已经没有的佣金明细
        if (queryProviderCommission && CollectionUtils.isNotEmpty(delIds) && providerPlanApprovalPass) {
            agentCommissionMapper.delete(new LambdaQueryWrapper<AgentCommission>()
                    .in(AgentCommission::getId, delIds));
        }
        return infoList;
    }

    private List<AgentCommissionListVo.AgentCommissionInfo> getAgentCommissionList(Long planId, Boolean initCommission, String feedbackRate,
                                                                                   Long providerPlanId, Boolean queryProviderCommission,
                                                                                   Boolean approvalPlan) {
        List<AgentCommission> commissionList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getCommissionType, 1));
        return getCommonCommissionList(commissionList, initCommission, feedbackRate, providerPlanId, queryProviderCommission, approvalPlan);
    }

    private List<AgentCommissionListVo.AgentCombinationInfo> getCombinationList(Long planId, Boolean initCommission, String rate) {
        List<AgentCommission> combinationList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getCommissionType, 3)
                .orderByAsc(AgentCommission::getId)
        );
        Map<String, List<AgentCommission>> packageList = combinationList.stream()
                .collect(Collectors.groupingBy(AgentCommission::getPackageKey));
//        List<AgentCommissionListVo.AgentCombinationInfo> combinationInfoList = packageList.entrySet().stream().map(entry -> {
        List<AgentCommissionListVo.AgentCombinationInfo> combinationInfoList = packageList.entrySet().stream()
                .sorted(Comparator.comparing(entry -> entry.getValue().get(0).getId()))
                .map(entry -> {
                    AgentCommissionListVo.AgentCombinationInfo info = new AgentCommissionListVo.AgentCombinationInfo();
                    info.setPackageKey(entry.getKey());
                    info.setPackageName(entry.getValue().get(0).getPackageName());
                    info.setPackageNameNative(entry.getValue().get(0).getPackageNameNative());
                    List<AgentCommissionListVo.AgentCommissionInfo> commissionInfoList = getCommonCommissionList(entry.getValue(), initCommission, rate, null, Boolean.FALSE, Boolean.FALSE);
                    info.setAgentCommissionInfos(commissionInfoList);
                    List<Long> commissionIds = entry.getValue().stream().map(AgentCommission::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(commissionIds)) {
                        List<Long> levelIds = agentCommissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                                .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, commissionIds)
                        ).stream().map(AgentCommissionMajorLevelCustom::getFkMajorLevelCustomId).distinct().collect(Collectors.toList());
                        info.setLevelIds(levelIds);
                        if (CollectionUtils.isNotEmpty(levelIds)) {
                            List<String> names = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                                            .in(MajorLevelCustom::getId, levelIds))
                                    .stream().map(MajorLevelCustom::getCustomName).distinct().collect(Collectors.toList());
                            info.setLevelNames(names);
                        }
                    }
                    return info;
                }).collect(Collectors.toList());
        return combinationInfoList;
    }

    private List<AgentCommissionListVo.AgentBonusInfo> getBonusInfoList(Long planId, Boolean initCommission, String rate) {
        List<AgentCommission> bonusList = agentCommissionMapper.selectList(new LambdaQueryWrapper<AgentCommission>()
                .eq(AgentCommission::getFkAgentCommissionPlanId, planId)
                .eq(AgentCommission::getCommissionType, 2));
        List<AgentCommissionListVo.AgentBonusInfo> bonusInfoList = bonusList.stream().map(commissionInfo -> {
            AgentCommissionListVo.AgentBonusInfo bonusInfo = new AgentCommissionListVo.AgentBonusInfo();
            BeanCopyUtils.copyProperties(commissionInfo, bonusInfo);
            bonusInfo.setOriginalCommission(commissionInfo.getCommission());
            bonusInfo.setOriginalFollowCommission(commissionInfo.getFollowCommission());
            //获取已保存的不用初始化
//            if (initCommission) {
//                bonusInfo.setFollowCommission(PercentageUtil.calculatePercentage(rate, commissionInfo.getFollowCommission()));
//                bonusInfo.setCommission(PercentageUtil.calculatePercentage(rate, commissionInfo.getCommission()));
//            }
            List<Long> levelIds = agentCommissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                    .eq(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, commissionInfo.getId())
            ).stream().map(AgentCommissionMajorLevelCustom::getFkMajorLevelCustomId).distinct().collect(Collectors.toList());
            bonusInfo.setLevelIds(levelIds);
            if (CollectionUtils.isNotEmpty(levelIds)) {
                List<String> names = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                                .in(MajorLevelCustom::getId, levelIds))
                        .stream().map(MajorLevelCustom::getCustomName).distinct().collect(Collectors.toList());
                bonusInfo.setLevelNames(names);
            }
            return bonusInfo;
        }).collect(Collectors.toList());
        return sortBonusInfoList(bonusInfoList);
    }


    private Boolean checkCommissionAmountOverLimit(AgentCommissionListVo.AgentCommissionInfo commissionInfo,
                                                   Map<Long, InstitutionProviderCommission> providerCommissionMap,
                                                   String rate, Integer type) {
        if (Objects.isNull(commissionInfo.getFkInstitutionProviderCommissionId())) {
            return false;
        }
        if (Objects.isNull(providerCommissionMap)) {
            return false;
        }
        InstitutionProviderCommission institutionProviderCommission = providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId());
        if (Objects.isNull(institutionProviderCommission)) {
            log.error("原始佣金InstitutionProviderCommission is null:id={}", commissionInfo.getFkInstitutionProviderCommissionId());
            return false;
        }
        if (type.equals(1)) {
            //判断佣金
            if (StringUtils.isNotBlank(commissionInfo.getCommissionUnit())
                    && StringUtils.isNotBlank(institutionProviderCommission.getCommissionUnit())
                    && commissionInfo.getCommissionUnit().equals(institutionProviderCommission.getCommissionUnit())) {
                //判断原始金额,如果大于会返回false
                return !PercentageUtil.isWithinLimit(institutionProviderCommission.getCommission(), commissionInfo.getCommission(), rate);
            }
            return false;
        }
        if (type.equals(2)) {
            //判断后续佣金
            if (StringUtils.isNotBlank(commissionInfo.getFollowCommissionUnit())
                    && StringUtils.isNotBlank(institutionProviderCommission.getFollowCommissionUnit())
                    && commissionInfo.getFollowCommissionUnit().equals(institutionProviderCommission.getFollowCommissionUnit())) {
                //判断原始金额
                return !PercentageUtil.isWithinLimit(institutionProviderCommission.getFollowCommission(), commissionInfo.getFollowCommission(), rate);
            }
        }
        return false;
    }


    private String getRate(Integer value, Long companyId, Long providerPlanId) {
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.PMP_AGENT_COMMISSION_RATE.key, value).getData();
        log.info("回显比例,获取系统配置CompanyConfigMap: {},key:{},value:{}", JSONObject.toJSONString(companyConfigMap), ProjectKeyEnum.PMP_AGENT_COMMISSION_RATE.key, 1);
        //回显比例
        if (value.equals(1)) {
            if (companyConfigMap.containsKey(companyId)) {
//                return companyConfigMap.get(companyId);
                String countryRate = getCountryRate(companyConfigMap.get(companyId), providerPlanId);
                return StringUtils.isNotBlank(countryRate) ? countryRate : "80";
            }
            return "80";
        }
        //保存比例
        if (value.equals(2)) {
            if (companyConfigMap.containsKey(companyId)) {
                return companyConfigMap.get(companyId);
            }
            return "85";
        }
        return "80";
    }

    private String getCountryRate(String rateStr, Long providerPlanId) {
        //rateStr:{"GBR":80,"AUS":60,"USA":60,"CAN":75,"NZL":70,"DEFAULT":60}
        Map<String, Object> rateMap = parseRate(rateStr);
        List<Long> institutionIds = providerCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                        .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, providerPlanId))
                .stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(institutionIds)) {
            List<Long> countryIds = institutionCenterMapper.getInstitutionListByIds(institutionIds)
                    .stream()
                    .map(InstitutionVo::getCountryId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(countryIds)) {
                return StringUtils.EMPTY;
            }
            List<String> countryNums = institutionCenterMapper.queryCountryListByIds(countryIds)
                    .stream()
                    .map(CountryVo::getNum)
                    .distinct()
                    .collect(Collectors.toList());
            if (countryNums.size() > 1) {
                //多个国别默认
                return rateMap.getOrDefault("DEFAULT", StringUtils.EMPTY).toString();
            } else {
                //取国别默认值-国别没有也取默认
                String rate = rateMap.getOrDefault(countryNums.get(0), StringUtils.EMPTY).toString();
                if (StringUtils.isNotBlank(rate)) {
                    return rate;
                }
                return rateMap.getOrDefault("DEFAULT", StringUtils.EMPTY).toString();
            }
        }
        return rateMap.getOrDefault("DEFAULT", StringUtils.EMPTY).toString();
    }

    /**
     * 初始化id、packageKey
     *
     * @param list
     * @return
     */
    private ExtendAgentCommissionVo initAgentCommissionList(ExtendAgentCommissionVo list) {
        List<AgentCommissionListVo.AgentCommissionInfo> commissionInfoList = list.getAgentCommissionInfoList();
        commissionInfoList.stream().forEach(info -> {
            info.setId(null);
            info.setFkAgentCommissionPlanId(null);
        });
        List<AgentCommissionListVo.AgentCombinationInfo> combinationList = list.getAgentCombinationList();
        combinationList.stream().forEach(info -> {
            info.setPackageKey(Strings.EMPTY);
            info.getAgentCommissionInfos().stream().forEach(commissionInfo -> {
                commissionInfo.setCommission(BigDecimal.ZERO);
                commissionInfo.setFollowCommission(BigDecimal.ZERO);
                commissionInfo.setId(null);
                commissionInfo.setFkAgentCommissionPlanId(null);
            });
        });
        list.setAgentCombinationList(combinationList);
        list.setAgentBonusList(new ArrayList<>());
//        List<AgentCommissionListVo.AgentBonusInfo> bonusList = list.getAgentBonusList();
//        bonusList.stream().forEach(info -> {
//            info.setCommission(BigDecimal.ZERO);
//            info.setFollowCommission(BigDecimal.ZERO);
//            info.setId(null);
//            info.setFkAgentCommissionPlanId(null);
//        });
        return list;
    }

    private List<AgentCommissionListVo.AgentBonusInfo> sortBonusInfoList(List<AgentCommissionListVo.AgentBonusInfo> bonusInfoList) {
        if (CollectionUtils.isEmpty(bonusInfoList)) {
            return new ArrayList<>();
        }
        List<Long> allLevelIds = bonusInfoList.stream()
                .filter(bonus -> CollectionUtils.isNotEmpty(bonus.getLevelIds()))
                .flatMap(bonus -> bonus.getLevelIds().stream())
                .distinct()
                .collect(Collectors.toList());
        //排序,优先排只有一个课程的，只有一个课程的根据viewOrder降序排序，多个课程等级的根据viewOrder的总和降序
        if (CollectionUtils.isEmpty(allLevelIds)) {
            return bonusInfoList;
        }
        Map<Long, Integer> levelViewOrderMap = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                        .in(MajorLevelCustom::getId, allLevelIds)).stream()
                .collect(Collectors.toMap(
                        MajorLevelCustom::getId,
                        MajorLevelCustom::getViewOrder,
                        Integer::max));
        bonusInfoList.sort((a, b) -> {
            int aSize = a.getLevelIds() != null ? a.getLevelIds().size() : 0;
            int bSize = b.getLevelIds() != null ? b.getLevelIds().size() : 0;

            // 1. 先按 levelId 数量，1个的排前面
            if (aSize == 1 && bSize != 1) {
                return -1;
            }
            if (aSize != 1 && bSize == 1) {
                return 1;
            }

            // 2. 如果都是 size==1，就用 viewOrder 倒序
            if (aSize == 1 && bSize == 1) {
                Integer aOrder = levelViewOrderMap.getOrDefault(a.getLevelIds().get(0), 0);
                Integer bOrder = levelViewOrderMap.getOrDefault(b.getLevelIds().get(0), 0);
                return Integer.compare(bOrder, aOrder);
            }

            // 3. 都是 size > 1，按 viewOrder 总和倒序
            int aSum = a.getLevelIds().stream()
                    .mapToInt(id -> levelViewOrderMap.getOrDefault(id, 0))
                    .sum();
            int bSum = b.getLevelIds().stream()
                    .mapToInt(id -> levelViewOrderMap.getOrDefault(id, 0))
                    .sum();
            return Integer.compare(bSum, aSum);
        });
        return bonusInfoList;
    }

    /**
     * 解析rate字符串为Map对象
     *
     * @param rateStr 可能是数字字符串或JSON字符串
     * @return Map<String, Object>
     */
    public static Map<String, Object> parseRate(String rateStr) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (rateStr != null && rateStr.trim().startsWith("{")) {
                // 是JSON格式
                result = objectMapper.readValue(rateStr, new TypeReference<Map<String, Object>>() {
                });
            } else {
                // 是单一数值，作为DEFAULT国家值
                result.put("DEFAULT", Double.parseDouble(rateStr));
            }
        } catch (Exception e) {
            throw new RuntimeException("rateStr格式错误: " + rateStr, e);
        }
        return result;
    }


//    private Boolean checkCommissionAmount(AgentCommissionListVo.AgentCommissionInfo commissionInfo,
//                                          Map<Long, InstitutionProviderCommission> providerCommissionMap,
//                                          String rate) {
//        if (Objects.isNull(commissionInfo.getFkInstitutionProviderCommissionId())) {
//            log.error("参数有误:{}", JSONObject.toJSONString(commissionInfo));
//            throw new GetServiceException("参数有误");
//        }
//        if (Objects.isNull(providerCommissionMap)) {
//            log.error("原始佣金为空:{}", JSONObject.toJSONString(commissionInfo));
//            return true;
//        }
//        InstitutionProviderCommission institutionProviderCommission = providerCommissionMap.get(commissionInfo.getFkInstitutionProviderCommissionId());
//        if (Objects.isNull(institutionProviderCommission)) {
//            log.error("原始佣金InstitutionProviderCommission is null:id={}", commissionInfo.getFkInstitutionProviderCommissionId());
//            return true;
//        }
//        if (StringUtils.isNotBlank(commissionInfo.getCommissionUnit())
//                && StringUtils.isNotBlank(institutionProviderCommission.getCommissionUnit())
//                && commissionInfo.getCommissionUnit().equals(institutionProviderCommission.getCommissionUnit())) {
//            //判断原始金额
//            boolean commissionCheck = PercentageUtil.isWithinLimit(institutionProviderCommission.getCommission(), commissionInfo.getCommission(), rate);
//            if (commissionCheck) {
//                if (StringUtils.isNotBlank(commissionInfo.getFollowCommissionUnit())
//                        && StringUtils.isNotBlank(institutionProviderCommission.getFollowCommissionUnit())
//                        && commissionInfo.getFollowCommissionUnit().equals(institutionProviderCommission.getFollowCommissionUnit())) {
//                    //判断后续金额
//                    return PercentageUtil.isWithinLimit(institutionProviderCommission.getFollowCommission(), commissionInfo.getFollowCommission(), rate);
//                }
//            }
//            return commissionCheck;
//        }
//        return true;
//    }

}

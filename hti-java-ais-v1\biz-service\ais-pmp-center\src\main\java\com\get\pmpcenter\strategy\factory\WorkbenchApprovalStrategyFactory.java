package com.get.pmpcenter.strategy.factory;

import com.get.pmpcenter.strategy.WorkbenchApprovalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/4/27
 * @Version 1.0
 * @apiNote:pmp工作台审核策略工厂
 */
@Component
public class WorkbenchApprovalStrategyFactory {

    private final Map<String, WorkbenchApprovalStrategy> strategyMap = new HashMap<>();

    @Autowired
    public WorkbenchApprovalStrategyFactory(List<WorkbenchApprovalStrategy> strategies) {
        for (WorkbenchApprovalStrategy strategy : strategies) {
            strategyMap.put(strategy.getApprovalType(), strategy);
        }
    }

    public WorkbenchApprovalStrategy getStrategy(String approvalType) {
        WorkbenchApprovalStrategy strategy = strategyMap.get(approvalType);
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported approval type: " + approvalType);
        }
        return strategy;
    }
}

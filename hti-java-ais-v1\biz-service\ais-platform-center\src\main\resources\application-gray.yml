#服务器端口
server:
  port: 1198


#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: platformdb
      datasource:
        platformdb:
          url: ${get.datasource.gray.platformdb.url}
          username: ${get.datasource.gray.platformdb.username}
          password: ${get.datasource.gray.platformdb.password}
        registrationdb:
          url: ${get.datasource.gray.registrationdb.url}
          username: ${get.datasource.gray.registrationdb.username}
          password: ${get.datasource.gray.registrationdb.password}
        appmsodb:
          url: ${get.datasource.gray.appmsodb.url}
          username: ${get.datasource.gray.appmsodb.username}
          password: ${get.datasource.gray.appmsodb.password}
        appmsologdb:
          url: ${get.datasource.gray.appmsologdb.url}
          username: ${get.datasource.gray.appmsologdb.username}
          password: ${get.datasource.gray.appmsologdb.password}
        aisfiledb:
          url: ${get.datasource.gray.aisfiledb.url}
          username: ${get.datasource.gray.aisfiledb.username}
          password: ${get.datasource.gray.aisfiledb.password}
package com.get.reportcenter.dao.report;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.reportcenter.entity.AdvSearchConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;

@Mapper
public interface AdvSearchConfigMapper extends BaseMapper<AdvSearchConfig> {
    int insert(AdvSearchConfig record);

    int insertSelective(AdvSearchConfig record);

    List<LinkedHashMap<String, String>> getQueryObject(IPage<LinkedHashMap<String, String>> iPage, @Param("query") String query);
}
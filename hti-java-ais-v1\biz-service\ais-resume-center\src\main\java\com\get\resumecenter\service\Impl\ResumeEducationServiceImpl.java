package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeEducationMapper;
import com.get.resumecenter.vo.ResumeEducationVo;
import com.get.resumecenter.entity.ResumeEducation;
import com.get.resumecenter.service.IResumeEducationService;
import com.get.resumecenter.dto.ResumeEducationDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 14:19
 * @Description: 教育经历实现类
 **/
@Service
public class ResumeEducationServiceImpl implements IResumeEducationService {
    @Resource
    private ResumeEducationMapper educationMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<ResumeEducationVo> getResumeEducationListDto(Long resumeId) {
        if (GeneralTool.isEmpty(resumeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
//        Example example = new Example(ResumeEducation.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        example.orderBy("startDate").desc();
        List<ResumeEducation> resumeEducations = educationMapper.selectList(Wrappers.<ResumeEducation>lambdaQuery().eq(ResumeEducation::getFkResumeId, resumeId).orderByDesc(ResumeEducation::getStartDate));
        return resumeEducations.stream()
                .map(resumeEducation -> BeanCopyUtils.objClone(resumeEducation, ResumeEducationVo::new)).collect(Collectors.toList());
    }

    @Override
    public ResumeEducationVo getResumeEducationById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeEducation resumeEducation = educationMapper.selectById(id);
        return BeanCopyUtils.objClone(resumeEducation, ResumeEducationVo::new);
    }

    @Override
    public Long addResumeEducation(ResumeEducationDto educationVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(educationVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeEducation resumeEducation = BeanCopyUtils.objClone(educationVo, ResumeEducation::new);
        utilService.updateUserInfoToEntity(resumeEducation);
        int i = educationMapper.insertSelective(resumeEducation);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return resumeEducation.getId();
    }

    @Override
    public ResumeEducationVo updateResumeEducation(ResumeEducationDto educationVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(educationVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeEducation resumeEducation = BeanCopyUtils.objClone(educationVo, ResumeEducation::new);
        utilService.updateUserInfoToEntity(resumeEducation);
        educationMapper.updateById(resumeEducation);
        return getResumeEducationById(resumeEducation.getId());
    }

    @Override
    public void deleteResumeEducation(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        educationMapper.deleteById(id);
    }

    @Override
    public List<Long> getResumeIdByInstitution(String institution) {
        if (GeneralTool.isEmpty(institution)) {
            return null;
        }
//        Example example = new Example(ResumeEducation.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andLike("institution", "%" + institution + "%");
        List<ResumeEducation> resumeEducations = educationMapper.selectList(Wrappers.<ResumeEducation>lambdaQuery().like(ResumeEducation::getInstitution, institution));
        if (GeneralTool.isEmpty(resumeEducations)) {
            return null;
        }
        return resumeEducations.stream().map(ResumeEducation::getFkResumeId).collect(Collectors.toList());
    }
}

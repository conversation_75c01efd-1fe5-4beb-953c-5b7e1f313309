#服务器端口
server:
  port: 1198


#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: platformdb
      datasource:
        platformdb:
          url: ${get.datasource.local.platformdb.url}
          username: ${get.datasource.local.platformdb.username}
          password: ${get.datasource.local.platformdb.password}
        registrationdb:
          url: ${get.datasource.local.registrationdb.url}
          username: ${get.datasource.local.registrationdb.username}
          password: ${get.datasource.local.registrationdb.password}
        appmsodb:
          url: ${get.datasource.local.appmsodb.url}
          username: ${get.datasource.local.appmsodb.username}
          password: ${get.datasource.local.appmsodb.password}
        appmsologdb:
          url: ${get.datasource.local.appmsologdb.url}
          username: ${get.datasource.local.appmsologdb.username}
          password: ${get.datasource.local.appmsologdb.password}
        aisfiledb:
          url: ${get.datasource.local.aisfiledb.url}
          username: ${get.datasource.local.aisfiledb.username}
          password: ${get.datasource.local.aisfiledb.password}
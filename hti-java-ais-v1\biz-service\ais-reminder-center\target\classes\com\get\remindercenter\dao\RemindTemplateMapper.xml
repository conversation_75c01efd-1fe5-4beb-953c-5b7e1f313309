<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.remindercenter.dao.RemindTemplateMapper">
  <insert id="insert" parameterType="com.get.remindercenter.entity.RemindTemplate">
    insert into u_remind_template (id, fk_remind_event_type_key, sms_template, 
      remark, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user, email_template
      )
    values (#{id,jdbcType=BIGINT}, #{fkRemindEventTypeKey,jdbcType=VARCHAR}, #{smsTemplate,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}, #{emailTemplate,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.remindercenter.entity.RemindTemplate">
    insert into u_remind_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkRemindEventTypeKey != null">
        fk_remind_event_type_key,
      </if>
      <if test="smsTemplate != null">
        sms_template,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="emailTemplate != null">
        email_template,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkRemindEventTypeKey != null">
        #{fkRemindEventTypeKey,jdbcType=VARCHAR},
      </if>
      <if test="smsTemplate != null">
        #{smsTemplate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="emailTemplate != null">
        #{emailTemplate,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.remindercenter.entity.RemindTemplate">
    update u_remind_template
    <set>
      <if test="fkRemindEventTypeKey != null">
        fk_remind_event_type_key = #{fkRemindEventTypeKey,jdbcType=VARCHAR},
      </if>
      <if test="smsTemplate != null">
        sms_template = #{smsTemplate,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="emailTemplate != null">
        email_template = #{emailTemplate,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.get.remindercenter.entity.RemindTemplate">
    update u_remind_template
    set fk_remind_event_type_key = #{fkRemindEventTypeKey,jdbcType=VARCHAR},
      sms_template = #{smsTemplate,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      email_template = #{emailTemplate,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.remindercenter.entity.RemindTemplate">
    update u_remind_template
    set fk_remind_event_type_key = #{fkRemindEventTypeKey,jdbcType=VARCHAR},
      sms_template = #{smsTemplate,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
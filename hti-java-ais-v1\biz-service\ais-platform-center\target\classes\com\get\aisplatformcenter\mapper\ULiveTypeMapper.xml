<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.ULiveTypeMapper">

    <resultMap id="BaseResultMap" type="com.get.aisplatformcenterap.entity.ULiveTypeEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
            <result property="typeNameChn" column="type_name_chn" jdbcType="VARCHAR"/>
            <result property="typeKey" column="type_key" jdbcType="VARCHAR"/>
            <result property="viewOrder" column="view_order" jdbcType="INTEGER"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>

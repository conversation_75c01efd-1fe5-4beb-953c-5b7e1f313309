package com.get.pmpcenter.controller.agent;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.dto.agent.AgentCommissionPlanListDto;
import com.get.pmpcenter.dto.agent.InstitutionAgentPlanDto;
import com.get.pmpcenter.dto.agent.TimeOverlapVerifyDto;
import com.get.pmpcenter.dto.agent.UpdateAgentCommissionPlanDto;
import com.get.pmpcenter.dto.common.*;
import com.get.pmpcenter.dto.institution.SaveInstitutionLabelDto;
import com.get.pmpcenter.entity.AgentCommissionPlanApproval;
import com.get.pmpcenter.entity.AgentCommissionType;
import com.get.pmpcenter.mapper.InstitutionCommissionLabelMapper;
import com.get.pmpcenter.service.*;
import com.get.pmpcenter.vo.agent.*;
import com.get.pmpcenter.vo.common.CompanyVo;
import com.get.pmpcenter.vo.common.CountryVo;
import com.get.pmpcenter.vo.common.StaffVo;
import com.get.pmpcenter.vo.institution.CustomizeLabelVo;
import com.get.pmpcenter.vo.institution.InstitutionLabelVo;
import com.get.pmpcenter.vo.institution.ProviderVo;
import io.swagger.annotations.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @DATE: 2020/7/14
 * @TIME: 18:17
 * @Description: 附件管理控制层
 **/

@Api(tags = "代理合同管理")
@RestController
@RequestMapping("/agentContract")
public class AgentContractController {

    @Autowired
    private AgentCommissionPlanCompanyService commissionPlanCompanyService;
    @Autowired
    private AgentCommissionTypeService commissionTypeService;
    @Autowired
    private AgentCommissionPlanService commissionPlanService;
    @Autowired
    private AgentCommissionPlanApprovalService agentApprovalService;
    @Autowired
    private InstitutionCommissionLabelMapper institutionCommissionLabelMapper;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionService planInstitutionService;


    @ApiOperation(value = "分公司列表", notes = "分公司列表")
    @GetMapping("/companyList")
    public ResponseBo<List<CompanyVo>> companyList() {
        return new ResponseBo<>(commissionPlanCompanyService.companyList());
    }

    @ApiOperation(value = "学校提供商列表-代理佣金", notes = "根据分公司ID获取学校提供商列表-代理佣金")
    @GetMapping("/agentProviderList")
    public ResponseBo<List<ProviderVo>> agentProviderList(Long companyId) {
        return new ResponseBo<>(commissionPlanCompanyService.agentProviderList(companyId));
    }

    @ApiOperation(value = "代理商等级列表", notes = "根据分公司ID获取代理商等级列表")
    @GetMapping("/agentCommissionTypeList")
    public ResponseBo<List<AgentCommissionType>> agentCommissionTypeList(Long companyId) {
        return new ResponseBo<>(commissionTypeService.getAgentCommissionTypeList(companyId));
    }

    @ApiOperation(value = "获取代理佣金方案基本信息", notes = "根据代理佣金计划ID获取代理佣金计划基本信息-编辑佣金方案用")
    @GetMapping("/getAgentCommissionPlanBaseInfo/{id}")
    public ResponseBo<AgentCommissionPlanBaseInfoVo> getAgentCommissionPlanBaseInfo(@PathVariable("id") Long id) {
        return new ResponseBo<>(commissionPlanService.getAgentCommissionPlanBaseInfo(id));
    }

    @ApiOperation(value = "获取代理佣金方案列表", notes = "根据学校提供商ID获取代理佣金计划列表")
    @GetMapping("/getAgentCommissionPlanList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "institutionProviderId", value = "学校提供商ID", required = true),
            @ApiImplicitParam(name = "companyId", value = "分公司ID", required = true),
    })
    public ResponseBo<List<AgentCommissionPlanVo>> getAgentCommissionPlanList(Long institutionProviderId, Long companyId) {
        return new ResponseBo<>(commissionPlanService.getAgentCommissionPlanList(institutionProviderId, companyId));
    }

    @ApiOperation(value = "继承模板列表", notes = "根据学校提供商ID和分公司ID获取继承模板列表")
    @GetMapping("/getExtendTemplateList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "institutionProviderId", value = "学校提供商ID", required = true),
            @ApiImplicitParam(name = "companyId", value = "分公司ID", required = true),
    })
    public ResponseBo<List<ExtendTemplateVo>> getExtendTemplateList(Long institutionProviderId, Long companyId) {
        return new ResponseBo<>(commissionPlanService.getExtendTemplateList(institutionProviderId, companyId));
    }

    @ApiOperation(value = "编辑代理佣金方案")
    @PostMapping("/saveAgentCommissionPlan")
    public ResponseBo<String> saveAgentCommissionPlan(@RequestBody @Valid UpdateAgentCommissionPlanDto saveAgentCommissionPlanDto) {
        commissionPlanService.updateAgentCommissionPlan(saveAgentCommissionPlanDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "删除代理佣金方案")
    @PostMapping("/delAgentCommissionPlan")
    public ResponseBo<String> delAgentCommissionPlan(@RequestBody @Valid IdDto idDto) {
        commissionPlanService.delAgentCommissionPlan(idDto.getId());
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "提交代理佣金方案审批")
    @PostMapping("/submitAgentApproval")
    public ResponseBo<String> submitAgentApproval(@RequestBody @Valid SubmitPlanApprovalDto approvalDto) {
        agentApprovalService.submitAgentApproval(approvalDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SUBMIT_SUCCESS", "提交成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "审批代理佣金方案")
    @PostMapping("/approvalAgentProviderPlan")
    public ResponseBo<String> approvalAgentProviderPlan(@RequestBody @Valid ApprovalPlanDto approvalPlanDto) {
        agentApprovalService.approvalAgentProviderPlan(approvalPlanDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_APPROVE_SUCCESS", "审批成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "代理审批人列表", notes = "代理审批人列表")
    @GetMapping("/getAgentStaff")
    public ResponseBo<List<StaffVo>> getAgentStaff() {
        return new ResponseBo<>(agentApprovalService.getAgentStaff());
    }

    @ApiOperation(value = "代理方案审批记录列表", notes = "代理方案审批记录列表")
    @GetMapping("/getAgentApprovalList")
    public ResponseBo<List<AgentCommissionPlanApproval>> getAgentApprovalList(@ApiParam("学校提供商ID") Long institutionProviderId,
                                                                              @ApiParam("分公司ID") Long companyId,
                                                                              @ApiParam("方案ID") Long planId,
                                                                              @ApiParam("方案名称") String planName,
                                                                              @ApiParam("关键字") String keyword) {
        return new ResponseBo<>(agentApprovalService.getAgentApprovalList(institutionProviderId, companyId, planId, planName,keyword));
    }

    @ApiOperation(value = "锁定/解锁代理佣金方案")
    @PostMapping("/lockAgentPlan")
    public ResponseBo<String> lockAgentPlan(@RequestBody @Valid LockPlanDto lockPlanDto) {
        agentApprovalService.lockAgentPlan(lockPlanDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "上下架代理方案")
    @PostMapping("/updateAgentCommissionPlanStatus")
    public ResponseBo<String> updateAgentCommissionPlanStatus(@RequestBody @Valid UpdatePlanStatusDto statusDto) {
        commissionPlanService.updateAgentCommissionPlanStatus(statusDto);
        if (statusDto.getIsActive().equals(1)){
            String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PUBLISH_SUCCESS", "上架成功");
            return new ResponseBo<>(message);
        }
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_UNPUBLISH_SUCCESS", "下架成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "设置首选代理方案", notes = "设置首选代理方案")
    @GetMapping("/setFirst")
    public ResponseBo<String> setFirst() {
        return new ResponseBo<>("保存成功");
    }

    @ApiOperation(value = "代理佣金方案列表")
    @PostMapping("/agentCommissionPlanList")
    public ResponseBo<AgentCommissionPlanListVo> agentCommissionPlanList(@RequestBody SearchBean<AgentCommissionPlanListDto> page) {
        List<AgentCommissionPlanListVo> list = commissionPlanService.agentCommissionPlanList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "国家线列表", notes = "国家列表(国家线列表)")
    @GetMapping("/getAgentCountryList")
    public ResponseBo<List<CountryVo>> getAgentCountryList() {
        return new ResponseBo<>(commissionPlanService.getAgentCountryList());
    }

    @ApiOperation(value = "已读方案最新调整")
    @PostMapping("/readAgentCommissionPlanModify")
    public ResponseBo<String> readAgentCommissionPlanModify(@RequestBody @Valid IdDto idDto) {
        commissionPlanService.readAgentCommissionPlanModify(idDto.getId());
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "自定义标签类型列表", notes = "获取自定义标签类型列表")
    @GetMapping("/getCustomizeLabelType")
    public ResponseBo<List<CustomizeLabelVo>> getCustomizeLabelType() {
        return new ResponseBo<>(institutionCommissionLabelMapper.selectCustomizeLabelType());
    }

    @ApiOperation(value = "自定义标签列表", notes = "获取自定义标签列表")
    @GetMapping("/getCustomizeLabelList")
    public ResponseBo<List<CustomizeLabelVo>> getCustomizeLabelList(@ApiParam("标签类型Id") Long labelTypeId,
                                                                    @Param("标签名称/关键字") String keyword) {
        return new ResponseBo<>(institutionCommissionLabelMapper.selectCustomizeLabelByTypeAndKeyword(labelTypeId, keyword));
    }

    @ApiOperation(value = "保存学校标签")
    @PostMapping("/saveInstitutionLabel")
    public ResponseBo<InstitutionLabelVo> saveInstitutionLabel(@RequestBody SaveInstitutionLabelDto institutionLabelDto) {
        return new ResponseBo<>(planInstitutionService.saveInstitutionLabel(institutionLabelDto));
    }

    @ApiOperation(value = "根据学校获取代理佣金方案")
    @PostMapping("/getInstitutionAgentPlanList")
    public ResponseBo<List<InstitutionAgentPlanListVo>> getInstitutionAgentPlanList(@Valid @RequestBody InstitutionAgentPlanDto agentPlanDto) {
        return new ResponseBo<>(commissionPlanService.getInstitutionAgentPlanList(agentPlanDto));
    }

    @ApiOperation(value = "佣金方案排序", notes = "根据佣金方案ID进行排序,靠前的id放在第一个位,靠后的放在第二位,只能有两个id")
    @PostMapping("/sortAgentPlan")
    public ResponseBo sortAgentPlan(@Valid @RequestBody List<Long> sortList) {
        commissionPlanService.sortAgentPlan(sortList);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "PMP佣金管理/代理佣金管理/关联方案/拖拽")
    @PostMapping("/movingOrder")
    public ResponseBo movingOrder(@RequestParam("start")Integer start, @RequestParam("end")Integer end) {
        commissionPlanService.movingOrder(start,end);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "获取有时间重叠的方案", notes = "保存代理佣金方案前调用,列表有数据说明有重叠的方案")
    @PostMapping("/getTimeOverlapPlanList")
    public ResponseBo<List<TimeOverlapPlanVo>> getTimeOverlapPlanList(@Valid @RequestBody TimeOverlapVerifyDto overlapVerifyDto) {
        return new ResponseBo<>(commissionPlanService.getTimeOverlapPlanList(overlapVerifyDto));
    }

    @ApiOperation(value = "根据学校名称获取佣金方案-AI助手", notes = "根据学校名称获取佣金方案-AI助手-代理佣金")
    @PostMapping("/getInstitutionAgentCommissionList")
    public ResponseBo<List<ExtendAgentCommissionVo>> getInstitutionAgentCommissionList(@Valid @RequestBody AIInstitutionDto aiInstitutionDto) {
        return new ResponseBo<>(commissionPlanService.getInstitutionAgentCommissionList(aiInstitutionDto.getInstitutionName()));
    }
}

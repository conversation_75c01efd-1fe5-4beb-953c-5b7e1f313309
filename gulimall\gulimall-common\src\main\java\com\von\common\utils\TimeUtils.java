package com.von.common.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

/**
 * <AUTHOR>
 * @Date 2025/1/12 11:16
 * @Version 1.0
 */
public class TimeUtils {


    /**
     * 获取当天开始时间
     *
     * @return
     */
    public static DateTime getNowBeginTime() {
        DateTime now = DateUtil.date();
        // 开始时间
        DateTime beginTime = DateUtil.beginOfDay(now);
        return beginTime;
    }

    /**
     * 获取两天后最后的时间
     *
     * @return
     */
    public static DateTime get2DayEndTime() {
        DateTime now = DateUtil.date();
        DateTime offset2Day = DateUtil.offsetDay(now, 2);
        // 结束时间
        DateTime endTime = DateUtil.endOfDay(offset2Day);
        return endTime;
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.BankMapper">
  <insert id="insert" parameterType="com.get.financecenter.entity.Bank">
    insert into u_bank (id, fk_currency_type_num, bank_name, 
      bank_name_international, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCurrencyTypeNum,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{bankNameInternational,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.financecenter.entity.Bank">
    insert into u_bank
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankNameInternational != null">
        bank_name_international,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankNameInternational != null">
        #{bankNameInternational,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="bankSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
     select id,bank_name name from u_bank
     <where>
       <if test="fkCurrencyTypeNum != null">
         and fk_currency_type_num = #{fkCurrencyTypeNum,jdbcType=VARCHAR}
       </if>
     </where>
  </select>
</mapper>
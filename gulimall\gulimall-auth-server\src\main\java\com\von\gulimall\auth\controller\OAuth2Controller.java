package com.von.gulimall.auth.controller;

import com.von.common.config.GulimallSessionConfig;
import com.von.common.config.Knife4jConfiguration;
import com.von.common.constant.AuthServerConstant;
import com.von.common.utils.R;
import com.von.common.vo.MemberRespVo;
import com.von.gulimall.auth.feign.MemberFeignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.session.web.http.CookieSerializer;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpSession;

/**
 * 社交登录
 * TODO : 这里由于认证没通过, 所以暂时只做模拟
 *
 * <AUTHOR>
 * @Date 2024/11/30 20:01
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/oauth2")
@RequiredArgsConstructor
public class OAuth2Controller {


    private final MemberFeignService memberFeignService;


    private final GulimallSessionConfig config;


    private final Knife4jConfiguration configuration;


    private final CookieSerializer cookieSerializer;



    @GetMapping("/weibo/success")
    public R weibo(@RequestParam("code") String code, HttpSession session) throws Exception {
        // TODO 模拟社交登录
//        R r = this.memberFeignService.oauthLogin(new SocialUser());
//        if (r.isOk()) {
//            MemberRespVo memberRespVo = r.getData(new TypeReference<MemberRespVo>() {});
//            log.info("登录成功");
//            session.setAttribute("loginUser", memberRespVo);
//        }

        session.setAttribute(AuthServerConstant.LOGIN_USER, MemberRespVo.builder().username("vonlight").id(1234L).build());
        log.info("{}", cookieSerializer.toString());
        return R.ok();
    }

    /**
     * 退出登录
     *
     * @throws Exception
     */
    @GetMapping("/exit")
    public R exit(HttpSession session) throws Exception {
        session.removeAttribute(AuthServerConstant.LOGIN_USER);
        return R.ok();
    }

}

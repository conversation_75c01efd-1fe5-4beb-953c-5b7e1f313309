package com.von.coupon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.von.common.to.SkuReductionTo;
import com.von.common.utils.PageUtils;
import com.von.coupon.entity.SkuFullReductionEntity;

import java.util.Map;

/**
 * 商品满减信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 23:29:50
 */
public interface SkuFullReductionService extends IService<SkuFullReductionEntity> {

    PageUtils queryPage(Map<String, Object> params);

    void saveSkuReduction(SkuReductionTo skuReductionTo);
}


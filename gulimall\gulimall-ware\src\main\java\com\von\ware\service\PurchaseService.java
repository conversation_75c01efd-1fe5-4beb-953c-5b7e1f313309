package com.von.ware.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.von.common.utils.PageUtils;
import com.von.ware.entity.PurchaseEntity;
import com.von.ware.vo.MergeVo;
import com.von.ware.vo.PurchaseDoneVo;

import java.util.List;
import java.util.Map;

/**
 * 采购信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 23:45:29
 */
public interface PurchaseService extends IService<PurchaseEntity> {

    PageUtils queryPage(Map<String, Object> params);

    PageUtils queryPageUnreceivePurchase(Map<String, Object> params);

    void mergePurchase(MergeVo mergeVo);

    List<PurchaseEntity> test();

    void received(List<Long> idList);

    void done(PurchaseDoneVo purchaseDoneVo);
}


package com.von.gulimall.car.controller;

import com.von.common.utils.R;
import com.von.gulimall.car.interceptor.CartInterceptor;
import com.von.gulimall.car.service.CartService;
import com.von.gulimall.car.vo.Cart;
import com.von.gulimall.car.vo.CartItem;
import com.von.gulimall.car.vo.UserInfoTo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/2 19:17
 * @Version 1.0
 */
@RestController
@RequestMapping("/cart")
@RequiredArgsConstructor
public class CartController {


    private final CartService cartService;

    @GetMapping("/cart")
    public R cart() throws Exception {
        UserInfoTo userInfoTo = CartInterceptor.threadLocal.get();
        return R.ok().setData(userInfoTo);
    }

    /**
     * 添加商品到购物车
     *
     * @param skuId
     * @param num 商品数量
     * @return
     * @throws Exception
     */
    @GetMapping("/addToCart")
    public R addToCart(
            @RequestParam("skuId") Long skuId,
            @RequestParam("num") Integer num
    ) throws Exception {
        CartItem cartItem = this.cartService.addToCart(skuId, num);
        return R.ok().setData(cartItem);
    }

    /**
     * 添加完购物车之后获取当前的购物车对象
     *
     * @param skuId
     * @return
     * @throws Exception
     */
    @GetMapping("/getCartItem")
    public R getCartItem(@RequestParam("skuId") Long skuId) throws Exception {
        CartItem cartItem = this.cartService.getCartItem(skuId);
        return R.ok().setData(cartItem);
    }

    /**
     * 获取全部的购物车对象
     *
     * @throws Exception
     */
    @GetMapping("/cartList")
    public R cartList() throws Exception {
        Cart cart = this.cartService.cartList();

        return R.ok().setData(cart);
    }

    /**
     * 修改选中状态
     *
     * @param skuId
     * @param check
     * @return
     * @throws Exception
     */
    @GetMapping("/checkItem")
    public R checkItem(@RequestParam("skuId") Long skuId, @RequestParam("check") Integer check) throws Exception {
        this.cartService.checkItem(skuId, check);

        return R.ok();
    }


    /**
     * 修改商品数量
     *
     * @param skuId
     * @param num
     * @return
     * @throws Exception
     */
    @PostMapping("/changeItemCount")
    public R changeItemCount(@RequestParam("skuId") Long skuId, @RequestParam("num") Integer num) throws Exception {
        this.cartService.changeItemCount(skuId, num);

        return R.ok();
    }

    @DeleteMapping("/deleteItem/{skuId}")
    public R deleteItem(@PathVariable("skuId") Long skuId) throws Exception {
        this.cartService.deleteItem(skuId);
        return R.ok();
    }

    /**
     * 获取当前用户所有选中的购物车项
     *
     * @return
     */
    @GetMapping("/getCurrentUserCheckCartItems")
    public List<CartItem> getCurrentUserCheckCartItems() {
        return this.cartService.getCurrentUserCheckCartItems();
    }

}

package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.ExpenseClaimFeeTypeMapper;
import com.get.financecenter.dao.ExpenseClaimFormItemMapper;
import com.get.financecenter.dao.ExpenseClaimFormMapper;
import com.get.financecenter.dao.InvoiceMapper;
import com.get.financecenter.dao.PaymentApplicationFormItemMapper;
import com.get.financecenter.dao.PaymentApplicationFormMapper;
import com.get.financecenter.dao.PaymentFeeTypeMapper;
import com.get.financecenter.dao.ProviderMapper;
import com.get.financecenter.dao.TravelClaimFormItemMapper;
import com.get.financecenter.dao.TravelClaimFormMapper;
import com.get.financecenter.dao.VouchItemMapper;
import com.get.financecenter.dao.VouchMapper;
import com.get.financecenter.dao.VouchOperationConfigMapper;
import com.get.financecenter.dao.VouchReceiptRegisterMapper;
import com.get.financecenter.dto.CreateVouchDto;
import com.get.financecenter.dto.VouchDto;
import com.get.financecenter.dto.VouchItemDto;
import com.get.financecenter.entity.AccountingItem;
import com.get.financecenter.entity.ExpenseClaimFeeType;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.entity.PaymentApplicationFormItem;
import com.get.financecenter.entity.PaymentFeeType;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.entity.TravelClaimFormItem;
import com.get.financecenter.entity.Vouch;
import com.get.financecenter.entity.VouchItem;
import com.get.financecenter.entity.VouchOperationConfig;
import com.get.financecenter.entity.VouchReceiptRegister;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.financecenter.enums.VouchTypeEnum;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.service.VouchService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.utils.RelationTargetProcessorUtils;
import com.get.financecenter.vo.VouchItemVo;
import com.get.financecenter.vo.VouchTypeVo;
import com.get.financecenter.vo.VouchVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 凭证业务处理类
 */
@Service("financeVouchService")
public class VouchServiceImpl extends ServiceImpl<VouchMapper, Vouch> implements VouchService {
    @Resource
    private ExpenseClaimFormMapper expenseClaimFormMapper;
    @Resource
    private ExpenseClaimFormItemMapper expenseClaimFormItemMapper;
    @Resource
    private ExpenseClaimFeeTypeMapper expenseClaimFeeTypeMapper;
    @Resource
    private InvoiceMapper invoiceMapper;
    @Resource
    private VouchMapper vouchMapper;
    @Resource
    private VouchItemMapper vouchItemMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private AccountingItemMapper accountingItemMapper;
    @Resource
    private VouchOperationConfigMapper vouchOperationConfigMapper;
    @Resource
    private TravelClaimFormMapper travelClaimFormMapper;
    @Resource
    private PaymentApplicationFormMapper paymentApplicationFormMapper;
    @Resource
    private PaymentApplicationFormItemMapper paymentApplicationFormItemMapper;
    @Autowired
    private TravelClaimFormItemMapper travelClaimFormItemMapper;
    @Resource
    private PaymentFeeTypeMapper paymentFeeTypeMapper;

    @Resource
    private GetAccountingCodeNameUtils getAccountingCodeNameUtils;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private IFinanceCenterClient financeCenterClient;

    @Resource
    private ProviderMapper providerMapper;
    @Resource
    private VouchReceiptRegisterMapper vouchReceiptRegisterMapper;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private RelationTargetProcessorUtils relationTargetProcessorUtils;

    /**
     * 生成凭证
     *
     * @param createVouchDto
     */
    @Override
    @Transactional
    public void createVouch(CreateVouchDto createVouchDto) {
        if (createVouchDto.getFkTableName().equals(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key)) {
            //费用报销单
            List<ExpenseClaimFormItem> expenseClaimFormItemList = expenseClaimFormItemMapper.selectList(Wrappers.<ExpenseClaimFormItem>lambdaQuery()
                    .eq(ExpenseClaimFormItem::getFkExpenseClaimFormId, createVouchDto.getFkTableId()));
            for (ExpenseClaimFormItem expenseClaimFormItem : expenseClaimFormItemList) {
                ExpenseClaimForm expenseClaimForm = expenseClaimFormMapper.selectById(expenseClaimFormItem.getFkExpenseClaimFormId());
                createVouchDto.setNum(expenseClaimForm.getNum());
                Long fkExpenseClaimFeeTypeId = expenseClaimFormItem.getFkExpenseClaimFeeTypeId();
                //费用类型
                ExpenseClaimFeeType expenseClaimFeeType = expenseClaimFeeTypeMapper.selectById(fkExpenseClaimFeeTypeId);
                //借方科目id
                Long drAccountingItemId = expenseClaimFeeType.getFkAccountingItemId();
                List<VouchItemDto> vouchItemDtos = new ArrayList<>();
                VouchItemDto vouchItemDto = new VouchItemDto();
                vouchItemDto.setFkAccountingItemId(drAccountingItemId);
                vouchItemDto.setFkCurrencyTypeNum(expenseClaimFormItem.getFkCurrencyTypeNum());
                vouchItemDto.setAmountDr(expenseClaimFormItem.getAmount());
                vouchItemDto.setRelationTargetKey(expenseClaimFormItem.getRelationTargetKey());
                vouchItemDto.setRelationTargetId(expenseClaimFormItem.getRelationTargetId());
                vouchItemDtos.add(vouchItemDto);

                //贷方
                VouchOperationConfig vouchOperationConfig = vouchOperationConfigMapper.selectOne(Wrappers.<VouchOperationConfig>lambdaQuery().eq(VouchOperationConfig::getOperationKey, "ECFCreateVouchCrAccounting"));
                if (GeneralTool.isEmpty(vouchOperationConfig)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("NO_VOUCH_OPERATION_CONFIG"));
                }
                //贷方科目
                Long crAccountingItemId = vouchOperationConfig.getFkAccountingItemId();
                vouchItemDto = new VouchItemDto();
                vouchItemDto.setFkAccountingItemId(crAccountingItemId);
                vouchItemDto.setFkCurrencyTypeNum(expenseClaimFormItem.getFkCurrencyTypeNum());
                vouchItemDto.setAmountCr(expenseClaimFormItem.getAmount());
                vouchItemDto.setRelationTargetKey(expenseClaimFormItem.getRelationTargetKey());
                vouchItemDto.setRelationTargetId(expenseClaimFormItem.getRelationTargetId());
                vouchItemDtos.add(vouchItemDto);

                //创建凭证
                Vouch vouch = new Vouch();
                vouch.setVouchType("转");
                vouch.setBusinessDate(expenseClaimForm.getGmtCreate());
                Long vouchId = createVouch(createVouchDto, vouch, vouchItemDtos);
                expenseClaimForm.setFkVouchId(vouchId);
                expenseClaimForm.setIsVouchCreated(true);
                expenseClaimForm.setDateVouchCreated(new Date());
                expenseClaimForm.setFkStaffIdVouchCreated(SecureUtil.getStaffId());
                utilService.updateUserInfoToEntity(expenseClaimForm);
                expenseClaimFormMapper.updateById(expenseClaimForm);
            }
        } else if (createVouchDto.getFkTableName().equals(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)) {
            //差旅报销单
            TravelClaimForm travelClaimForm = travelClaimFormMapper.selectById(createVouchDto.getFkTableId());
            createVouchDto.setNum(travelClaimForm.getNum());
            List<TravelClaimFormItem> travelClaimFormItemList = travelClaimFormItemMapper.selectList(Wrappers.<TravelClaimFormItem>lambdaQuery().eq(TravelClaimFormItem::getFkTravelClaimFormId, createVouchDto.getFkTableId()));
            TravelClaimFormItem travelClaimFormItem = travelClaimFormItemList.get(0);
            //报销金额累加
            BigDecimal amount = travelClaimFormItemList.stream().map(TravelClaimFormItem::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

            VouchOperationConfig vouchOperationConfig = vouchOperationConfigMapper.selectOne(Wrappers.<VouchOperationConfig>lambdaQuery().eq(VouchOperationConfig::getOperationKey, "TCFCreateVouchDrAccounting"));
            if (GeneralTool.isEmpty(vouchOperationConfig)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("NO_VOUCH_OPERATION_CONFIG"));
            }
            //关联类型Key（目标类型表名）
            String relationTargetKey = null;
            //关联类型Id（目标类型表对应记录项Id）
            Long relationTargetId = null;
            if (GeneralTool.isNotEmpty(vouchOperationConfig.getRelationTargetKey()) && vouchOperationConfig.getRelationTargetKey().equals(TableEnum.PERMISSION_STAFF.key)) {
                relationTargetKey = TableEnum.PERMISSION_STAFF.key;
                relationTargetId = travelClaimForm.getFkStaffId();
            }
            List<VouchItemDto> vouchItemDtos = new ArrayList<>();
            //借方科目id
            Long drAccountingItemId = vouchOperationConfig.getFkAccountingItemId();
            VouchItemDto vouchItemDto = new VouchItemDto();
            vouchItemDto.setFkAccountingItemId(drAccountingItemId);
            vouchItemDto.setFkCurrencyTypeNum(travelClaimFormItem.getFkCurrencyTypeNum());
            vouchItemDto.setAmountDr(amount);
            vouchItemDto.setRelationTargetKey(relationTargetKey);
            vouchItemDto.setRelationTargetId(relationTargetId);
            vouchItemDtos.add(vouchItemDto);

            //贷方
            vouchOperationConfig = vouchOperationConfigMapper.selectOne(Wrappers.<VouchOperationConfig>lambdaQuery().eq(VouchOperationConfig::getOperationKey, "TCFCreateVouchCrAccounting"));
            if (GeneralTool.isEmpty(vouchOperationConfig)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("NO_VOUCH_OPERATION_CONFIG"));
            }
            //贷方科目
            Long crAccountingItemId = vouchOperationConfig.getFkAccountingItemId();
            vouchItemDto = new VouchItemDto();
            vouchItemDto.setFkAccountingItemId(crAccountingItemId);
            vouchItemDto.setFkCurrencyTypeNum(travelClaimFormItem.getFkCurrencyTypeNum());
            vouchItemDto.setAmountCr(amount);
            vouchItemDto.setRelationTargetKey(relationTargetKey);
            vouchItemDto.setRelationTargetId(relationTargetId);
            vouchItemDtos.add(vouchItemDto);

            //创建凭证
            Vouch vouch = new Vouch();
            vouch.setVouchType("转");
            vouch.setBusinessDate(travelClaimForm.getGmtCreate());
            Long vouchId = createVouch(createVouchDto, vouch, vouchItemDtos);
            travelClaimForm.setFkVouchId(vouchId);
            travelClaimForm.setIsVouchCreated(true);
            travelClaimForm.setDateVouchCreated(new Date());
            travelClaimForm.setFkStaffIdVouchCreated(SecureUtil.getStaffId());
            utilService.updateUserInfoToEntity(travelClaimForm);
            travelClaimFormMapper.updateById(travelClaimForm);
        } else if (createVouchDto.getFkTableName().equals(TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key)) {
            //支付申请单
            PaymentApplicationForm paymentApplicationForm = paymentApplicationFormMapper.selectById(createVouchDto.getFkTableId());
            createVouchDto.setNum(paymentApplicationForm.getNum());
            List<PaymentApplicationFormItem> paymentApplicationFormItems = paymentApplicationFormItemMapper.selectList(Wrappers.<PaymentApplicationFormItem>lambdaQuery().eq(PaymentApplicationFormItem::getFkPaymentApplicationFormId, paymentApplicationForm.getId()));
            List<VouchItemDto> vouchItemDtos = new ArrayList<>();
            for (PaymentApplicationFormItem paymentApplicationFormItem : paymentApplicationFormItems) {
                //借方
                Long fkPaymentFeeTypeId = paymentApplicationFormItem.getFkPaymentFeeTypeId();
                PaymentFeeType paymentFeeType = paymentFeeTypeMapper.selectById(fkPaymentFeeTypeId);
                //借方科目Id
                Long drAccountingItemId = paymentFeeType.getFkAccountingItemId();
                VouchItemDto vouchItemDto = new VouchItemDto();
                vouchItemDto.setFkAccountingItemId(drAccountingItemId);
                vouchItemDto.setFkCurrencyTypeNum(paymentApplicationFormItem.getFkCurrencyTypeNum());
                vouchItemDto.setAmountDr(paymentApplicationFormItem.getAmount());
                vouchItemDto.setRelationTargetKey(paymentApplicationFormItem.getRelationTargetKey());
                vouchItemDto.setRelationTargetId(paymentApplicationFormItem.getRelationTargetId());
                vouchItemDtos.add(vouchItemDto);

                //贷方
                VouchOperationConfig vouchOperationConfig = vouchOperationConfigMapper.selectOne(Wrappers.<VouchOperationConfig>lambdaQuery().eq(VouchOperationConfig::getOperationKey, "PMFCreateVouchCrAccounting"));
                if (GeneralTool.isEmpty(vouchOperationConfig)) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("NO_VOUCH_OPERATION_CONFIG"));
                }
                //贷方科目id
                Long crAccountingItemId = vouchOperationConfig.getFkAccountingItemId();
                vouchItemDto = new VouchItemDto();
                vouchItemDto.setFkAccountingItemId(crAccountingItemId);
                vouchItemDto.setFkCurrencyTypeNum(paymentApplicationFormItem.getFkCurrencyTypeNum());
                vouchItemDto.setAmountCr(paymentApplicationFormItem.getAmount());
                vouchItemDto.setRelationTargetKey(paymentApplicationFormItem.getRelationTargetKey());
                vouchItemDto.setRelationTargetId(paymentApplicationFormItem.getRelationTargetId());
                vouchItemDtos.add(vouchItemDto);
            }
            //创建凭证
            Vouch vouch = new Vouch();
            vouch.setVouchType("转");
            vouch.setBusinessDate(paymentApplicationForm.getGmtCreate());
            Long vouchId = createVouch(createVouchDto, vouch, vouchItemDtos);
            paymentApplicationForm.setFkVouchId(vouchId);
            paymentApplicationForm.setIsVouchCreated(true);
            paymentApplicationForm.setDateVouchCreated(new Date());
            paymentApplicationForm.setFkStaffIdVouchCreated(SecureUtil.getStaffId());
            utilService.updateUserInfoToEntity(paymentApplicationForm);
            paymentApplicationFormMapper.updateById(paymentApplicationForm);
        }


    }

    /**
     * 创建凭证
     */
    @Transactional
    public Long createVouch(CreateVouchDto createVouchDto, Vouch vouch, List<VouchItemDto> vouchItemDtos) {
        //创建凭证
        //凭证号：银付-202504-000279
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        //凭证月份
        String vouchMonth = now.format(formatter);
        int maxVouchQty = invoiceMapper.getMaxVouchQty(vouchMonth);
        //凭证当月数量累计（6位补0）
        String vouchQty = String.format("%06d", maxVouchQty);
        String vouchNum = VouchTypeEnum.getValueByCode(vouch.getVouchType()) + "-" + vouchMonth + "-" + vouchQty;
        vouch.setVouchNum(vouchNum);
        vouch.setVouchType(VouchTypeEnum.getValueByCode(vouch.getVouchType()));
        vouch.setVouchMonth(vouchMonth);
        vouch.setVouchQty(maxVouchQty);
        if (GeneralTool.isNotEmpty(createVouchDto)) {
            StringBuilder remarkStr = new StringBuilder();
            if (GeneralTool.isNotEmpty(createVouchDto.getFkTableName())) {
                if (createVouchDto.getFkTableName().equals(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key)) {
                    remarkStr.append("创建费用报销单凭证，表单号[");
                } else if (createVouchDto.getFkTableName().equals(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)) {
                    remarkStr.append("创建差旅报销单凭证，表单号[");
                } else if (createVouchDto.getFkTableName().equals(TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key)) {
                    remarkStr.append("创建支付申请单凭证，表单号[");
                }
                remarkStr.append(createVouchDto.getNum()).append("]，申请人[").append(SecureUtil.getStaffName()).append("] ");
                if (GeneralTool.isNotEmpty(vouch.getRemark())) {
                    remarkStr.append(vouch.getRemark());
                }
                vouch.setRemark(remarkStr.toString());
            }
        }
        vouch.setFkCompanyId(SecureUtil.getFkCompanyId());
        vouch.setCreateType("system");
        vouch.setIsActive(true);
        utilService.setCreateInfo(vouch);
        vouchMapper.insert(vouch);
        //新增 凭证明细
        for (VouchItemDto vouchItemDto : vouchItemDtos) {
            VouchItem vouchItem = BeanCopyUtils.objClone(vouchItemDto, VouchItem::new);
            AccountingItem accountingItem = accountingItemMapper.selectById(vouchItemDto.getFkAccountingItemId());
            //子凭证
            vouchItem.setFkVouchId(vouch.getId());
            vouchItem.setSummary(accountingItem.getCodeName());
            utilService.setCreateInfo(vouchItem);
            vouchItemMapper.insert(vouchItem);
        }
        return vouch.getId();
    }


    /**
     * 获取凭证类型
     *
     * @return
     */
    @Override
    public List<VouchTypeVo> getVouchType() {
        return VouchTypeEnum.getOptions();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(VouchDto vouchDto) {
        if (GeneralTool.isEmpty(vouchDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(vouchDto.getBusinessDate())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("business_date_null"));
        }
//        if (GeneralTool.isEmpty(vouchDto.getFkCompanyId())){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
//        }
        if (GeneralTool.isEmpty(vouchDto.getVouchItemList()) || (vouchDto.getVouchItemList().size() == 0) || (vouchDto.getVouchItemList().size() % 2 != 0)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("vouch_item_null"));
        }

        Vouch vouch = BeanCopyUtils.objClone(vouchDto, Vouch::new);
        Long id = createVouch(null, vouch, vouchDto.getVouchItemList());

        return id;

    }

    /**
     * 获取列表数据
     *
     * @param vouchDto
     * @param page
     * @return
     */
    @Override
    public List<VouchVo> getVouchs(VouchDto vouchDto, Page page) {

        LambdaQueryWrapper<Vouch> wrapper = new LambdaQueryWrapper();
        IPage<Vouch> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<VouchVo> vouchs = vouchMapper.getVouchs(pages, vouchDto);
        //凭证明细
        List<VouchItem> vouchItems = vouchItemMapper.selectList(new LambdaQueryWrapper<VouchItem>());
        List<VouchItemVo> vouchItemVos = vouchItems.stream().map(vouchItem -> BeanCopyUtils.objClone(vouchItem, VouchItemVo::new)).collect(Collectors.toList());

        //根据getFkVouchId分组
        Map<Long, List<VouchItemVo>> vouchItemMap = vouchItemVos.stream().collect(Collectors.groupingBy(VouchItemVo::getFkVouchId));

        page.setAll((int) pages.getTotal());
        //公司名数据
        Map<Long, String> companyNamesByIds = new HashMap<>();

        if (GeneralTool.isNotEmpty(vouchs)) {
            //公司ids
            Set<Long> companyIds = vouchs.stream().map(VouchVo::getFkCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());

            Result<Map<Long, String>> result1 = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                companyNamesByIds = result1.getData();
            }

        }
        for (VouchVo vouchVo : vouchs) {
            if (GeneralTool.isNotEmpty(vouchVo.getFkCompanyId())) {
                vouchVo.setCompanyName(companyNamesByIds.get(vouchVo.getFkCompanyId()));
            }
            vouchVo.setVouchItemVos(vouchItemMap.get(vouchVo.getId()));
        }
        return vouchs;


    }

    @Override
    public VouchVo getVouchsById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Vouch vouch = vouchMapper.selectById(id);
        //公司名数据
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(vouch)) {
            //公司ids
            Set<Long> companyIds = new HashSet<>();
            if (GeneralTool.isNotEmpty(vouch.getFkCompanyId())) {
                companyIds.add(vouch.getFkCompanyId());
            }

            Result<Map<Long, String>> result1 = permissionCenterClient.getCompanyNamesByIds(companyIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                companyNamesByIds = result1.getData();
            }

        }
        VouchVo vouchVo = BeanCopyUtils.objClone(vouch, VouchVo::new);
        vouchVo.setIsActive(vouch.getIsActive()? 1:0);
        vouchVo.setCompanyName(companyNamesByIds.get(vouchVo.getFkCompanyId()));
        //获取凭证明细

        List<VouchItem> vouchItems = vouchItemMapper.selectList(new LambdaQueryWrapper<VouchItem>().eq(VouchItem::getFkVouchId, id));

        if (GeneralTool.isNotEmpty(vouchItems)) {
            //币种nums
            Map<String, String> currencyNamesByNums = new HashMap<>();
            Set<String> currencyTypeNums = vouchItems.stream().map(VouchItem::getFkCurrencyTypeNum).filter(GeneralTool::isNotEmpty).collect(Collectors.toSet());

            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                currencyNamesByNums = result.getData();
            }
            List<VouchItemVo> vouchItemVos = vouchItems.stream().map(vouchItem -> BeanCopyUtils.objClone(vouchItem, VouchItemVo::new)).collect(Collectors.toList());

            for (VouchItemVo vouchItemVo : vouchItemVos) {
                if (GeneralTool.isNotEmpty(vouchItemVo.getFkCurrencyTypeNum())) {
                    vouchItemVo.setCurrencyTypeName(currencyNamesByNums.get(vouchItemVo.getFkCurrencyTypeNum()));
                }
                if (GeneralTool.isNotEmpty(vouchItemVo.getFkAccountingItemId())) {
                    vouchItemVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(vouchItemVo.getFkAccountingItemId()));
                }
                if (GeneralTool.isNotEmpty(vouchItemVo.getRelationTargetKey())) {
                    vouchItemVo.setRelationTargetName(RelationTargetKeyEnum.getNameByRelationTargetKey(vouchItemVo.getRelationTargetKey()));
                }

                if (GeneralTool.isNotEmpty(vouchItemVo.getRelationTargetId())) {
//                    if (vouchItemVo.getRelationTargetKey().equals(RelationTargetKeyEnum.STAFF.relationTargetKey)) {
//                        StaffVo staffVo = permissionCenterClient.getStaffById(vouchItemVo.getRelationTargetId()).getData();
//                        vouchItemVo.setName(staffVo.getFullName());
//                        vouchItemVo.setRelationTargetDrCompanyId(staffVo.getFkCompanyId());
//                        vouchItemVo.setRelationTargetCrCompanyId(staffVo.getFkCompanyId());
//                    } else if (vouchItemVo.getRelationTargetKey().equals(RelationTargetKeyEnum.PROVIDER.relationTargetKey)) {
//                        Provider provider = providerMapper.selectById(vouchItemVo.getRelationTargetId());
//                        vouchItemVo.setName(provider.getName());
//                        vouchItemVo.setRelationTargetDrCompanyId(provider.getFkCompanyId());
//                        vouchItemVo.setRelationTargetCrCompanyId(provider.getFkCompanyId());
//                    } else if (vouchItemVo.getRelationTargetKey().equals(RelationTargetKeyEnum.STUDENT.relationTargetKey)) {
//                        Student student = saleCenterClient.getStudentById(vouchItemVo.getRelationTargetId()).getData();
//                        vouchItemVo.setName(student.getName());
//                        vouchItemVo.setRelationTargetDrCompanyId(student.getFkCompanyId());
//                        vouchItemVo.setRelationTargetCrCompanyId(student.getFkCompanyId());
//                    }else if (vouchItemVo.getRelationTargetKey().equals(RelationTargetKeyEnum.STUDENT_SERVICE_FEE.relationTargetKey)) {
//                        StudentServiceFeeSummaryVo studentServiceFeeSummaryVo = saleCenterClient.getServiceFeeNumById(vouchItemVo.getRelationTargetId()).getData();
//                        vouchItemVo.setName(studentServiceFeeSummaryVo.getServiceFeeNum());
//                        vouchItemVo.setRelationTargetDrCompanyId(studentServiceFeeSummaryVo.getTargetCompanyNameId());
//                        vouchItemVo.setRelationTargetCrCompanyId(studentServiceFeeSummaryVo.getTargetCompanyNameId());
//                    }
                    relationTargetProcessorUtils.processRelationTarget(
                            vouchItemVo.getRelationTargetKey(),
                            vouchItemVo.getRelationTargetId(),
                            name -> vouchItemVo.setName(name),
                            companyId -> {
                                vouchItemVo.setRelationTargetDrCompanyId(companyId);
                                vouchItemVo.setRelationTargetCrCompanyId(companyId);
                            },null);
                }

            }
            vouchVo.setVouchItemVos(vouchItemVos);
        }

        return vouchVo;
    }

    @Override
    public Long delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Vouch vouch = vouchMapper.selectById(id);
        if (GeneralTool.isEmpty(vouch)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        // TODO  检查凭证是否被使用
        List<VouchReceiptRegister> vouchReceiptRegisters = vouchReceiptRegisterMapper.selectList(new LambdaQueryWrapper<VouchReceiptRegister>().eq(VouchReceiptRegister::getFkVouchId, id));
        if (GeneralTool.isNotEmpty(vouchReceiptRegisters)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_used_by_vouch_receipt_register"));
        }

        //删除凭证明细
        int delete = vouchItemMapper.delete(new LambdaQueryWrapper<VouchItem>().eq(VouchItem::getFkVouchId, id));
        if (delete > 0) {
            vouchMapper.deleteById(id);
        }
        return vouch.getId();

    }

    @Override
    public VouchVo updateById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Vouch vouch = vouchMapper.selectById(id);
        if (GeneralTool.isEmpty(vouch)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        // TODO  检查凭证是否被使用
        List<VouchReceiptRegister> vouchReceiptRegisters = vouchReceiptRegisterMapper.selectList(new LambdaQueryWrapper<VouchReceiptRegister>().eq(VouchReceiptRegister::getFkVouchId, id));
        if (GeneralTool.isNotEmpty(vouchReceiptRegisters)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("cancel_obj_used_by_vouch_receipt_register"));
        }
        //修改凭证状态
        Vouch vouchUpdate = new Vouch();
        LambdaUpdateWrapper<Vouch> vouchLambdaUpdateWrapper = new LambdaUpdateWrapper<Vouch>();
        vouchLambdaUpdateWrapper.eq(Vouch::getId, id);
        vouchLambdaUpdateWrapper.eq(Vouch::getIsActive, true);
        vouchUpdate.setIsActive(false);
        utilService.setUpdateInfo(vouchUpdate);
        vouchMapper.update(vouchUpdate,vouchLambdaUpdateWrapper);
        return getVouchsById(id);
    }

    @Override
    public VouchVo updateByVouchId(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Vouch vouch = vouchMapper.selectById(id);
        if (GeneralTool.isEmpty(vouch)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //修改凭证状态
        Vouch vouchUpdate = new Vouch();
        LambdaUpdateWrapper<Vouch> vouchLambdaUpdateWrapper = new LambdaUpdateWrapper<Vouch>();
        vouchLambdaUpdateWrapper.eq(Vouch::getId, id);
        vouchLambdaUpdateWrapper.eq(Vouch::getIsActive, true);
        vouchUpdate.setIsActive(false);
        utilService.setUpdateInfo(vouchUpdate);
        vouchMapper.update(vouchUpdate,vouchLambdaUpdateWrapper);
        return getVouchsById(id);
    }


}

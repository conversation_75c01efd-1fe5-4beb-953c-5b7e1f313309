package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.InvoiceReceivablePlanMapper;
import com.get.financecenter.dao.PayablePlanSettlementInstallmentMapper;
import com.get.financecenter.dao.PayablePlanSettlementStatusMapper;
import com.get.financecenter.dao.PaymentFormItemMapper;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.financecenter.entity.PayablePlanSettlementStatus;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.vo.PrepaymentButtonHtiVo;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.financecenter.service.IInvoiceReceivablePlanService;
import com.get.financecenter.dto.BatchUpdateAmountDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.dto.PrepaymentButtonHtiDto;
import com.get.salecenter.vo.ReceivablePlanVo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/4/27 12:37
 * @verison: 1.0
 * @description:
 */
@Service
public class InvoiceReceivablePlanServiceImpl extends BaseServiceImpl<InvoiceReceivablePlanMapper, InvoiceReceivablePlan> implements IInvoiceReceivablePlanService {

    @Resource
    private InvoiceReceivablePlanMapper invoiceReceivablePlanMapper;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IExchangeRateService exchangeRateService;
    @Resource
    private PayablePlanSettlementStatusMapper payablePlanSettlementStatusMapper;
    @Resource
    private PaymentFormItemMapper paymentFormItemMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchUpdateAmount(List<BatchUpdateAmountDto> batchUpdateAmountDtos) throws GetServiceException{
        if (GeneralTool.isEmpty(batchUpdateAmountDtos)){
            return;
        }
//        if (interfaceConfiguration.equals("HTI")) {
//            List<InvoiceReceivablePlan> invoiceReceivablePlans = invoiceReceivablePlanMapper.selectBatchIds(batchUpdateAmountDtos.stream().map(BatchUpdateAmountDto::getId).collect(Collectors.toList()));
//            List<Long> fkInvoiceReceivablePlanIds = invoiceReceivablePlans.stream().filter(invoiceReceivablePlan -> invoiceReceivablePlan.getIsPayInAdvance() != null && invoiceReceivablePlan.getIsPayInAdvance()).map(InvoiceReceivablePlan::getId).collect(Collectors.toList());
//            if (GeneralTool.isNotEmpty(fkInvoiceReceivablePlanIds)) {
//                Result<Boolean> result = saleCenterClient.checkHtiCommissionInSettlement(fkInvoiceReceivablePlanIds);
//                if (!result.isSuccess()) {
//                    throw new GetServiceException(result.getMessage());
//                }
//                if (result.getData()) {
//                    throw new GetServiceException(LocaleMessageUtils.getMessage("INVOICE_PREPAYMENT_SETTLEMENT_IN_PROGRESS"));
//                }
//            }
//        }
        List<PrepaymentButtonHtiDto> prepaymentButtonHtiDtoList = new ArrayList<>();
        for (BatchUpdateAmountDto batchUpdateAmountDto : batchUpdateAmountDtos) {
            InvoiceReceivablePlan invoiceReceivablePlan = invoiceReceivablePlanMapper.selectById(batchUpdateAmountDto.getId());
            invoiceReceivablePlan.setAmount(batchUpdateAmountDto.getAmount());
            utilService.setUpdateInfo(invoiceReceivablePlan);
            invoiceReceivablePlanMapper.updateById(invoiceReceivablePlan);

            if (GeneralTool.isNotEmpty(invoiceReceivablePlan.getIsPayInAdvance()) && invoiceReceivablePlan.getIsPayInAdvance()) {
                PrepaymentButtonHtiDto prepaymentButtonHtiDto = new PrepaymentButtonHtiDto();
                prepaymentButtonHtiDto.setFkReceivablePlanId(invoiceReceivablePlan.getFkReceivablePlanId());
                prepaymentButtonHtiDto.setPayInAdvancePercent(invoiceReceivablePlan.getPayInAdvancePercent());
                prepaymentButtonHtiDto.setFkInvoiceId(invoiceReceivablePlan.getFkInvoiceId());
                prepaymentButtonHtiDto.setSubtotal(invoiceReceivablePlan.getAmount());
                prepaymentButtonHtiDto.setInvoiceReceivablePlanRelationId(invoiceReceivablePlan.getId());
                prepaymentButtonHtiDtoList.add(prepaymentButtonHtiDto);
            }
        }
        //发票绑定金额变动，预付更新
        for (PrepaymentButtonHtiDto prepaymentButtonHtiDto : prepaymentButtonHtiDtoList) {
            Result<PayablePlan> result = saleCenterClient.getPayableInfoById(prepaymentButtonHtiDto.getFkReceivablePlanId());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            PayablePlan plan = result.getData();
            if (GeneralTool.isEmpty(plan)) {
                continue;
            }

            LambdaQueryWrapper<PayablePlanSettlementInstallment> wrapper = Wrappers.<PayablePlanSettlementInstallment>lambdaQuery().eq(PayablePlanSettlementInstallment::getFkInvoiceReceivablePlanId, prepaymentButtonHtiDto.getInvoiceReceivablePlanRelationId()).eq(PayablePlanSettlementInstallment::getStatus, ProjectExtraEnum.INSTALLMENT_UNTREATED.key).eq(PayablePlanSettlementInstallment::getStatusSettlement, ProjectExtraEnum.UNSETTLED.key);
            List<PayablePlanSettlementInstallment> payablePlanSettlementInstallments = payablePlanSettlementInstallmentMapper.selectList(wrapper);
            if (GeneralTool.isNotEmpty(payablePlanSettlementInstallments)) {
                payablePlanSettlementInstallmentMapper.delete(wrapper);
                Result<ReceivablePlanVo> result1 = saleCenterClient.getReceivablePlanById(prepaymentButtonHtiDto.getFkReceivablePlanId());
                if (!result1.isSuccess()) {
                    throw new GetServiceException(result1.getMessage());
                }
                ReceivablePlanVo receivablePlan = result1.getData();
                BigDecimal amountPaid = paymentFormItemMapper.getAmountPaidByPayablePlanId(plan.getId());
                amountPaid = amountPaid.add(payablePlanSettlementInstallmentMapper.getAmountPaidByPayablePlanId(plan.getId()));
                if (amountPaid.compareTo(plan.getPayableAmount()) == 0) {
                    continue;
                }

                Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.PAY_IN_ADVANCE_SERVICE_FEE.key, 1).getData();
                //应收金额
                BigDecimal receivableAmount = receivablePlan.getReceivableAmount();
                //应收币种
                String fkCurrencyTypeNum = receivablePlan.getFkCurrencyTypeNum();
                //应收金额折合成应付币种的应收金额
                BigDecimal receivableExchangeAmount;
                //统一将全部金额转成应付计划币种进行公式计算
                if (fkCurrencyTypeNum.equals(plan.getFkCurrencyTypeNum())) {
                    receivableExchangeAmount = receivableAmount;
                } else {
                    BigDecimal lastExchangeRate = exchangeRateService.getLastExchangeRate(false, fkCurrencyTypeNum, plan.getFkCurrencyTypeNum()).getExchangeRate();
                    receivableExchangeAmount = receivableAmount.multiply(lastExchangeRate).setScale(2, RoundingMode.HALF_UP);
                }
                BigDecimal proportion = plan.getPayableAmount().divide(receivableExchangeAmount, 12, RoundingMode.DOWN);
                //本次预付金额
                BigDecimal amountActual = prepaymentButtonHtiDto.getSubtotal().multiply(proportion).multiply(prepaymentButtonHtiDto.getPayInAdvancePercent().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
                String configValue1 = companyConfigMap.get(plan.getFkCompanyId());
                BigDecimal serviceFee = new BigDecimal(configValue1);
                amountActual = amountActual.subtract(serviceFee);

                PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
                payablePlanSettlementInstallment.setFkPayablePlanId(plan.getId());
                payablePlanSettlementInstallment.setAmountExpect(amountActual);
                payablePlanSettlementInstallment.setAmountActual(amountActual);
                payablePlanSettlementInstallment.setAmountActualInit(amountActual);
                payablePlanSettlementInstallment.setServiceFeeExpect(serviceFee);
                payablePlanSettlementInstallment.setServiceFeeActual(serviceFee);
                payablePlanSettlementInstallment.setServiceFeeActualInit(serviceFee);
                payablePlanSettlementInstallment.setFkInvoiceId(prepaymentButtonHtiDto.getFkInvoiceId());
                payablePlanSettlementInstallment.setFkInvoiceReceivablePlanId(prepaymentButtonHtiDto.getInvoiceReceivablePlanRelationId());
                payablePlanSettlementInstallment.setRollBack(false);
                payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
                utilService.setCreateInfo(payablePlanSettlementInstallment);
                payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);

                PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
                payablePlanSettlementStatus.setFkPayablePlanId(plan.getId());
                payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
                payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                utilService.setCreateInfo(payablePlanSettlementStatus);
                payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);

                invoiceReceivablePlanMapper.update(null, Wrappers.<InvoiceReceivablePlan>lambdaUpdate().eq(InvoiceReceivablePlan::getId, prepaymentButtonHtiDto.getInvoiceReceivablePlanRelationId()).set(InvoiceReceivablePlan::getPayInAdvanceAmount, amountActual));
            }
        }


//        Set<Long> invoiceIdset = invoiceReceivablePlanVos.stream().map(InvoiceReceivablePlanVo::getFkInvoiceId).collect(Collectors.toSet());
//        if (GeneralTool.isEmpty(invoiceIdset)){
//            invoiceIdset.add(0L);
//        }
//        Set<Long> planIdset = invoiceReceivablePlanVos.stream().map(InvoiceReceivablePlanVo::getFkReceivablePlanId).collect(Collectors.toSet());
//        if (GeneralTool.isEmpty(planIdset)){
//            planIdset.add(0L);
//        }
//        //删除关系表
//        invoiceReceivablePlanMapper.delete(Wrappers.<InvoiceReceivablePlan>lambdaQuery()
//                .in(InvoiceReceivablePlan::getFkInvoiceId, invoiceIdset)
//                .in(InvoiceReceivablePlan::getFkReceivablePlanId, planIdset));
//        List<InvoiceReceivablePlan> invoiceReceivablePlans = BeanCopyUtils.copyListProperties(invoiceReceivablePlanVos, InvoiceReceivablePlan::new);
//        for (InvoiceReceivablePlan invoiceReceivablePlan : invoiceReceivablePlans) {
//            utilService.updateUserInfoToEntity(invoiceReceivablePlan);
//        }
//        boolean b = saveBatch(invoiceReceivablePlans);
//        if (!b){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
//        }

    }


    /**
     * 应收绑定发票
     * @param receivablePlanId
     * @param amount
     * @param invoiceId
     */
    @Override
    public void receivableBindingInvoice(Long receivablePlanId, BigDecimal amount, Long invoiceId) {
        InvoiceReceivablePlan invoiceReceivablePlan = new InvoiceReceivablePlan();
        invoiceReceivablePlan.setFkInvoiceId(invoiceId);
        invoiceReceivablePlan.setFkReceivablePlanId(receivablePlanId);
        invoiceReceivablePlan.setAmount(amount);
        utilService.setCreateInfo(invoiceReceivablePlan);
        invoiceReceivablePlanMapper.insert(invoiceReceivablePlan);
    }

    /**
     * 获取佣金通知信息
     * @param fkInvoiceId
     * @return
     */
    @Override
    public Map<Long, String> getInvoiceCommissionNotice(Long fkInvoiceId) {
        List<InvoiceReceivablePlan> invoiceReceivablePlans = invoiceReceivablePlanMapper.selectList(Wrappers.<InvoiceReceivablePlan>lambdaQuery().eq(InvoiceReceivablePlan::getFkInvoiceId, fkInvoiceId));
        return invoiceReceivablePlans.stream().collect(HashMap<Long, String>::new, (m, v) -> m.put(v.getFkReceivablePlanId(), v.getCommissionNotice()), HashMap::putAll);
    }

    @Override
    public Boolean updateInvoiceReceivablePlan(Long invoiceId, Long receivablePlanId, BigDecimal amount) {
        InvoiceReceivablePlan invoiceReceivablePlan = new InvoiceReceivablePlan();
        invoiceReceivablePlan.setFkInvoiceId(invoiceId);
        invoiceReceivablePlan.setFkReceivablePlanId(receivablePlanId);
        invoiceReceivablePlan.setAmount(amount);
        utilService.setUpdateInfo(invoiceReceivablePlan);
        int update = invoiceReceivablePlanMapper.update(invoiceReceivablePlan, Wrappers.lambdaUpdate(InvoiceReceivablePlan.class)
                .eq(InvoiceReceivablePlan::getFkInvoiceId, invoiceId)
                .eq(InvoiceReceivablePlan::getFkReceivablePlanId, receivablePlanId)
        );

        return update>0;
    }

    /**
     * HTI预付
     *
     * @Date 18:17 2024/6/14
     * <AUTHOR>
     */
    @Override
    @Transactional
    public Boolean prepaymentButtonHti(List<PrepaymentButtonHtiVo> prepaymentButtonHtiDtoList) {
        for (PrepaymentButtonHtiVo prepaymentButtonHtiVo : prepaymentButtonHtiDtoList) {
            InvoiceReceivablePlan invoiceReceivablePlan = invoiceReceivablePlanMapper.selectById(prepaymentButtonHtiVo.getInvoiceReceivablePlanRelationId());
            invoiceReceivablePlan.setIsPayInAdvance(true);
            invoiceReceivablePlan.setPayInAdvancePercent(prepaymentButtonHtiVo.getPercentage());
            invoiceReceivablePlan.setPayInAdvanceAmount(prepaymentButtonHtiVo.getPayInAdvanceAmount());
            utilService.setUpdateInfo(invoiceReceivablePlan);
            invoiceReceivablePlanMapper.updateById(invoiceReceivablePlan);
        }
        return true;
    }



    @Override
    public Map<Long, InvoiceReceivablePlan> getInvoiceAmountByIds(List<Long> fkInvoiceReceivablePlanIds) {
        Map<Long, InvoiceReceivablePlan> map = new HashMap<>();
        List<InvoiceReceivablePlan> invoiceReceivablePlans = invoiceReceivablePlanMapper.selectBatchIds(fkInvoiceReceivablePlanIds);
        for (InvoiceReceivablePlan invoiceReceivablePlan : invoiceReceivablePlans) {
            map.put(invoiceReceivablePlan.getId(), invoiceReceivablePlan);
        }
        return map;
    }

    /**
     * 更新发票绑定金额
     *
     * @Date 13:36 2024/6/21
     * <AUTHOR>
     */
    @Override
    public Boolean updateInvoiceReceivablePlanAmount(Map<Long, BigDecimal> map) {
        map.forEach((k, v) -> {
            InvoiceReceivablePlan invoiceReceivablePlan = invoiceReceivablePlanMapper.selectById(k);
            invoiceReceivablePlan.setPayInAdvanceAmount(v);
            utilService.setUpdateInfo(invoiceReceivablePlan);
            invoiceReceivablePlanMapper.updateById(invoiceReceivablePlan);
        });
        return true;
    }


    @Override
    public InvoiceReceivablePlan getInvoiceReceivablePlanById(Long fkInvoiceReceivablePlanId) {
        InvoiceReceivablePlan invoiceReceivablePlan = invoiceReceivablePlanMapper.selectById(fkInvoiceReceivablePlanId);
        if (GeneralTool.isNotEmpty(invoiceReceivablePlan)){
            return invoiceReceivablePlan;
        }
        return null;
    }

}

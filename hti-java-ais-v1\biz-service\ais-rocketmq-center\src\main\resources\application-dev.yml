server:
  port: 8104
#数据源配置
spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: reminderdb
      datasource:
        reminderdb:
          url: ${get.datasource.dev.reminderdb.url}
          username: ${get.datasource.dev.reminderdb.username}
          password: ${get.datasource.dev.reminderdb.password}


rocketmq:
  # 配置 NameServer 地址
  name-server: 192.168.2.28:9876
  # 生产者分组
  producer:
    group: mail_system_queue_topic_group
    # 发送超时时间（毫秒）
    send-message-timeout: 3000
    # 生产者发送失败的最大重试次数
    retry-times-when-send-failed: 3
  consumer:
    # 消费者分组
    group: mail_system_queue_topic_group
    # 消息最大重试次数（超出后进入死信队列）
    max-reconsume-times: 3
    # 开启消息轨迹
    enable-msg-trace: true
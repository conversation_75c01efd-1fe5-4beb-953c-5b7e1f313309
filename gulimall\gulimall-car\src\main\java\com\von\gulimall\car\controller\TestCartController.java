package com.von.gulimall.car.controller;

import com.alibaba.fastjson.TypeReference;
import com.von.common.utils.R;
import com.von.gulimall.car.feign.ProductFeignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/3 22:09
 * @Version 1.0
 */

@RequestMapping("/test")
@RestController
@Slf4j
@RequiredArgsConstructor
public class TestCartController {


    private final ProductFeignService productFeignService;

    @GetMapping("/test1")
    public R test1() throws Exception {
        R skuSaleAttrValuesBySkuId = this.productFeignService.getSkuSaleAttrValuesBySkuId(1L);
        List<String> data = skuSaleAttrValuesBySkuId.getData(new TypeReference<List<String>>() {
        });
        log.info("测试");
        return R.ok().setData(data);
    }

}

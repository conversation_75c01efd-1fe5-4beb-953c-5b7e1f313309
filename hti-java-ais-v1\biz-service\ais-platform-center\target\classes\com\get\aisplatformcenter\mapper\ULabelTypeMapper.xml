<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.ULabelTypeMapper">

<!--    //排序-->
    <select id="getMaxOrder" resultType="java.lang.Integer">
        select
            IFNULL(max(view_order)+1,0) view_order
        from
            u_label_type
    </select>



    <select id="getExportLabelTypeInfo" resultType="com.get.aisplatformcenterap.vo.ULabelTypeExportVo">
        SELECT
               type.id AS id,
               type.type_name AS typeName,
               type.type_key AS typeKey,
               type.remark AS remark,
               label.id AS labelId,
               label.label_name AS labelName,
               label.label_key AS labelKey,
               label.remark AS labelRemark
               FROM u_label_type  AS type LEFT JOIN u_label AS label ON type.id = label.fk_label_type_id
            <where>
               <if test="uLabelTypeDto.typeName!=null and uLabelTypeDto.typeName!=''">
                   AND type.type_name LIKE CONCAT('%',#{uLabelTypeDto.typeName},'%')
               </if>
                <if test="uLabelTypeDto.typeKey!=null and uLabelTypeDto.typeKey!=''">
                    AND type.type_key LIKE CONCAT('%',#{uLabelTypeDto.typeKey},'%')
                </if>
            </where>
    </select>


</mapper>

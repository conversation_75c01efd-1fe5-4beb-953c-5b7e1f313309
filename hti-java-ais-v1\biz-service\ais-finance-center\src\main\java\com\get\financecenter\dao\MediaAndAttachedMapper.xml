<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.MediaAndAttachedMapper">
    <resultMap id="BaseResultMap" type="com.get.financecenter.entity.FMediaAndAttached">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_file_guid" jdbcType="VARCHAR" property="fkFileGuid"/>
        <result column="fk_table_name" jdbcType="VARCHAR" property="fkTableName"/>
        <result column="fk_table_id" jdbcType="BIGINT" property="fkTableId"/>
        <result column="type_key" jdbcType="VARCHAR" property="typeKey"/>
        <result column="index_key" jdbcType="INTEGER" property="indexKey"/>
        <result column="link" jdbcType="VARCHAR" property="link" />
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_file_guid, fk_table_name, fk_table_id, type_key, index_key, remark, gmt_create, 
    gmt_create_user, gmt_modified, gmt_modified_user, link
  </sql>

    <insert id="insertSelective" parameterType="com.get.financecenter.entity.FMediaAndAttached" keyProperty="id"
            useGeneratedKeys="true">
        insert into s_media_and_attached
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkFileGuid != null">
                fk_file_guid,
            </if>
            <if test="fkTableName != null">
                fk_table_name,
            </if>
            <if test="fkTableId != null">
                fk_table_id,
            </if>
            <if test="typeKey != null">
                type_key,
            </if>
            <if test="indexKey != null">
                index_key,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="link != null">
                link,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkFileGuid != null">
                #{fkFileGuid,jdbcType=VARCHAR},
            </if>
            <if test="fkTableName != null">
                #{fkTableName,jdbcType=VARCHAR},
            </if>
            <if test="fkTableId != null">
                #{fkTableId,jdbcType=BIGINT},
            </if>
            <if test="typeKey != null">
                #{typeKey,jdbcType=VARCHAR},
            </if>
            <if test="indexKey != null">
                #{indexKey,jdbcType=INTEGER},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="link != null">
                #{link,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getNextIndexKey" resultType="java.lang.Integer">
    select max(index_key)+1 from s_media_and_attached where fk_table_id=#{tableId} and fk_table_name=#{tableName}
  </select>
    <select id="getMediaAndAttachedList" resultType="com.get.financecenter.vo.FMediaAndAttachedVo">
select * from s_media_and_attached where fk_table_id=#{fkTableId}
</select>

</mapper>
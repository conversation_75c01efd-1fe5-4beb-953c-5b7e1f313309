package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.OtherTypeMapper;
import com.get.resumecenter.dto.OtherTypeDto;
import com.get.resumecenter.entity.OtherType;
import com.get.resumecenter.service.IDeleteService;
import com.get.resumecenter.service.IOtherTypeService;
import com.get.resumecenter.vo.OtherTypeVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 15:49
 * @Description:
 **/
@Service
public class OtherTypeServiceImpl implements IOtherTypeService {
    @Resource
    private OtherTypeMapper otherTypeMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private UtilService utilService;

    @Override
    public List<OtherTypeVo> datas(OtherTypeDto otherTypeDto) {
//        Example example = new Example(OtherType.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<OtherType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(otherTypeDto)) {
            if (GeneralTool.isNotEmpty(otherTypeDto.getTypeName())) {
//                criteria.andEqualTo("typeName", otherTypeDto.getTypeName());
                lambdaQueryWrapper.eq(OtherType::getTypeName, otherTypeDto.getTypeName());
            }
            lambdaQueryWrapper.orderByDesc(OtherType::getViewOrder);
        }
        List<OtherType> otherTypes = otherTypeMapper.selectList(lambdaQueryWrapper);
        List<OtherTypeVo> convertDatas = new ArrayList<>();
        for (OtherType otherType : otherTypes) {
            OtherTypeVo otherTypeVo = BeanCopyUtils.objClone(otherType, OtherTypeVo::new);
            convertDatas.add(otherTypeVo);
        }
        return convertDatas;
    }

    @Override
    public OtherTypeVo findOtherTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null")) ;
        }
        OtherType otherType = otherTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(otherType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        OtherTypeVo otherTypeVo = BeanCopyUtils.objClone(otherType, OtherTypeVo::new);
        return otherTypeVo;
    }

    @Override
    public OtherTypeVo updateOtherType(OtherTypeDto otherTypeDto) {
        if (otherTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(otherTypeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        OtherType rs = this.otherTypeMapper.selectById(otherTypeDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        OtherType otherType = BeanCopyUtils.objClone(otherTypeDto, OtherType::new);
        if (validateUpdate(otherTypeDto)) {
            utilService.updateUserInfoToEntity(otherType);
            otherTypeMapper.updateById(otherType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findOtherTypeById(otherType.getId());
    }

    @Override
    public Long addOtherType(OtherTypeDto otherTypeDto) {
        OtherType otherType = BeanCopyUtils.objClone(otherTypeDto, OtherType::new);
        if (validateAdd(otherTypeDto)) {
            otherType.setViewOrder(otherTypeMapper.getMaxViewOrder());
            utilService.updateUserInfoToEntity(otherType);
            otherTypeMapper.insert(otherType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return otherType.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        OtherTypeVo otherType = findOtherTypeById(id);
        if (otherType == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        deleteService.deleteValidateOtherType(id);
        int i = otherTypeMapper.deleteById(otherType.getId());
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<OtherTypeDto> otherTypeDtos) {
        for (OtherTypeDto otherTypeDto : otherTypeDtos) {
            if (GeneralTool.isEmpty(otherTypeDto.getId())) {
                if (validateAdd(otherTypeDto)) {
                    OtherType otherType = BeanCopyUtils.objClone(otherTypeDto, OtherType::new);
                    utilService.updateUserInfoToEntity(otherType);
                    otherType.setViewOrder(otherTypeMapper.getMaxViewOrder());
                    otherTypeMapper.insert(otherType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(otherTypeDto)) {
                    OtherType otherType = BeanCopyUtils.objClone(otherTypeDto, OtherType::new);
                    utilService.updateUserInfoToEntity(otherType);
                    otherTypeMapper.updateById(otherType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }


        }

    }

    @Override
    public List<BaseSelectEntity> getOtherTypeSelect() {
        //下拉
        return otherTypeMapper.getOtherTypeSelect();
    }

    @Override
    public void movingOrder(List<OtherTypeDto> otherTypeDtos) {
        if (GeneralTool.isEmpty(otherTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        OtherType otherType = BeanCopyUtils.objClone(otherTypeDtos.get(0), OtherType::new);
        OtherType otherType2 = BeanCopyUtils.objClone(otherTypeDtos.get(1), OtherType::new);

        Integer viewOrder = otherType.getViewOrder();
        otherType.setViewOrder(otherType2.getViewOrder());
        otherType2.setViewOrder(viewOrder);

        otherTypeMapper.updateById(otherType);
        otherTypeMapper.updateById(otherType2);

    }

    private boolean validateAdd(OtherTypeDto otherTypeDto) {
//        Example example = new Example(OtherType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", otherTypeDto.getTypeName());
        List<OtherType> list = this.otherTypeMapper.selectList(Wrappers.<OtherType>lambdaQuery().eq(OtherType::getTypeName, otherTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateAdd(List<OtherTypeDto> otherTypeDtos) {
        boolean success = true;
        for (OtherTypeDto otherTypeDto : otherTypeDtos) {
//            Example example = new Example(OtherType.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("typeName", otherTypeDto.getTypeName());
//            List<OtherType> list = this.otherTypeMapper.selectByExample(example);

            List<OtherType> list = this.otherTypeMapper.selectList(Wrappers.<OtherType>lambdaQuery().eq(OtherType::getTypeName, otherTypeDto.getTypeName()));
            if (!GeneralTool.isEmpty(list)) {
                success = false;
            }
        }
        return success;
    }

    private boolean validateUpdate(OtherTypeDto otherTypeDto) {
//        Example example = new Example(OtherType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", otherTypeDto.getTypeName());
//        List<OtherType> list = this.otherTypeMapper.selectByExample(example);
        List<OtherType> list = this.otherTypeMapper.selectList(Wrappers.<OtherType>lambdaQuery().eq(OtherType::getTypeName, otherTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(otherTypeDto.getId());
    }
}

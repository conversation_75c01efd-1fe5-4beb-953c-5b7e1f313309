package com.von.coupon.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.von.common.utils.PageUtils;
import com.von.coupon.entity.CouponSpuRelationEntity;

import java.util.Map;

/**
 * 优惠券与产品关联
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 23:29:50
 */
public interface CouponSpuRelationService extends IService<CouponSpuRelationEntity> {

    PageUtils queryPage(Map<String, Object> params);
}


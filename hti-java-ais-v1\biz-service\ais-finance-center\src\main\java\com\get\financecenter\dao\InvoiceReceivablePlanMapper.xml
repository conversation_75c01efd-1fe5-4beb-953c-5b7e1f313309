<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.InvoiceReceivablePlanMapper">

  <insert id="addMappingInvoiceReceivablePlan">
        INSERT INTO  r_invoice_receivable_plan(fk_invoice_id,fk_receivable_plan_id,gmt_create,gmt_create_user,gmt_modified,gmt_modified_user)
        VALUES
            <foreach collection="list" item="item" separator=",">
              (
                    #{item.fkInvoiceId},
                    #{item.fkReceivablePlanId},
                    #{item.gmtCreate},
                    #{item.gmtCreateUser},
                    #{item.gmtModified},
                    #{item.gmtModifiedUser}
              )
            </foreach>

  </insert>
  <select id="getInvoicesByPlanIds" resultType="com.get.financecenter.vo.InvoiceVo">
      SELECT
        rirp.fk_receivable_plan_id as fkReceivablePlanId ,
        mi.*
      FROM
        r_invoice_receivable_plan AS rirp
          LEFT JOIN
        m_invoice AS mi
        ON
          rirp.fk_invoice_id = mi.id
      where 1=1
      <if test="planIds != null and planIds.size()>0">
      And rirp.fk_receivable_plan_id in
        <foreach collection="planIds" index="index" item="planId" open="(" separator="," close=")">
          #{planId}
        </foreach>
      </if>
    </select>
    <select id="getReceivablePlanByInvoiceId" resultType="com.get.financecenter.entity.InvoiceReceivablePlan">
          SELECT
              r.*
          FROM
              r_invoice_receivable_plan r
          INNER JOIN ais_sale_center.m_receivable_plan p ON p.id = r.fk_receivable_plan_id
          WHERE
              r.fk_invoice_id = #{invoiceId}
          AND p.`status` = 1
    </select>
    <select id="getInvoiceBingDingAmount" resultType="java.math.BigDecimal">
        SELECT
            SUM(amount)
        FROM
            r_invoice_receivable_plan
        WHERE
            1=1
            <if test="invoiceIds!=null and invoiceIds.size>0">
                AND fk_invoice_id IN
                <foreach collection="invoiceIds" separator="," open="(" close=")" item="cid">
                    #{cid}
                </foreach>
            </if>
    </select>
    <select id="getInvoiceNumByReceivableId" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            a.fk_receivable_plan_id as keyId,
            GROUP_CONCAT(b.num ORDER BY b.gmt_create DESC) AS val
        FROM
            ais_finance_center.r_invoice_receivable_plan a
        LEFT JOIN ais_finance_center.m_invoice b ON a.fk_invoice_id = b.id
        WHERE
            a.fk_receivable_plan_id IN
            <foreach collection="ids" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        GROUP BY a.fk_receivable_plan_id
    </select>
    <select id="getReceivablePlanById" resultType="com.get.salecenter.entity.ReceivablePlan">
        SELECT * FROM ais_sale_center.m_receivable_plan WHERE id = #{receivablePlanId}
    </select>
    <select id="getPrepaidAmount" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(IFNULL(rirp.pay_in_advance_amount, 0)),0)
        FROM
            m_receipt_form AS mrf
                INNER JOIN r_receipt_form_invoice AS rrfi ON rrfi.fk_receipt_form_id = mrf.id
                INNER JOIN r_invoice_receivable_plan AS rirp ON rirp.fk_invoice_id = rrfi.fk_invoice_id
        WHERE
            mrf.id = #{fkReceiptFormId}
          AND rirp.fk_receivable_plan_id = #{fkReceivablePlanId}
          AND rirp.is_pay_in_advance = 1
        GROUP BY
            rirp.fk_receivable_plan_id
    </select>
    <delete id="deleteInvoiceReceivablePlan">
    DELETE FROM r_invoice_receivable_plan WHERE fk_receivable_plan_id IN
    <foreach collection="planIds" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </delete>
  <delete id="deleteInvoiceReceivablePlanByInvoiceId">
    DELETE FROM r_invoice_receivable_plan WHERE fk_invoice_id = #{invoiceId}
  </delete>
</mapper>
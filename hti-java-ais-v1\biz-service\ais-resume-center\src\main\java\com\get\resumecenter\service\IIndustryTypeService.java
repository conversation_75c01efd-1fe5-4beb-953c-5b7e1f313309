package com.get.resumecenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.resumecenter.vo.IndustryTypeVo;
import com.get.resumecenter.dto.IndustryTypeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 10:52
 * @Description: 行业类型业务类
 **/
public interface IIndustryTypeService {
    /**
     * @return java.util.List<com.get.resumecenter.vo.IndustryTypeVo>
     * @Description: 列表数据
     * @Param [industryTypeDto]
     * <AUTHOR>
     */
    List<IndustryTypeVo> datas(IndustryTypeDto industryTypeDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    IndustryTypeVo findIndustryTypeById(Long id);

    /**
     * 修改
     *
     * @param industryTypeDto
     * @return
     */
    IndustryTypeVo updateIndustryType(IndustryTypeDto industryTypeDto);

    /**
     * 保存
     *
     * @param industryTypeDto
     * @return
     */
    Long addIndustryType(IndustryTypeDto industryTypeDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param industryTypeDtos
     * @return
     */
    void batchAdd(List<IndustryTypeDto> industryTypeDtos);

    /**
     * @return java.lang.String
     * @Description: 根据id查询名称
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    String getNameByTypeId(Long industryTypeId);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getIndustryTypeSelect();

    /**
     * @return void
     * @Description: 上移下移
     * @Param [industryTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<IndustryTypeDto> industryTypeDtos);
}

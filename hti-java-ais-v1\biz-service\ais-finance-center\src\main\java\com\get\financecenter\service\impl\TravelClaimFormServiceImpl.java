package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.TravelClaimFeeTypeMapper;
import com.get.financecenter.dao.TravelClaimFormItemMapper;
import com.get.financecenter.dao.TravelClaimFormMapper;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.TravelClaimFormDto;
import com.get.financecenter.dto.TravelClaimFormItemDto;
import com.get.financecenter.dto.query.TravelClaimFormQueryDto;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import com.get.financecenter.entity.TravelClaimFeeType;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.entity.TravelClaimFormItem;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.service.TravelClaimFormItemService;
import com.get.financecenter.service.TravelClaimFormService;
import com.get.financecenter.utils.MyStringUtils;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.TravelClaimFeeTypeVo;
import com.get.financecenter.vo.TravelClaimFormItemVo;
import com.get.financecenter.vo.TravelClaimFormVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 差旅报销申请单
 */
@Service
public class TravelClaimFormServiceImpl extends BaseServiceImpl<TravelClaimFormMapper, TravelClaimForm> implements TravelClaimFormService {

    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private TravelClaimFormItemService travelClaimFormItemService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private TravelClaimFormMapper travelClaimFormMapper;
    @Resource
    private TravelClaimFormItemMapper travelClaimFormItemMapper;
    @Resource
    private TravelClaimFeeTypeMapper travelClaimFeeTypeMapper;
    @Resource
    private UtilService utilService;

    /**
     * 查询差旅报销申请单
     *
     * @param travelClaimFormQueryDto
     * @param page
     * @return
     */
    @Override
    public List<TravelClaimFormVo> getTravelClaimForms(TravelClaimFormQueryDto travelClaimFormQueryDto, SearchBean<TravelClaimFormQueryDto> page) {
        if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(travelClaimFormQueryDto.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        //111111 待修改
        Result<Map<Long, Integer>> result = workflowCenterClient.getFromIdsByStaffId(SecureUtil.getStaffId(), TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<TravelClaimForm> expenseClaimForms = getTravelClaimFormsByExample(travelClaimFormQueryDto, page, result.getData());
        List<TravelClaimFormVo> convertDatas = new ArrayList<>();
        //公司id集合
        Set<Long> companyIds = new HashSet<>();
        //部门id集合
        Set<Long> departmentIds = new HashSet<>();
        //报销人id集合
        Set<Long> staffIds = new HashSet<>();
        //报销单id集合
        List<Long> expenseClaimFormIds = new ArrayList<>();
        //获取各自集合的值
        for (TravelClaimForm travelClaimForm : expenseClaimForms) {
            companyIds.add(travelClaimForm.getFkCompanyId());
            departmentIds.add(travelClaimForm.getFkDepartmentId());
            staffIds.add(travelClaimForm.getFkStaffId());
            expenseClaimFormIds.add(travelClaimForm.getId());
        }
        //feign调用 获取公司id-name的map
        companyIds.removeIf(Objects::isNull);
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        //feign调用 获取部门id-name的map
        departmentIds.removeIf(Objects::isNull);
        Map<Long, String> departmentNameMap = permissionCenterClient.getDepartmentNamesByIds(departmentIds).getData();
        //feign调用 获取报销人id-name的map
        staffIds.removeIf(Objects::isNull);
        Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIds).getData();
        //feign调用 获取流程方面dto 报销单id-actRuTaskDot的map
        expenseClaimFormIds.removeIf(Objects::isNull);
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key, expenseClaimFormIds);
        Result<Map<Long, ActRuTaskVo>> result1 = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = result1.getData();
        //币种编号nums
        Set<String> currencyTypeNums = expenseClaimForms.stream().map(TravelClaimForm::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        }

        for (TravelClaimForm travelClaimForm : expenseClaimForms) {
            TravelClaimFormVo travelClaimFormVo = BeanCopyUtils.objClone(travelClaimForm, TravelClaimFormVo::new);
            travelClaimFormVo.setDepartmentName("");
            travelClaimFormVo
                    //公司名称
                    .setCompanyName(companyNameMap.get(travelClaimFormVo.getFkCompanyId()))
                    //部门名称
                    .setDepartmentName(departmentNameMap.get(travelClaimFormVo.getFkDepartmentId()))
                    //申请人名称
                    .setStaffName(staffNameMap.get(travelClaimFormVo.getFkStaffId()))
                    //表单明细对象
                    .setTravelClaimFormItemDtoList(travelClaimFormItemService.getDtoByTravelClaimFormId(travelClaimFormVo.getId()))
                    //币种名称
                    .setCurrencyTypeName(currencyNamesByNums.get(travelClaimFormVo.getFkCurrencyTypeNum()))
                    //报销单状态
                    .setExpenseClaimFormStatus(result.getData().get(travelClaimFormVo.getId()));
            //流程对象
            ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(travelClaimFormVo.getId());
            //正在进行的任务id
            if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
                travelClaimFormVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
            }
            //流程实例id
            if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
                travelClaimFormVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
            }
            //任务版本
            if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
                travelClaimFormVo.setTaskVersion(actRuTaskVo.getTaskVersion());
            }
            //获取报销金额总和
            BigDecimal amountSum = getAmountSum(travelClaimFormVo);
            travelClaimFormVo.setAmountSum(amountSum);
            convertDatas.add(travelClaimFormVo);
        }
        return convertDatas;
    }

    /**
     * 新增差旅报销申请单
     *
     * @param travelClaimFormDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addTravelClaimForm(TravelClaimFormDto travelClaimFormDto) {
        if (GeneralTool.isEmpty(travelClaimFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        TravelClaimForm travelClaimForm = BeanCopyUtils.objClone(travelClaimFormDto, TravelClaimForm::new);
        travelClaimForm.setFkStaffId(SecureUtil.getStaffId());
        //待发起状态
        travelClaimForm.setStatus(0);
        travelClaimForm.setIsVouchCreated(false);
        utilService.updateUserInfoToEntity(travelClaimForm);
        travelClaimFormMapper.insert(travelClaimForm);
        //获取表单编号并设置
        String expenseClaimFormNum = MyStringUtils.getFormNum("TCF", travelClaimForm.getId());
        travelClaimForm.setNum(expenseClaimFormNum);
        travelClaimFormMapper.updateById(travelClaimForm);

        Long id = travelClaimForm.getId();
        //保存表单明细内容
        if (GeneralTool.isNotEmpty(travelClaimFormDto.getTravelClaimFormItemDtos())) {
            for (TravelClaimFormItemDto travelClaimFormItemDto : travelClaimFormDto.getTravelClaimFormItemDtos()) {
                TravelClaimFormItem travelClaimFormItem = BeanCopyUtils.objClone(travelClaimFormItemDto, TravelClaimFormItem::new);
                travelClaimFormItem.setFkTravelClaimFormId(id);
                utilService.updateUserInfoToEntity(travelClaimFormItem);
                travelClaimFormItemMapper.insert(travelClaimFormItem);
            }
        }
        return travelClaimForm.getId();
    }

    /**
     * 差旅报销费用类型下拉框数据
     *
     * @return
     */
    @Override
    public List<TravelClaimFeeTypeVo> getTravelClaimFeeTypeSelect() {
        List<TravelClaimFeeType> travelClaimFeeTypes = travelClaimFeeTypeMapper.selectList(Wrappers.<TravelClaimFeeType>query().lambda()
                .orderByDesc(TravelClaimFeeType::getViewOrder));
        return travelClaimFeeTypes.stream().map(travelClaimFeeType -> BeanCopyUtils.objClone(travelClaimFeeType, TravelClaimFeeTypeVo::new)).collect(Collectors.toList());
    }

    /**
     * 更新差旅报销申请单
     *
     * @param travelClaimFormDto
     */
    @Override
    public void updateTravelClaimForm(TravelClaimFormDto travelClaimFormDto) {
        if (GeneralTool.isEmpty(travelClaimFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        TravelClaimForm travelClaimForm = BeanCopyUtils.objClone(travelClaimFormDto, TravelClaimForm::new);
        utilService.updateUserInfoToEntity(travelClaimForm);
        travelClaimFormMapper.updateById(travelClaimForm);

        //保存表单明细内容(先删后增)
        travelClaimFormItemMapper.delete(Wrappers.<TravelClaimFormItem>query().lambda().eq(TravelClaimFormItem::getFkTravelClaimFormId, travelClaimForm.getId()));
        //保存表单明细内容
        if (GeneralTool.isNotEmpty(travelClaimFormDto.getTravelClaimFormItemDtos())) {
            for (TravelClaimFormItemDto travelClaimFormItemDto : travelClaimFormDto.getTravelClaimFormItemDtos()) {
                TravelClaimFormItem travelClaimFormItem = BeanCopyUtils.objClone(travelClaimFormItemDto, TravelClaimFormItem::new);
                travelClaimFormItem.setFkTravelClaimFormId(travelClaimForm.getId());
                utilService.updateUserInfoToEntity(travelClaimFormItem);
                travelClaimFormItemMapper.insert(travelClaimFormItem);
            }
        }
    }

    /**
     * 差旅报销申请单详情
     *
     * @param id
     * @return
     */
    @Override
    public TravelClaimFormVo findTravelClaimFormById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        TravelClaimForm travelClaimForm = travelClaimFormMapper.selectById(id);
        if (GeneralTool.isEmpty(travelClaimForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //111111 修改为feign接口调用
        Result<Map<Long, Integer>> result = workflowCenterClient.getFromIdsByStaffId(SecureUtil.getStaffId(), TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key);
        Map<Long, Integer> map = result.getData();
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key, Collections.singletonList(id));
        Result<Map<Long, ActRuTaskVo>> result1 = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = result1.getData();
        TravelClaimFormVo travelClaimFormVo = BeanCopyUtils.objClone(travelClaimForm, TravelClaimFormVo::new);
        travelClaimFormVo
                //公司名称
                .setCompanyName(permissionCenterClient.getCompanyNameById(travelClaimFormVo.getFkCompanyId()).getData())
                //部门名称
                .setDepartmentName(permissionCenterClient.getDepartmentNameById(travelClaimFormVo.getFkDepartmentId()).getData())
                //报销人名称
                .setStaffName(permissionCenterClient.getStaffName(travelClaimFormVo.getFkStaffId()).getData())
                //币种名称
                .setCurrencyTypeName(currencyTypeService.getCurrencyNameByNum(travelClaimFormVo.getFkCurrencyTypeNum()))
                .setFkStaffIdVouchCreatedName(permissionCenterClient.getStaffName(travelClaimFormVo.getFkStaffIdVouchCreated()).getData());

        //报销单状态
        if (GeneralTool.isEmpty(map.get(travelClaimFormVo.getId()))) {
            //我的申请列表和所有表单列表点详情进去的话，该表单又不属于登录人 需要给这个
            travelClaimFormVo.setExpenseClaimFormStatus(2);
        } else {
            travelClaimFormVo.setExpenseClaimFormStatus(map.get(travelClaimFormVo.getId()));
        }
        //流程对象信息
        ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(id);
        if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
            travelClaimFormVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
        }
        if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
            travelClaimFormVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
        }
        if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
            travelClaimFormVo.setTaskVersion(actRuTaskVo.getTaskVersion());
        }
        //表单明细对象集合
        List<TravelClaimFormItemVo> travelClaimFormList = travelClaimFormItemService.getDtoByTravelClaimFormId(id);
        travelClaimFormVo.setTravelClaimFormItemDtoList(travelClaimFormList);
        //报销总金额
        travelClaimFormVo.setAmountSum(getAmountSum(travelClaimFormVo));

        Result<HiCommentFeignVo> result2 = workflowCenterClient.getHiComment(id, TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key);
        if (!result2.isSuccess()) {
            throw new GetServiceException(result2.getMessage());
        }
        HiCommentFeignVo hiComment = result2.getData();
        if (GeneralTool.isNotEmpty(hiComment)) {
            travelClaimFormVo.setAgreeButtonType(hiComment.getAgreeButtonType());
            travelClaimFormVo.setRefuseButtonType(hiComment.getRefuseButtonType());
        } else {
            travelClaimFormVo.setAgreeButtonType(false);
            travelClaimFormVo.setRefuseButtonType(false);
        }
        return travelClaimFormVo;
    }

    /**
     * 查询差旅报销申请单附件
     * @param attachedVo
     * @param page
     * @return
     */
    @Override
    public List<FMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    /**
     * 保存差旅报销申请单附件
     * @param mediaAttachedVos
     * @return
     */
    @Override
    public List<FMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }

    /**
     * 开启差旅报销申请单流程
     * @param companyId
     * @param businessKey
     * @param procdefKey
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startProcess(Long companyId, Long businessKey, String procdefKey) {
        TravelClaimForm travelClaimForm = travelClaimFormMapper.selectById(businessKey);
        if (0 != travelClaimForm.getStatus()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
        }
        Map<String, Object> map = new HashMap<>();
        map.put("applyUser", GetAuthInfo.getStaffId());
        //开启流程后，更新表单状态
        Boolean result = workflowCenterClient.startProcess(String.valueOf(businessKey), procdefKey, String.valueOf(companyId), map).getData();
        if (!result) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
        }
        travelClaimForm.setStatus(2);
        utilService.updateUserInfoToEntity(travelClaimForm);
        travelClaimFormMapper.updateById(travelClaimForm);
    }

    /**
     * 差旅报销申请单作废
     * @param id
     */
    @Override
    public void updateStatus(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        TravelClaimForm travelClaimForm = travelClaimFormMapper.selectById(id);
        if (GeneralTool.isEmpty(travelClaimForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (0 != travelClaimForm.getStatus()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("process_already_start_cannot_Invalid"));
        }
        travelClaimForm.setStatus(5);
        travelClaimFormMapper.updateById(travelClaimForm);
    }

    /**
     * 差旅报销申请单撤单
     * @param id
     * @param summary
     */
    @Override
    public void getRevokeExpenseClaimForm(Long id, String summary) {
        TravelClaimForm travelClaimForm =  travelClaimFormMapper.selectById(id);
        if (GeneralTool.isEmpty(travelClaimForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (GeneralTool.isNotEmpty(travelClaimFormMapper.getExistParentId(id))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        List<TravelClaimFormItem> travelClaimFormItemList =  travelClaimFormItemMapper.selectList(Wrappers.<TravelClaimFormItem>lambdaQuery().eq(TravelClaimFormItem::getFkTravelClaimFormId, id));
        if (GeneralTool.isEmpty(travelClaimFormItemList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        TravelClaimForm revocationForm;
        revocationForm = travelClaimForm;
        revocationForm.setId(null);
        revocationForm.setStatus(ProjectExtraEnum.TO_BE_INITIATED.key);
        revocationForm.setGmtModified(null);
        revocationForm.setGmtModifiedUser(null);
        revocationForm.setFkTravelClaimFormIdRevoke(id);
        utilService.updateUserInfoToEntity(revocationForm);
        travelClaimFormMapper.insert(revocationForm);
        //获取表单编号并设置
        String revocationFormNum = MyStringUtils.getFormNum("TCF",revocationForm.getId());
        revocationForm.setNum(revocationFormNum);
        travelClaimFormMapper.updateById(revocationForm);

        for (TravelClaimFormItem travelClaimFormItem : travelClaimFormItemList) {
            TravelClaimFormItem formItem;
            formItem = travelClaimFormItem;
            formItem.setId(null);
            formItem.setGmtModified(null);
            formItem.setGmtModifiedUser(null);
            formItem.setSummary(summary);
            formItem.setFkTravelClaimFormId(revocationForm.getId());
            utilService.updateUserInfoToEntity(formItem);
            travelClaimFormItemMapper.insert(formItem);
        }
        //成功则改状态
        TravelClaimForm parentTravelClaimForm = travelClaimFormMapper.selectById(id);
        if (GeneralTool.isEmpty(parentTravelClaimForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //改为已撤单状态
        parentTravelClaimForm.setStatus(ProjectExtraEnum.REVOKED.key);
        utilService.updateUserInfoToEntity(parentTravelClaimForm);
        travelClaimFormMapper.updateById(parentTravelClaimForm);
    }

    /**
     * 根据id获取差旅报销单
     * @param targetId
     * @return
     */
    @Override
    public TravelClaimForm getTravelClaimFormById(Long targetId) {
        return travelClaimFormMapper.selectById(targetId);
    }

    /**
     * 修改差旅报销单状态
     * @param travelClaimForm
     * @return
     */
    @Override
    public Boolean updateTravelClaimFormStatus(TravelClaimForm travelClaimForm) {
        utilService.updateUserInfoToEntity(travelClaimForm);
        travelClaimFormMapper.updateById(travelClaimForm);
        return true;
    }

    /**
     * 获取差旅报销单总金额
     * @param id
     * @return
     */
    @Override
    public BigDecimal getTravelClaimFormTotalAmount(Long id) {
        return travelClaimFormMapper.getTravelClaimFormTotalAmount(id);
    }

    private List<TravelClaimForm> getTravelClaimFormsByExample(TravelClaimFormQueryDto travelClaimFormQueryDto, Page page, Map<Long, Integer> map) {
        LambdaQueryWrapper<TravelClaimForm> wrapper = new LambdaQueryWrapper();
        //不选所属公司时
        if (GeneralTool.isEmpty(travelClaimFormQueryDto) || GeneralTool.isEmpty(travelClaimFormQueryDto.getFkCompanyId())) {
            wrapper.in(TravelClaimForm::getFkCompanyId, SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId()));
        }
        if (GeneralTool.isNotEmpty(travelClaimFormQueryDto)) {
            //查询条件-所属公司 多选
            if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getFkCompanyIds())) {
                if (!SecureUtil.validateCompanys(travelClaimFormQueryDto.getFkCompanyIds())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.in(TravelClaimForm::getFkCompanyId, travelClaimFormQueryDto.getFkCompanyIds());
            }
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(travelClaimFormQueryDto.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.eq(TravelClaimForm::getFkCompanyId, travelClaimFormQueryDto.getFkCompanyId());
            }
            //查询条件-所属部门
            if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getFkDepartmentId())) {
                wrapper.eq(TravelClaimForm::getFkDepartmentId, travelClaimFormQueryDto.getFkDepartmentId());
            }
            if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getStatus())) {
                wrapper.eq(TravelClaimForm::getStatus, travelClaimFormQueryDto.getStatus());
            }
            if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getGmtCreateUser())) {
                wrapper.like(TravelClaimForm::getGmtCreateUser, travelClaimFormQueryDto.getGmtCreateUser());
            }
            //查询条件-表单编号
            if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getNum())) {
                wrapper.like(TravelClaimForm::getNum, travelClaimFormQueryDto.getNum());
            }
            //查询条件-开始日期
            if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getStartTime())) {
                wrapper.ge(TravelClaimForm::getGmtCreate, travelClaimFormQueryDto.getStartTime());
            }
            //查询条件-结束日期
            if (GeneralTool.isNotEmpty(travelClaimFormQueryDto.getEndTime())) {
                wrapper.le(TravelClaimForm::getGmtCreate, travelClaimFormQueryDto.getEndTime());
            }
            //根据不同的选择状态拼接不同的条件
            if ("0".equals(travelClaimFormQueryDto.getSelectStatus())) {
                //0表示要显示我的申请列表，即我创建的表单
                wrapper.eq(TravelClaimForm::getFkStaffId, GetAuthInfo.getStaffId());
            } else if ("1".equals(travelClaimFormQueryDto.getSelectStatus())) {
                //1表示所有表单

            } else if ("2".equals(travelClaimFormQueryDto.getSelectStatus())) {
                //2表示显示我的审批列表，即我操作过的表单都要显示
                List<Long> fromIds = new ArrayList<>(map.keySet());
                if (GeneralTool.isEmpty(fromIds)) {
                    fromIds.add(0L);
                }
                wrapper.in(TravelClaimForm::getId, fromIds);
            }
        }
        wrapper.orderByDesc(TravelClaimForm::getGmtCreate);
        IPage<TravelClaimForm> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<TravelClaimForm> expenseClaimForms = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return expenseClaimForms;
    }

    /**
     * @return java.math.BigDecimal
     * @Description : 获取报销金额总和
     * @Param [travelClaimFormDto]
     * <AUTHOR>
     */
    private BigDecimal getAmountSum(TravelClaimFormVo travelClaimFormVo) {
        BigDecimal amountSum = BigDecimal.valueOf(0);
        for (TravelClaimFormItemVo travelClaimFormItemVo : travelClaimFormVo.getTravelClaimFormItemDtoList()) {
            amountSum = amountSum.add(travelClaimFormItemVo.getAmount());
        }
        return amountSum;
    }


}

package com.get.remindercenter.feign;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.remindercenter.component.EmailAbstractHelper;
import com.get.remindercenter.controller.RemindTaskQueueController;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.*;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.service.*;
import com.get.remindercenter.vo.*;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign实现
 */
@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class ReminderCenterClient implements IReminderCenterClient {
    private final IContactPersonTypeService contactPersonTypeService;
    private final RemindTaskService remindTaskService;
    private final RemindTaskQueueController remindTaskQueueController;
    private final RemindTaskQueueService remindTaskQueueService;
    private final RemindTemplateService remindTemplateService;

    private final EmailTemplateService emailTemplateService;

    private final DefaultSendEmailService defaultSendEmailService;
    private final IBatchEmailPromotionQueueService batchEmailPromotionQueueService;
    private final EmailSenderQueueService emailSenderQueueService;



//    @Override
//    public Result<List<ContactPersonTypeDto>> getAllType() {
//        return Result.data(contactPersonTypeService.getContactPersonTypes());
//    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> batchAdd(List<RemindTaskDto> remindTaskDtos) {
        return Result.data(remindTaskService.batchAdd(remindTaskDtos));
    }

    @Override
    public Result<Boolean> batchUpdate(List<RemindTaskDto> remindTaskDtos, String fkTableName, Long fkTableId) {
        return Result.data(remindTaskService.batchUpdate(remindTaskDtos, fkTableName, fkTableId));
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<Boolean> batchUpdateNew(List<RemindTaskDto> remindTaskDtos) {
        return Result.data(remindTaskService.batchUpdateNew(remindTaskDtos));
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<Boolean> batchUpdateTaskNew(List<RemindTaskDto> remindTaskDtos) {
        return Result.data(remindTaskService.batchUpdateTaskNew(remindTaskDtos));
    }

    @Override
    public Result<Boolean> batchDeleteTaskNew(List<RemindTaskDto> remindTaskDtos) {
        return Result.data(remindTaskService.batchDeleteTaskNew(remindTaskDtos));
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<Boolean> batchUpdateImport(List<RemindTaskDto> remindTaskDtos) {
        return Result.data(remindTaskService.batchUpdatebatchImport(remindTaskDtos));
    }

    @Override
    public Result<Boolean> batchDeleteByTableId(String fkTableName, Long fkTableId, List<String> fkRemindEventTypeKeys) {
        return Result.data(remindTaskService.batchDeleteByTableId(fkTableName,fkTableId,fkRemindEventTypeKeys));
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<Boolean> batchAddTask(List<RemindTaskDto> remindTaskDtos) {
        return Result.data(remindTaskService.batchAddTask(remindTaskDtos));
    }


    @Override
    public Result<Boolean> batchUpdateByTableIds(List<RemindTaskDto> remindTaskDtos, String fkTableName, Set<Long> fkTableIds) {
        return Result.data(remindTaskService.batchUpdateByTableIds(remindTaskDtos, fkTableName, fkTableIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void performTasks() {
        remindTaskQueueController.performTasks();
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void sendEmailScheduleTask() {
        remindTaskQueueController.sendEmailScheduleTask();
    }

    @Override
    public Result add(RemindTaskUpdateDto remindTaskUpdateDto) {
        remindTaskService.add(remindTaskUpdateDto);
        return Result.success("操作成功");
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> sendMail(String title, String template, String toEmail, String ccEmail) {
        return Result.data(remindTaskQueueService.sendMail(title, template, toEmail, ccEmail));
    }

//    @Override
//    public void sendEmail(String title, String typeKey, Long fkStaffId,String taskRemark) {
//        remindTaskQueueService.sendEmail(title,typeKey,fkStaffId,taskRemark);
//    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void batchSendEmail(List<Map<String, String>> list, String typeKey) {
        remindTaskQueueService.batchSendEmail(list,typeKey);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void batchSendEnEmail(List<Map<String, String>> list, String typeKey,String version) {
        remindTaskQueueService.batchSendEnEmail(list,typeKey,version);
    }



    @Override
    public void batchSendEmailCustom(MailDto mailDto) {
        remindTaskQueueService.batchSendEmailCustom(mailDto);
    }


    @Override
    public Result<Boolean> sendEmailToStaff(String title, String typeKey, Long fkStaffId,String taskRemark) {
        return Result.data(remindTaskQueueService.sendEmailToStaff(title,typeKey,fkStaffId,taskRemark));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> customSendMail(String defaultEncoding, String host, int port, String protocol, String userName, String password, String title, String toEmail, String ccEmail, String template, boolean flag) {
        return Result.data(remindTaskQueueService.customSendMail(defaultEncoding, host, port, protocol, userName, password, title, toEmail, ccEmail, template, flag));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> customSendMailByBody(MailDto mailDto) {
        return Result.data(remindTaskQueueService.customSendMailByMailVo(mailDto));
    }

    @Override
    public Result<Boolean> aliyunSendMailNew(AliyunSendMailDto aliyunSendMailVo) {
        return Result.data(remindTaskQueueService.aliyunSendMailNew(aliyunSendMailVo));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean>  sendSystemMail(EmailSystemMQMessageDto emailSystemMQMessageDto) {
        return Result.data(remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> sendCustomMail(EmailCustomMQMessageDto emailCustomMQMessageDto) {
        return Result.data(remindTaskQueueService.sendCustomMail(emailCustomMQMessageDto));
    }

    @Override
    public Result<Boolean> sendMqEmail(EmailSenderQueue emailSenderQueue) {
        return Result.data(emailSenderQueueService.sendMqEmail(emailSenderQueue));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> sendMqEmailTask(EmailSenderQueue emailSenderQueue) {
        return Result.data(emailSenderQueueService.performEmailTasks(emailSenderQueue));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> batchAddEmailQueue(List<EmailSenderQueue> queueList) {
        return Result.data(emailSenderQueueService.batchAddEmailQueue(queueList));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> updateEmailQueue(EmailSenderQueue emailSenderQueue) {
        return Result.data(emailSenderQueueService.updateEmailQueue(emailSenderQueue));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> sendNewsEmail(AliyunSendMailDto aliyunSendMailVo) {
        return Result.data(remindTaskQueueService.sendNewsEmail(aliyunSendMailVo));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> deleteMsgFromQueue(Long id) {
        return Result.data(batchEmailPromotionQueueService.deleteMsgFromQueue(id));
    }

    @Override
    public Result<Boolean> addTask2NewsEmailQueen(AliyunSendMailDto aliyunSendMailVo) {
        return Result.data(batchEmailPromotionQueueService.addTask2NewsEmailQueen(aliyunSendMailVo));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<BatchEmailPromotionQueueVo>> getSendNewsEamilQueues() {
        return Result.data(batchEmailPromotionQueueService.getSendNewsEamilQueues());
    }

    /**
     * 获取必发邮箱
     * @return
     */
    @Override
    public Result<List<String>> getDefaultSendEmail() {
        return Result.data(defaultSendEmailService.getDefaultSendEmail());
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<RemindTemplate> getRemindTemplateByTypeKey(String typeKey) {
        return Result.data(remindTemplateService.getRemindTemplateByTypeKey(typeKey));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<EmailTemplate> getEmailTemplateByTypeKey(String typeKey) {
        return Result.data(emailTemplateService.getEmailTemplateByTypeKey(typeKey));
    }


    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> sendSms(SmsDto smsDto) {
        return Result.data(remindTaskQueueService.sendSms(smsDto));
    }


    @Override
    public Result<Long> aliyunAddTag(String TagName) {
        return Result.data(remindTaskQueueService.aliyunAddTag(TagName));
    }

}

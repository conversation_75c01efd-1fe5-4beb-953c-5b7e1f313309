<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.get</groupId>
        <artifactId>biz-service</artifactId>
        <version>1.0.RELEASE</version>
    </parent>

    <artifactId>ais-rocketmq-center</artifactId>
    <name>${project.artifactId}</name>
    <version>${get.project.version}</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>

        <!-- 邮件依赖-->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>

        <!-- 解析word-->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>5.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.3</version>
        </dependency>


        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.11.0</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>i18n-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>


        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.24</version>
        </dependency>

        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.1000</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-reminder-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-reminder-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-reminder-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-reminder-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-rocketmq-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.get</groupId>
            <artifactId>ais-sale-center-ap</artifactId>
            <version>1.0.RELEASE</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>



    <build>
        <finalName>${project.name}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring.boot.version}</version>
                    <configuration>
                        <fork>true</fork>
                        <finalName>${project.build.finalName}</finalName>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>1.2.2</version>
                    <executions>
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <!--打包docker镜像的docker服务器-->
                        <dockerHost>${docker-url}</dockerHost>
                        <!--镜像名，这里用工程名 -->
                        <imageName>${registry-url}/${nexus.project_name}/${project.artifactId}:${nexus.version}</imageName>
                        <!--nexus3 hosted 仓库地址-->
                        <registryUrl>${registry-url}</registryUrl>
                        <!-- ca认证正书-->
                        <!--                        <dockerCertPath>./docker/cert-new</dockerCertPath>-->
                        <!--TAG,这里用工程版本号-->
                        <imageTags>
                            <!-- 指定镜像标签,可以排至多个标签 -->
                            <imageTag>${nexus.version}</imageTag>
                            <imageTag>latest</imageTag>
                        </imageTags>
                        <!--是否强制覆盖已有镜像-->
                        <forceTags>true</forceTags>
                        <!--方式一：1、指定Dockerfile文件所在目录，通过文件执行打包上传nexus私服-->
                        <dockerDirectory>biz-service/${project.artifactId}/src/main/docker</dockerDirectory>
                        <!-- 指定docker镜像打包参数，即dockerfile中使用的参数，通过${参数名}取值 -->
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                            <JAR_FILE_NAME>${project.artifactId}.jar</JAR_FILE_NAME>
                        </buildArgs>
                        <resources>
                            <resource>
                                <targetPath>/</targetPath>
                                <directory>${project.build.directory}</directory>
                                <include>${project.build.finalName}.jar</include>
                            </resource>
                        </resources>
                        <serverId>nexus-docker-prod</serverId>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <executions>
                        <execution>
                            <phase>package</phase>
                            <goals>
                                <goal>run</goal>
                            </goals>
                            <configuration>
                                <tasks>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <copy overwrite="true"
                                          tofile="${session.executionRootDirectory}/target/${project.artifactId}.jar"
                                          file="${project.build.directory}/${project.artifactId}.jar" />
                                </tasks>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
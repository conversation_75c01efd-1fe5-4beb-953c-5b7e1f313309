package com.von.gulimall.car.service;

import com.von.gulimall.car.vo.Cart;
import com.von.gulimall.car.vo.CartItem;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @Date 2024/12/3 14:02
 * @Version 1.0
 */
public interface CartService {
    CartItem addToCart(Long skuId, Integer num) throws ExecutionException, InterruptedException;

    CartItem getCartItem(Long skuId);

    Cart cartList() throws ExecutionException, InterruptedException;

    void checkItem(Long skuId, Integer check);

    void changeItemCount(Long skuId, Integer num);

    void deleteItem(Long skuId);

    List<CartItem> getCurrentUserCheckCartItems();
}

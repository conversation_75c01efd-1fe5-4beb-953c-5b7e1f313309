package com.von.gulimall.auth.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.TypeReference;
import com.von.common.constant.AuthServerConstant;
import com.von.common.enums.BizCodeEnum;
import com.von.common.utils.R;
import com.von.common.vo.MemberLoginVo;
import com.von.common.vo.MemberRespVo;
import com.von.gulimall.auth.feign.MemberFeignService;
import com.von.gulimall.auth.feitn.ThirdPartFeignService;
import com.von.gulimall.auth.vo.UserRegisterVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpSession;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/11/28 17:34
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/login")
@RequiredArgsConstructor
public class LoginController {


    private final MemberFeignService memberFeignService;


    private final ThirdPartFeignService thirdPartFeignService;

    private final StringRedisTemplate stringRedisTemplate;



    /**
     * 发送验证码并保存到redis, 过期时间是10分钟
     *
     * @param phone
     * @return
     */
    @GetMapping("/sendCode")
    public R sendCode(@RequestParam("phone") String phone) {
        String redisKey = AuthServerConstant.SMS_CODE_CACHE_PREFIX + phone;
        String redisCode = this.stringRedisTemplate.opsForValue().get(redisKey);
        // 查询redis
        if (StrUtil.isNotBlank(redisCode)) {
            List<String> redisCodeSplit = StrUtil.split(redisCode, "_");
            if (CollectionUtil.isEmpty(redisCodeSplit) || redisCodeSplit.size() < 2) {
                return R.error();
            }
            long saveTime = Long.parseLong(redisCodeSplit.get(1));
            // 短信验证码获取时间需要超过60s
            if (System.currentTimeMillis() - saveTime < 60000) {
                log.info("当前的验证码是 : {}", redisCodeSplit.get(0));
                return R.error(BizCodeEnum.SMS_CODE_EXCEPTION.getCode(), BizCodeEnum.SMS_CODE_EXCEPTION.getMsg());
            }
        }

        // 发送验证码
        String code = StrUtil.toString(RandomUtil.randomInt(1000, 9999));
        // TODO 这里如果验证码保存异常的话要进行处理
        this.thirdPartFeignService.sendCode(phone, code);
        String value = code + "_" + System.currentTimeMillis();
        this.stringRedisTemplate.opsForValue().set(redisKey, value, 10, TimeUnit.MINUTES);
        log.info("当前的验证码是 : {}", code);

        return R.ok();
    }

    /**
     * 注册
     *
     * @param userRegisterVo
     * @return
     * @throws Exception
     */
    @PostMapping("/regist")
    public R regist(@Valid @RequestBody UserRegisterVo userRegisterVo) {
        // 校验验证码
        String code = userRegisterVo.getCode();
        String redisKey = AuthServerConstant.SMS_CODE_CACHE_PREFIX + userRegisterVo.getPhone();
        String redisCode = this.stringRedisTemplate.opsForValue().get(redisKey);
        if (StrUtil.isBlank(redisCode)) {
            return R.error("验证码为空");
        }
        List<String> redisCodeSplit = StrUtil.split(redisCode, "_");
        if (!code.equals(redisCodeSplit.get(0))) {
            return R.error("验证码错误");
        }
        // 远程调用进行注册
        R r = this.memberFeignService.regist(userRegisterVo);
        // 校验通过, 删除验证码( 令牌机制 )
        this.stringRedisTemplate.delete(redisKey);
        if (!r.isOk()) {
            return R.error("注册发生错误");
        }

        return R.ok("注册成功");
    }

    /**
     * 登陆
     *
     * @param memberLoginVo
     * @return
     */
    @PostMapping("/login")
    public R login(@RequestBody MemberLoginVo memberLoginVo, HttpSession session) {
        R r = this.memberFeignService.login(memberLoginVo);

        if (r.isOk()) {
            MemberRespVo memberRespVo = r.getData(new TypeReference<MemberRespVo>() {});
            session.setAttribute(AuthServerConstant.LOGIN_USER, memberLoginVo);
        }

        return r;
    }

}

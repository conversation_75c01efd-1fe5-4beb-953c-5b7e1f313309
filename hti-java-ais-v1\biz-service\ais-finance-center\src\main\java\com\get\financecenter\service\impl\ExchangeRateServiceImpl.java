package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ExchangeRateMapper;
import com.get.financecenter.dto.ExchangeRateDto;
import com.get.financecenter.entity.ExchangeRate;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.utils.GetExchangeRateUtils;
import com.get.financecenter.vo.ExchangeRateVo;
import com.get.financecenter.vo.GetExchangeRateVo;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/12/21 16:50
 * @verison: 1.0
 * @description:
 */
@Service
public class ExchangeRateServiceImpl extends BaseServiceImpl<ExchangeRateMapper, ExchangeRate> implements IExchangeRateService {
    @Resource
    private ExchangeRateMapper exchangeRateMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private GetExchangeRateUtils getExchangeRateUtils;

    @Override
    public ExchangeRateVo findExchangeRateById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExchangeRate exchangeRate = exchangeRateMapper.selectById(id);
        if (GeneralTool.isEmpty(exchangeRate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(exchangeRate, ExchangeRateVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ExchangeRateDto> exchangeRateDtos) {
        if (GeneralTool.isEmpty(exchangeRateDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ExchangeRateDto exchangeRateDto : exchangeRateDtos) {
            ExchangeRate exchangeRate = BeanCopyUtils.objClone(exchangeRateDto, ExchangeRate::new);
            utilService.updateUserInfoToEntity(exchangeRate);
            exchangeRateMapper.insertSelective(exchangeRate);
        }
    }

    @Override
    public Boolean batchAddAuTo(List<ExchangeRateDto> exchangeRateDtos) {
        if (GeneralTool.isEmpty(exchangeRateDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ExchangeRateDto exchangeRateDto : exchangeRateDtos) {
            ExchangeRate exchangeRate = BeanCopyUtils.objClone(exchangeRateDto, ExchangeRate::new);
            exchangeRate.setGmtCreateUser("admin");
            exchangeRate.setGmtCreate(new Date());
            exchangeRateMapper.insertSelective(exchangeRate);
        }
        return true;
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (exchangeRateMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        exchangeRateMapper.deleteById(id);
    }

    @Override
    public ExchangeRateVo updateExchangeRate(ExchangeRateDto exchangeRateDto) {
        if (exchangeRateDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ExchangeRate result = exchangeRateMapper.selectById(exchangeRateDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ExchangeRate exchangeRate = BeanCopyUtils.objClone(exchangeRateDto, ExchangeRate::new);
        utilService.updateUserInfoToEntity(exchangeRate);
        exchangeRateMapper.updateById(exchangeRate);
        return findExchangeRateById(exchangeRate.getId());
    }

    @Override
    public List<ExchangeRateVo> getExchangeRates(ExchangeRateDto exchangeRateDto, Page page) {
//        Example example = new Example(ExchangeRate.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(exchangeRateVo)) {
//            //查询条件-日期
//            if (GeneralTool.isNotEmpty(exchangeRateVo.getGetDate())) {
//                criteria.andEqualTo("getDate", exchangeRateVo.getGetDate());
//            }
//            //查询条件-币种
//            if (GeneralTool.isNotEmpty(exchangeRateVo.getFkCurrencyTypeNumFrom())) {
//                criteria.andEqualTo("fkCurrencyTypeNumFrom", exchangeRateVo.getFkCurrencyTypeNumFrom());
//            }
//            //查询条件-兑换币种
//            if (GeneralTool.isNotEmpty(exchangeRateVo.getFkCurrencyTypeNumTo())) {
//                criteria.andEqualTo("fkCurrencyTypeNumTo", exchangeRateVo.getFkCurrencyTypeNumTo());
//            }
//        }
//        example.orderBy("getDate").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ExchangeRate> exchangeRates = exchangeRateMapper.selectByExample(example);
//        page.restPage(exchangeRates);

        LambdaQueryWrapper<ExchangeRate> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(exchangeRateDto)) {
            //查询条件-日期
            if (GeneralTool.isNotEmpty(exchangeRateDto.getGetDate())) {
                wrapper.eq(ExchangeRate::getGetDate, exchangeRateDto.getGetDate());
            }
            //查询条件-币种
            if (GeneralTool.isNotEmpty(exchangeRateDto.getFkCurrencyTypeNumFrom())) {
                wrapper.eq(ExchangeRate::getFkCurrencyTypeNumFrom, exchangeRateDto.getFkCurrencyTypeNumFrom());
            }
            //查询条件-兑换币种
            if (GeneralTool.isNotEmpty(exchangeRateDto.getFkCurrencyTypeNumTo())) {
                wrapper.eq(ExchangeRate::getFkCurrencyTypeNumTo, exchangeRateDto.getFkCurrencyTypeNumTo());
            }
        }
        IPage<ExchangeRate> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        page.setAll((int) pages.getTotal());
        List<ExchangeRate> exchangeRates = pages.getRecords();
        List<ExchangeRateVo> convertDatas = new ArrayList<>();
        Set<String> currencyTypeNum = new HashSet<>();
        for (ExchangeRate exchangeRate : exchangeRates) {
            currencyTypeNum.add(exchangeRate.getFkCurrencyTypeNumFrom());
            currencyTypeNum.add(exchangeRate.getFkCurrencyTypeNumTo());
        }
        Map<String, String> currencyNameMap = currencyTypeService.getCurrencyNamesByNums(currencyTypeNum);
        for (ExchangeRate exchangeRate : exchangeRates) {
            ExchangeRateVo exchangeRateVo = BeanCopyUtils.objClone(exchangeRate, ExchangeRateVo::new);
            exchangeRateVo.setCurrencyTypeNumFromFullName(currencyNameMap.get(exchangeRateVo.getFkCurrencyTypeNumFrom()));
            exchangeRateVo.setCurrencyTypeNumToFullName(currencyNameMap.get(exchangeRateVo.getFkCurrencyTypeNumTo()));
            convertDatas.add(exchangeRateVo);
        }
        return convertDatas;
    }

    @Override
    public BigDecimal getRateByCurrency(String fromCurrency, String toCurrency) {
//        if (GeneralTool.isEmpty(fromCurrency) || GeneralTool.isEmpty(toCurrency)) {
//            return new BigDecimal(1.0000);
//        }
//
//        BigDecimal fromRate = "CNY".equals(fromCurrency) ? new BigDecimal(1) : exchangeRateMapper.getRateByCurrency("CNY", fromCurrency);
//        BigDecimal toRate = "CNY".equals(toCurrency) ? new BigDecimal(1) : exchangeRateMapper.getRateByCurrency("CNY", toCurrency);
//
//        if (GeneralTool.isEmpty(fromRate) || GeneralTool.isEmpty(toRate)) {
//            return new BigDecimal(1.0000);
//        }
//        //保留4位 四舍五入
//        return toRate.divide(fromRate, 6, BigDecimal.ROUND_HALF_UP);

        return getLastExchangeRate(false, fromCurrency, toCurrency).getExchangeRate();
    }


    @Override
    public Long addExchangeRate(ExchangeRateDto exchangeRateDto) {
        if (GeneralTool.isEmpty(exchangeRateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ExchangeRate exchangeRate = BeanCopyUtils.objClone(exchangeRateDto, ExchangeRate::new);
        exchangeRate.setGmtCreateUser("admin");
        exchangeRate.setGmtCreate(new Date());
        exchangeRateMapper.insertSelective(exchangeRate);
        return exchangeRate.getId();
    }

    @Override
    public ExchangeRateVo getLastExchangeRate(Boolean last, String fromCurrency, String toCurrency) {
        //当创建最新参数为true，直接在汇率表拿当天的汇率，有，返回汇率。else 如果找不到，创建连接拿即时汇率，然后记录到汇率表，然后返回汇率
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String today = fmt.format(date);
        if (Objects.equals("FCY", fromCurrency) && Objects.equals("FCY", toCurrency)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("fcy_not_count"));
        }
        if (!last) {
            ExchangeRate exchangeRate = exchangeRateMapper.getRateByCurrencyByGetDate(fromCurrency, toCurrency, date);
            //有，返回汇率
            if (GeneralTool.isNotEmpty(exchangeRate)) {
                return BeanCopyUtils.objClone(exchangeRate, ExchangeRateVo::new);
            } else {
                //如果找不到，创建连接拿即时汇率，然后记录到汇率表，然后返回汇率
                return getLastRate(fromCurrency, toCurrency, date);
            }
        } else {
            //false时，直接拿最新的汇率表对应的汇率
            return getLastRate(fromCurrency, toCurrency, date);
        }
    }

    /**
     * 获取香港对应所有币种的汇率
     * @return
     */
    @Override
    public Map<String,BigDecimal> getLastExchangeRateHkd(Set<String> toCurrencys) {
        if (GeneralTool.isEmpty(toCurrencys)){
            return Maps.newHashMap();
        }
        Map<String, BigDecimal> exchangeRateHkdMap = new HashMap<String, BigDecimal>();
        List<ExchangeRateVo> lastExchangeRateHkd = exchangeRateMapper.getLastExchangeRateHkd(toCurrencys,"HKD");
        if (GeneralTool.isNotEmpty(lastExchangeRateHkd)){
           exchangeRateHkdMap = lastExchangeRateHkd.stream().collect(Collectors.toMap(ExchangeRateVo::getFkCurrencyTypeNumFrom,
                   ExchangeRateVo::getExchangeRate,(v1, v2)->v1));
            //取差集
            Set<String> copyToCurrencys = new HashSet<>(toCurrencys);
            copyToCurrencys.removeAll(exchangeRateHkdMap.keySet());
            if (GeneralTool.isNotEmpty(copyToCurrencys)){
                for (String toCurrency : copyToCurrencys) {
                    try{
                        ExchangeRateVo exchangeRateVo = getLastRate(toCurrency, "HKD", new Date());
                        exchangeRateHkdMap.put(exchangeRateVo.getFkCurrencyTypeNumFrom(), exchangeRateVo.getExchangeRate());
                    }catch (Exception e){
                        log.error(toCurrency+"找不到該幣種" + e.getMessage());
                    }
                }
            }
        }else{
            for (String toCurrency : toCurrencys) {
                try {
                    ExchangeRateVo exchangeRateVo = getLastRate(toCurrency, "HKD", new Date());
                    exchangeRateHkdMap.put(exchangeRateVo.getFkCurrencyTypeNumFrom(), exchangeRateVo.getExchangeRate());
                }catch (Exception e){
                    log.error(toCurrency+"找不到該幣種" + e.getMessage());
                }
            }
        }
        return exchangeRateHkdMap;
    }

    @Override
    public Map<String, BigDecimal> getLastExchangeRate(Set<String> toCurrencys, String currencyNum) {
        if (GeneralTool.isEmpty(toCurrencys)){
            return Maps.newHashMap();
        }
        Map<String, BigDecimal> exchangeRateHkdMap = new HashMap<String, BigDecimal>();
        List<ExchangeRateVo> lastExchangeRateHkd = exchangeRateMapper.getLastExchangeRateHkd(toCurrencys,currencyNum);
        if (GeneralTool.isNotEmpty(lastExchangeRateHkd)){
            exchangeRateHkdMap = lastExchangeRateHkd.stream().collect(Collectors.toMap(ExchangeRateVo::getFkCurrencyTypeNumFrom,
                    ExchangeRateVo::getExchangeRate,(v1, v2)->v1));
            //取差集
            Set<String> copyToCurrencys = new HashSet<>(toCurrencys);
            copyToCurrencys.removeAll(exchangeRateHkdMap.keySet());
            if (GeneralTool.isNotEmpty(copyToCurrencys)){
                for (String toCurrency : copyToCurrencys) {
                    try{
                        ExchangeRateVo exchangeRateVo = getLastRate(toCurrency, currencyNum, new Date());
                        exchangeRateHkdMap.put(exchangeRateVo.getFkCurrencyTypeNumFrom(), exchangeRateVo.getExchangeRate());
                    }catch (Exception e){
                        log.error(toCurrency+"找不到該幣種" + e.getMessage());
                    }
                }
            }
        }else{
            for (String toCurrency : toCurrencys) {
                try {
                    ExchangeRateVo exchangeRateVo = getLastRate(toCurrency, currencyNum, new Date());
                    exchangeRateHkdMap.put(exchangeRateVo.getFkCurrencyTypeNumFrom(), exchangeRateVo.getExchangeRate());
                }catch (Exception e){
                    log.error(toCurrency+"找不到該幣種" + e.getMessage());
                }
            }
        }
        return exchangeRateHkdMap;
    }

    private ExchangeRateVo getLastRate(String fromCurrency, String toCurrency, Date date) throws GetServiceException {
        try {
            if (Objects.equals("FCY", fromCurrency) && Objects.equals("FCY", toCurrency)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("fcy_not_count"));
            }
            String exchangeRateUtils = getExchangeRateUtils.getExchangeRateUtils(fromCurrency, toCurrency);
            GetExchangeRateVo getExchangeRateVo = GeneralTool.str2Bean(exchangeRateUtils, GetExchangeRateVo.class);
            ExchangeRateVo exchangeRateVo = new ExchangeRateVo();
            exchangeRateVo.setFkCurrencyTypeNumFrom(fromCurrency);
            exchangeRateVo.setFkCurrencyTypeNumTo(toCurrency);
            //保留四位
            exchangeRateVo.setExchangeRate(getExchangeRateVo.getResult().getRate());
            //添加表
            ExchangeRateDto exchangeRateDto = BeanCopyUtils.objClone(exchangeRateVo, ExchangeRateDto::new);
            exchangeRateDto.setGetDate(date);
            addExchangeRate(exchangeRateDto);
            return exchangeRateVo;
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("get_exchange_rate_error"));
        }
    }

}

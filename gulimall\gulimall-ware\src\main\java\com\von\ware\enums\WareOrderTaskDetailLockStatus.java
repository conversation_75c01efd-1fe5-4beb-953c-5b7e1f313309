package com.von.ware.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * 库存订单任务详情状态枚举
 *
 * <AUTHOR>
 * @Date 2024/12/31 16:41
 * @Version 1.0
 */
@Getter
@ToString
@AllArgsConstructor
public enum WareOrderTaskDetailLockStatus {

    LOCK("锁定", 1),

    UNLOCK("解锁", 2),

    REDUCE("减少", 3);

    private final String msg;

    private final Integer code;

    public static final Map<Integer, WareOrderTaskDetailLockStatus> WARE_ORDER_TASK_DETAIL_LOCK_STATUS_MAP = new HashMap<>();

    static {
        for (WareOrderTaskDetailLockStatus wareOrderTaskDetailLockStatus : WareOrderTaskDetailLockStatus.values()) {
            WARE_ORDER_TASK_DETAIL_LOCK_STATUS_MAP.put(wareOrderTaskDetailLockStatus.getCode(), wareOrderTaskDetailLockStatus);
        }
    }

    public static WareOrderTaskDetailLockStatus valueOf(Integer code) {
        return WARE_ORDER_TASK_DETAIL_LOCK_STATUS_MAP.get(code);
    }

}

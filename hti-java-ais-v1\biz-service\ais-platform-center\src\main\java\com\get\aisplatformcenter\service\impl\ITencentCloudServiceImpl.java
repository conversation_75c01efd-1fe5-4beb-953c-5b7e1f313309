package com.get.aisplatformcenter.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.get.aisplatformcenter.util.ConnectTencentCloud;
import com.qcloud.cos.model.PutObjectRequest;
import com.qcloud.cos.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

import  com.get.aisplatformcenter.service.ITencentCloudService;

@Service
@Slf4j
public class ITencentCloudServiceImpl implements ITencentCloudService {

    //图片存储地址-公开桶
    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    //文件存储
    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;

    @Override
    public Boolean uploadObject(boolean isPub, String bucketName, MultipartFile file, String fileKey) {

        try {
            // 指定要上传的文件
            File localFile = null;
            try {
                localFile = multipartFileToFile(file);
            } catch (Exception e) {
                e.printStackTrace();
            }
            if(ObjectUtil.isNotEmpty(localFile)){
                if (localFile.exists()) {
                    try {
                        Thread.sleep(15);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    // fileKey 指定要上传到 COS 上对象键
                    PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, fileKey, localFile);

                    //公开桶上传
                    if(isPub)
                    {
                        log.info("上传文件到公开桶："+bucketName);
                        PutObjectResult putObjectResult = ConnectTencentCloud.getPublicCosClient().putObject(putObjectRequest);
                    }else {
                        log.info("上传文件到私密桶："+bucketName);
                        PutObjectResult putObjectResult = ConnectTencentCloud.getPublicCosClient().putObject(putObjectRequest);
                    }
                }else {
                    return false;
                }
            }else {
                return false;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    /**
     * MultipartFile 转 File
     *
     * @param file
     * @throws Exception
     */
    public static File multipartFileToFile(MultipartFile file) throws Exception {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {
            InputStream ins = null;
            ins = file.getInputStream();
            toFile = new File(file.getOriginalFilename());
            inputStreamToFile(ins, toFile);
            ins.close();
        }
        return toFile;
    }
    /**
     * 获取流文件
     * @param ins 传入文件字节流
     * @param file 返回的文件对象
     */
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

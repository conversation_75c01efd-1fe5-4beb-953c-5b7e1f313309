package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.financecenter.dao.CompanyProfitAndLossTemplateMapper;
import com.get.financecenter.entity.CompanyProfitAndLossTemplate;
import com.get.financecenter.service.CompanyProfitAndLossTemplateService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * 公司损益表项模板服务实现类
 */
@Service("financeCompanyProfitAndLossTemplateService")
public class CompanyProfitAndLossTemplateServiceImpl extends ServiceImpl<CompanyProfitAndLossTemplateMapper, CompanyProfitAndLossTemplate> implements CompanyProfitAndLossTemplateService {
    @Resource
    private CompanyProfitAndLossTemplateMapper companyProfitAndLossTemplateMapper;

}


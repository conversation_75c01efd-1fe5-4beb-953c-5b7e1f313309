package com.von.ware.controller;

import com.von.common.utils.PageUtils;
import com.von.common.utils.R;
import com.von.ware.entity.PurchaseEntity;
import com.von.ware.service.PurchaseService;
import com.von.ware.vo.MergeVo;
import com.von.ware.vo.PurchaseDoneVo;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;



/**
 * 采购信息
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 23:45:29
 */
@RestController
@RequestMapping("ware/purchase")
@RequiredArgsConstructor
public class PurchaseController {

    private final PurchaseService purchaseService;

    /**
     * 合并采购需求到采购单
     *
     * @param mergeVo
     * @return
     */
    @PostMapping("/merge")
    public R merge(@RequestBody MergeVo mergeVo) {
        this.purchaseService.mergePurchase(mergeVo);

        return R.ok();
    }

    /**
     * 查找未分配或者新建状态下的采购单
     *
     *
     * @param params
     * @return
     */
    @GetMapping("/unreceive/list")
    public R unreceiveList(@RequestParam Map<String, Object> params){
        PageUtils page = this.purchaseService.queryPageUnreceivePurchase(params);

        return R.ok().put("page", page);
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    // @RequiresPermissions("ware:purchase:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = purchaseService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @GetMapping("/info/{id}")
    // @RequiresPermissions("ware:purchase:info")
    public R info(@PathVariable("id") Long id){
		PurchaseEntity purchase = purchaseService.getById(id);

        return R.ok().put("purchase", purchase);
    }

    /**
     * 保存
     */
    @PostMapping("/save")
    // @RequiresPermissions("ware:purchase:save")
    public R save(@RequestBody PurchaseEntity purchase){
		purchaseService.save(purchase);

        return R.ok();
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    // @RequiresPermissions("ware:purchase:update")
    public R update(@RequestBody PurchaseEntity purchase){
		purchaseService.updateById(purchase);

        return R.ok();
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete")
    // @RequiresPermissions("ware:purchase:delete")
    public R delete(@RequestBody Long[] ids){
		purchaseService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

    /**
     * 采购人员领取采购单, 修改采购单的状态
     *
     * @param idList
     * @return
     */
    @PutMapping("/received")
    public R received(@RequestBody List<Long> idList) {
        this.purchaseService.received(idList);
        return R.ok();
    }


    /**
     * 测试
     */
    @PutMapping("/test")
    // @RequiresPermissions("ware:purchase:update")
    public R test(){
        List<PurchaseEntity> purchaseEntities = this.purchaseService.test();

        return R.ok().put("data", purchaseEntities);
    }

    @PostMapping("/done")
    public R finish(@RequestBody PurchaseDoneVo purchaseDoneVo) {
        this.purchaseService.done(purchaseDoneVo);

        return R.ok();
    }

}

package com.von.ware.to;

import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/10/30 23:03
 * @Version 1.0
 */
@Data
@NotNull
@AllArgsConstructor
public class WareSkuTo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku名称
     */
    private String skuName;

}

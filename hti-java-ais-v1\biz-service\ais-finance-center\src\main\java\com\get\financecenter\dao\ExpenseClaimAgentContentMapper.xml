<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ExpenseClaimAgentContentMapper">
    <select id="getExpenseClaimAgentContents" resultType="com.get.financecenter.vo.ExpenseClaimAgentContentVo">
        SELECT uecac.*
        FROM u_expense_claim_agent_content uecac
        WHERE 1=1
        <if test="expenseClaimAgentContentDto.keyWord != null and expenseClaimAgentContentDto.keyWord !=''">
            AND uecac.name LIKE CONCAT('%',#{expenseClaimAgentContentDto.keyWord},'%')
        </if>

        ORDER BY uecac.view_order DESC

    </select>

    <select id="selectByName" resultType="boolean">
        SELECT EXISTS(SELECT 1 FROM u_expense_claim_agent_content WHERE name = #{name})
    </select>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        SELECT
        IFNULL(max(view_order)+1,0) view_order
        FROM
        u_expense_claim_agent_content
    </select>

    <update id="updateBatchById">
        <foreach collection="updateList" item="item" index="index" separator=";">
            UPDATE u_expense_claim_agent_content
            SET
            <if test="item.viewOrder != null and item.viewOrder !=''">
                view_order = #{item.viewOrder},
            </if>
            gmt_modified = #{item.gmtModified},
            gmt_modified_user = #{item.gmtModifiedUser}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="checkName" resultType="int">
        select count(*) from u_expense_claim_agent_content uecac where uecac.name = #{name}
    </select>
</mapper>
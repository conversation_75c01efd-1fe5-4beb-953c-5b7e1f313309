package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.financecenter.entity.Bank;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BankMapper extends BaseMapper<Bank> {
    @Override
    int insert(Bank record);

    int insertSelective(Bank record);

    List<BaseSelectEntity> bankSelect(@Param("fkCurrencyTypeNum") String fkCurrencyTypeNum);
}
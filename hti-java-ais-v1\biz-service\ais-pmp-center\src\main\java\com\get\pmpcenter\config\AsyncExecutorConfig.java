package com.get.pmpcenter.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:04
 * @Version 1.0
 * 线程池配置类
 */
@Configuration
@ConfigurationProperties(prefix = "async.executor")
@Data
public class AsyncExecutorConfig {

    @ApiModelProperty("核心线程数")
    private int corePoolSize;

    @ApiModelProperty("最大线程数")
    private int maxPoolSize;

    @ApiModelProperty("队列容量")
    private int queueCapacity;

    @ApiModelProperty("线程名称前缀")
    private String threadNamePrefix;
}

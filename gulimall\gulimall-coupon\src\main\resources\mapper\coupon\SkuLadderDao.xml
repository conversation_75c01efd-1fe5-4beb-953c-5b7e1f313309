<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.von.coupon.dao.SkuLadderDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.von.coupon.entity.SkuLadderEntity" id="skuLadderMap">
        <result property="id" column="id"/>
        <result property="skuId" column="sku_id"/>
        <result property="fullCount" column="full_count"/>
        <result property="discount" column="discount"/>
        <result property="price" column="price"/>
        <result property="addOther" column="add_other"/>
    </resultMap>


</mapper>
package com.get.remindercenter.utils;

import com.get.core.tool.utils.GeneralTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;
@Slf4j
@RefreshScope
@Component
public class ReminderTemplateUtils {

    public static String getReminderTemplate(Map<String, String> map, String htmlText) {
        if (GeneralTool.isNotEmpty(map)) {
            for (String s : map.keySet()) {
                if (GeneralTool.isNotEmpty(map.get(s))) {
                    htmlText = htmlText.replace("${" + s + "}", map.get(s));
                } else {
                    htmlText = htmlText.replace("${" + s + "}", "");
                }
            }
        }
        return htmlText;
    }

}

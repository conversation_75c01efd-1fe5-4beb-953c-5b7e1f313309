#服务器端口
server:
  port: 1198


#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: platformdb
      datasource:
        platformdb:
          url: ${get.datasource.prod.platformdb.url}
          username: ${get.datasource.prod.platformdb.username}
          password: ${get.datasource.prod.platformdb.password}
        registrationdb:
          url: ${get.datasource.prod.registrationdb.url}
          username: ${get.datasource.prod.registrationdb.username}
          password: ${get.datasource.prod.registrationdb.password}
        appmsodb:
          url: ${get.datasource.prod.appmsodb.url}
          username: ${get.datasource.prod.appmsodb.username}
          password: ${get.datasource.prod.appmsodb.password}
        appmsologdb:
          url: ${get.datasource.prod.appmsologdb.url}
          username: ${get.datasource.prod.appmsologdb.username}
          password: ${get.datasource.prod.appmsologdb.password}
        aisfiledb:
          url: ${get.datasource.prod.aisfiledb.url}
          username: ${get.datasource.prod.aisfiledb.username}
          password: ${get.datasource.prod.aisfiledb.password}
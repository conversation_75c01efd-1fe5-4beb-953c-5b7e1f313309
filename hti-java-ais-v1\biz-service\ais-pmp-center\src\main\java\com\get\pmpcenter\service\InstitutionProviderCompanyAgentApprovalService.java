package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.dto.agent.ApprovalAgentCompanyDto;
import com.get.pmpcenter.dto.agent.SubmitAgentApprovalDto;
import com.get.pmpcenter.entity.InstitutionProviderCompanyAgent;
import com.get.pmpcenter.entity.InstitutionProviderCompanyAgentApproval;
import com.get.pmpcenter.vo.common.StaffVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/31
 * @Version 1.0
 * @apiNote:代理佣金方案审核service
 */
public interface InstitutionProviderCompanyAgentApprovalService extends IService<InstitutionProviderCompanyAgentApproval> {

    /**
     * 初始化代理佣金方案审核记录-根据学校提供商和分公司确认唯一
     *
     * @param institutionProviderId
     * @param companyId
     */
    InstitutionProviderCompanyAgent initApproveRecord(Long institutionProviderId, Long companyId);

    /**
     * 提交代理佣金方案审核
     *
     * @param submitApprovalDto
     */
    void submitAgentApproval(SubmitAgentApprovalDto submitApprovalDto);


    /**
     * 审批代理佣金方案
     *
     * @param approvalAgentCompanyDto
     */
    void approvalApprovalAgentCompany(ApprovalAgentCompanyDto approvalAgentCompanyDto);


    /**
     * 获取代理佣金方案审核记录
     *
     * @param institutionProviderId
     * @param companyId
     * @return
     */
    List<InstitutionProviderCompanyAgentApproval> getAgentApprovalList(Long institutionProviderId, Long companyId);

    /**
     * 获取代理佣金方案审核权限
     *
     * @param institutionProviderId
     * @param companyId
     * @return 1:沒有提交审核 2:已提交审核但是没有权限 3:已提交审核且有权限
     */
    Integer getAgentApprovalPermission(Long institutionProviderId, Long companyId);

    /**
     * 代理佣金方案审核人列表
     * @return
     */
    List<StaffVo> getAgentStaff();
}

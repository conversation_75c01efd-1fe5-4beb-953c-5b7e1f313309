<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.resumecenter.dao.ResumeMapper">
    <resultMap id="BaseResultMap" type="com.get.resumecenter.entity.Resume">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId"/>
        <result column="fk_resume_type_id" jdbcType="BIGINT" property="fkResumeTypeId"/>
        <result column="resume_guid" jdbcType="VARCHAR" property="resumeGuid"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="gender" jdbcType="INTEGER" property="gender"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="height" jdbcType="INTEGER" property="height"/>
        <result column="start_working_year" jdbcType="INTEGER" property="startWorkingYear"/>
        <result column="identity_card" jdbcType="VARCHAR" property="identityCard"/>
        <result column="nationality" jdbcType="VARCHAR" property="nationality"/>
        <result column="residence" jdbcType="VARCHAR" property="residence"/>
        <result column="marriage" jdbcType="VARCHAR" property="marriage"/>
        <result column="political" jdbcType="VARCHAR" property="political"/>
        <result column="home_tel" jdbcType="VARCHAR" property="homeTel"/>
        <result column="mobile_area_code" jdbcType="VARCHAR" property="mobileAreaCode"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="qq" jdbcType="VARCHAR" property="qq"/>
        <result column="wechat" jdbcType="VARCHAR" property="wechat"/>
        <result column="whatsapp" jdbcType="VARCHAR" property="whatsapp"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>

    <insert id="insertSelective" parameterType="com.get.resumecenter.entity.Resume" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_resume
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="fkResumeTypeId != null">
                fk_resume_type_id,
            </if>
            <if test="resumeGuid != null">
                resume_guid,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="gender != null">
                gender,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="height != null">
                height,
            </if>
            <if test="startWorkingYear != null">
                start_working_year,
            </if>
            <if test="identityCard != null">
                identity_card,
            </if>
            <if test="nationality != null">
                nationality,
            </if>
            <if test="residence != null">
                residence,
            </if>
            <if test="marriage != null">
                marriage,
            </if>
            <if test="political != null">
                political,
            </if>
            <if test="homeTel != null">
                home_tel,
            </if>
            <if test="mobileAreaCode != null">
                mobile_area_code,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="zipCode != null">
                zip_code,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="qq != null">
                qq,
            </if>
            <if test="wechat != null">
                wechat,
            </if>
            <if test="whatsapp != null">
                whatsapp,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="fkResumeTypeId != null">
                #{fkResumeTypeId,jdbcType=BIGINT},
            </if>
            <if test="resumeGuid != null">
                #{resumeGuid,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="gender != null">
                #{gender,jdbcType=INTEGER},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=DATE},
            </if>
            <if test="height != null">
                #{height,jdbcType=INTEGER},
            </if>
            <if test="startWorkingYear != null">
                #{startWorkingYear,jdbcType=INTEGER},
            </if>
            <if test="identityCard != null">
                #{identityCard,jdbcType=VARCHAR},
            </if>
            <if test="nationality != null">
                #{nationality,jdbcType=VARCHAR},
            </if>
            <if test="residence != null">
                #{residence,jdbcType=VARCHAR},
            </if>
            <if test="marriage != null">
                #{marriage,jdbcType=VARCHAR},
            </if>
            <if test="political != null">
                #{political,jdbcType=VARCHAR},
            </if>
            <if test="homeTel != null">
                #{homeTel,jdbcType=VARCHAR},
            </if>
            <if test="mobileAreaCode != null">
                #{mobileAreaCode,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="qq != null">
                #{qq,jdbcType=VARCHAR},
            </if>
            <if test="wechat != null">
                #{wechat,jdbcType=VARCHAR},
            </if>
            <if test="whatsapp != null">
                #{whatsapp,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="isExistByResumeTypeId" resultType="java.lang.Boolean">
         SELECT IFNULL(max(id),0) id from m_resume where fk_resume_type_id=#{resumeTypeId}
    </select>
    <select id="getByGuid" resultMap="BaseResultMap">
        SELECT * from m_resume where resume_guid=#{guid}
    </select>
</mapper>
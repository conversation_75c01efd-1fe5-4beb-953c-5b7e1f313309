package com.get.financecenter.dao;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.financecenter.dto.InvoiceBindDto;
import com.get.financecenter.entity.InvoiceTarget;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Mapper
public interface InvoiceTargetMapper extends GetMapper<InvoiceTarget> {

    /**
     * 获取发票的渠道id集合
     * @param invoiceId
     * @return
     */
    List<InvoiceTarget> getTargetIdAndTypeKeyByInvoiceId(Long invoiceId);

    List<InvoiceTarget> getTargetIdAndTypeKeyByInvoiceIds(@Param("list") Set<Long> invoiceId);


    List<String> getInvoiceNum(@Param("invoiceBindDto") InvoiceBindDto invoiceBindDto);
}

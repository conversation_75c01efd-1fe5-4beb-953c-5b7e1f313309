package com.get.pmpcenter.strategy.workbench;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.permissioncenter.enums.WorkbenchApprovalTypeEnum;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.entity.AgentCommissionPlanApproval;
import com.get.pmpcenter.entity.AgentCommissionPlanCompany;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.mapper.AgentCommissionPlanApprovalMapper;
import com.get.pmpcenter.mapper.AgentCommissionPlanCompanyMapper;
import com.get.pmpcenter.mapper.PermissionCenterMapper;
import com.get.pmpcenter.service.AgentCommissionPlanService;
import com.get.pmpcenter.strategy.WorkbenchApprovalStrategy;
import com.get.pmpcenter.vo.common.StaffVo;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/4/27
 * @Version 1.0
 * @apiNote:
 */
@Component
public class AgentCommissionPlanApprovalStrategy implements WorkbenchApprovalStrategy {

    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private AgentCommissionPlanApprovalMapper agentCommissionPlanApprovalMapper;
    @Autowired
    private AgentCommissionPlanCompanyMapper agentCommissionPlanCompanyMapper;

    @Override
    public List<WorkbenchApprovalVo> getApprovalList(PmpWorkbenchApprovalDto workbenchApprovalDto) {
        List<AgentCommissionPlanApproval> approvals;
        if (Objects.nonNull(workbenchApprovalDto.getApprovalStatus())) {
            approvals = getAgentApprovalListByStatus(workbenchApprovalDto.getStaffId()
                    , workbenchApprovalDto.getApprovalStatus(), workbenchApprovalDto.getLoginId());
        } else {
            approvals = new ArrayList<>();
            Arrays.asList(ApprovalStatusEnum.PENDING_APPROVAL.getCode(), ApprovalStatusEnum.REJECT.getCode()).stream().forEach(status -> {
                List<AgentCommissionPlanApproval> statusList = getAgentApprovalListByStatus(workbenchApprovalDto.getStaffId(),
                        status, workbenchApprovalDto.getLoginId());
                approvals.addAll(statusList);
            });
        }
        //获取审批记录Map-带去重
        Map<Long, AgentCommissionPlanApproval> approvalMap = approvals.stream()
                .sorted(Comparator.comparing(AgentCommissionPlanApproval::getGmtCreate).reversed())
                .collect(Collectors.toList())
                .stream().collect(Collectors.toMap(
                        AgentCommissionPlanApproval::getFkAgentCommissionPlanId,
                        Function.identity(),
                        (existing, replacement)
                                -> existing.getGmtCreate().after(replacement.getGmtCreate()) ? existing : replacement));
        if (MapUtils.isNotEmpty(approvalMap)) {
            //代理方案map
            Map<Long, AgentCommissionPlan> commissionPlanMap = agentCommissionPlanService.list(
                            new LambdaQueryWrapper<AgentCommissionPlan>()
                                    .in(AgentCommissionPlan::getId, approvalMap.keySet()))
                    .stream()
                    .collect(Collectors.toMap(
                            AgentCommissionPlan::getId,
                            Function.identity(),
                            (existing, replacement) -> replacement));

            //分公司map
            Map<Long, AgentCommissionPlanCompany> planCompanyMap = agentCommissionPlanCompanyMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanCompany>()
                            .in(AgentCommissionPlanCompany::getFkAgentCommissionPlanId, approvalMap.keySet()))
                    .stream()
                    .collect(Collectors.toMap(
                            AgentCommissionPlanCompany::getFkAgentCommissionPlanId,
                            Function.identity(),
                            (existing, replacement) -> replacement));
            //发起人id
            List<String> initiatorLoginIds = approvalMap.values().stream().map(AgentCommissionPlanApproval::getGmtCreateUser).distinct().collect(Collectors.toList());
            List<Long> passOrRejectPlanIds = approvals.stream().filter(approval -> approval.getApprovalStatus() >= ApprovalStatusEnum.PASS.getCode())
                    .map(AgentCommissionPlanApproval::getFkAgentCommissionPlanId)
                    .collect(Collectors.toList());
            Map<Long, String> passOrRejectPlanApprovalMap = Collections.emptyMap();
            if (CollectionUtils.isNotEmpty(passOrRejectPlanIds)) {
                passOrRejectPlanApprovalMap = agentCommissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>()
                                .eq(AgentCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                                .in(AgentCommissionPlanApproval::getFkAgentCommissionPlanId, passOrRejectPlanIds))
                        .stream()
                        .collect(Collectors.groupingBy(
                                AgentCommissionPlanApproval::getFkAgentCommissionPlanId,
                                Collectors.collectingAndThen(
                                        Collectors.maxBy(Comparator.comparing(AgentCommissionPlanApproval::getGmtCreate)),
                                        opt -> opt.map(AgentCommissionPlanApproval::getGmtCreateUser).orElse(null)
                                )
                        ));
                passOrRejectPlanApprovalMap = passOrRejectPlanApprovalMap.entrySet().stream()
                        .filter(entry -> entry.getValue() != null)
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue
                        ));
                initiatorLoginIds.addAll(passOrRejectPlanApprovalMap.values());
            }
            List<StaffVo> staffVos = permissionCenterMapper.getStaffListByLoginIds(initiatorLoginIds);
            Map<String, String> nameMap = staffVos.stream().collect(Collectors.toMap(StaffVo::getLoginId, StaffVo::getName));
            Map<Long, String> finalPassOrRejectPlanApprovalMap = passOrRejectPlanApprovalMap;
            return approvalMap.keySet().stream().map(id -> {
                WorkbenchApprovalVo vo = new WorkbenchApprovalVo();
                vo.setId(id);
                vo.setName(Objects.nonNull(commissionPlanMap.get(id)) ? commissionPlanMap.get(id).getName() : "");
                vo.setApprovalType(WorkbenchApprovalTypeEnum.PMP_AGENT.getCode());
                vo.setApprovalStatus(approvalMap.get(id).getApprovalStatus());
                vo.setGmtCreate(approvalMap.get(id).getGmtCreate());
                Map<String, Object> defData = new HashMap<>();
                defData.put("companyId", Objects.nonNull(planCompanyMap.get(id)) ? planCompanyMap.get(id).getFkCompanyId() : null);
                defData.put("institutionProviderId", Objects.nonNull(commissionPlanMap.get(id)) ? commissionPlanMap.get(id).getFkInstitutionProviderId() : null);
                defData.put("planId", id);
                if (vo.getApprovalStatus() >= ApprovalStatusEnum.PASS.getCode()) {
                    String initiatorId = finalPassOrRejectPlanApprovalMap.getOrDefault(id, "");
                    if (StringUtils.isNotBlank(initiatorId)) {
                        vo.setInitiatorIdName(nameMap.getOrDefault(initiatorId, ""));
                    }
                } else {
                    vo.setInitiatorIdName(nameMap.getOrDefault(approvalMap.get(id).getGmtCreateUser(), ""));
                }
                vo.setDefData(defData);
                return vo;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public String getApprovalType() {
        return WorkbenchApprovalTypeEnum.PMP_AGENT.getCode();
    }

    private List<AgentCommissionPlanApproval> getAgentApprovalListByStatus(Long staffId, Integer approvalStatus, String loginId) {
        //只需要查询待审核和审核失败的方案 只会有1和3两种状态：1：需要我审核的 3：我提交的但是是审核失败的
        //1：需要我审核的
        if (approvalStatus.equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            return agentCommissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>()
                    .eq(AgentCommissionPlanApproval::getFkStaffId, staffId)
                    .eq(AgentCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                    .exists("select 1 from m_agent_commission_plan where id = m_agent_commission_plan_approval.fk_agent_commission_plan_id and approval_status = 1 and is_active = 1"));
        }
        //3：我提交的但是是审核失败的
        //所有审核失败的
        List<AgentCommissionPlanApproval> approvals = agentCommissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>()
                .eq(AgentCommissionPlanApproval::getApprovalStatus, approvalStatus)
                .exists("select 1 from m_agent_commission_plan where id = m_agent_commission_plan_approval.fk_agent_commission_plan_id and approval_status = " + approvalStatus +
                        " and is_active = 1"));
        return approvals.stream().map(approval -> {
            //过滤最新提交记录不是当前用户的
            Optional<AgentCommissionPlanApproval> latestSubmitApproval = agentCommissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>()
                            .eq(AgentCommissionPlanApproval::getFkAgentCommissionPlanId, approval.getFkAgentCommissionPlanId())
                            .orderByDesc(AgentCommissionPlanApproval::getGmtCreate)).stream()
                    .filter(item -> item.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode()))
                    .max(Comparator.comparing(AgentCommissionPlanApproval::getGmtCreate));
            if (!latestSubmitApproval.isPresent()) {
                return null;
            }
            AgentCommissionPlanApproval latestSubmit = latestSubmitApproval.get();
            if (!latestSubmit.getGmtCreateUser().equals(loginId)) {
                return null;
            }
            return approval;
        }).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        //0-待审核-需要指定员工审核
//        if (approvalStatus.equals(ApprovalStatusEnum.UN_COMMITTED.getCode())) {
//            return agentCommissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>()
//                    .eq(AgentCommissionPlanApproval::getFkStaffId, staffId)
//                    .eq(AgentCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
//                    .exists("select 1 from m_agent_commission_plan where id = m_agent_commission_plan_approval.fk_agent_commission_plan_id and approval_status = 1 "));
//        }
//        //1-审核中-表示当前员工创建的需要等待其他人审核
//        //2-已通过-当前员工创建的并且审核通过的+当前员工审核通过的
//        //3-已拒绝-当前员工创建的并且审核拒绝的+当前员工审核拒绝的
//        if (approvalStatus >= ApprovalStatusEnum.PENDING_APPROVAL.getCode()) {
//            //当前员工创建的并且是指定状态的方案
//            List<AgentCommissionPlanApproval> approvalList = agentCommissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>()
//                    .eq(AgentCommissionPlanApproval::getApprovalStatus, approvalStatus)
//                    .exists("select 1 from m_agent_commission_plan where id = m_agent_commission_plan_approval.fk_agent_commission_plan_id and approval_status = " + approvalStatus +
//                            " and gmt_create_user = '" + loginId + "'"));
//            if (approvalStatus >= ApprovalStatusEnum.PASS.getCode()) {
//                //当前员工审核通过或者驳回的方案
//                List<AgentCommissionPlanApproval> currentApprovalList = agentCommissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>()
//                        .eq(AgentCommissionPlanApproval::getFkStaffId, staffId)
//                        .eq(AgentCommissionPlanApproval::getApprovalStatus, approvalStatus)
//                        .exists("select 1 from m_agent_commission_plan where id = m_agent_commission_plan_approval.fk_agent_commission_plan_id " +
//                                " and approval_status = " + approvalStatus));
//                approvalList.addAll(currentApprovalList);
//            }
//            return approvalList;
//        }
//        return Collections.emptyList();
    }
}

package com.get.pmpcenter.controller.agent;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.annotation.OperationLogger;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.pmpcenter.service.AgentCommissionTypeAgentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "代理佣金分类和代理关系管理")
@RestController
@RequestMapping("/agentCommissionTypeAndAgent")
public class AgentCommissionTypeAgentController {
    @Resource
    private AgentCommissionTypeAgentService agentCommissionTypeAgentService;

    /**
     * 新增代理佣金分类和代理关系
     *
     * @param
     * @return
     */
    @ApiOperation(value = "新增接口", notes = "新增代理佣金分类和代理关系")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "功能中心/代理佣金分类/新增")
    @PostMapping("addAgentCommissionTypeAndAgent")
    public ResponseBo addAgentCommissionTypeAgent(@RequestBody AgentCommissionTypeAgentDto agentCommissionTypeAgentDto) {
        agentCommissionTypeAgentService.add(agentCommissionTypeAgentDto);
        return ResponseBo.ok();
    }

    /**
     * 删除代理佣金分类和代理关系
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "删除代理佣金分类和代理关系")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "功能中心/代理佣金分类/删除")
    @DeleteMapping("deleteAgentCommissionTypeAndAgent")
    public ResponseBo deleteAgentCommissionTypeAgent(@Param("agentCommissionTypeAgentDto")Long id) {
        return new ResponseBo<>(agentCommissionTypeAgentService.delete(id));
    }

    @ApiOperation(value = "查询接口", notes = "查询代理佣金分类和代理关系")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "功能中心/代理佣金分类/查询")
    @PostMapping("selectAgentCommissionTypeAndAgent")
    public ResponseBo getAgentCommissionTypeList(@RequestBody SearchBean<AgentCommissionTypeAgentDto> page) {
        return agentCommissionTypeAgentService.dataList(page);
    }


}

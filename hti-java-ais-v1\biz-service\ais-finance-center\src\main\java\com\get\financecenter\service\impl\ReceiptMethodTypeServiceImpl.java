package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ReceiptMethodTypeMapper;
import com.get.financecenter.dao.VouchReceiptRegisterMapper;
import com.get.financecenter.dto.ReceiptMethodTypeDto;
import com.get.financecenter.entity.ReceiptMethodType;
import com.get.financecenter.entity.VouchReceiptRegister;
import com.get.financecenter.enums.RelationTargetKeyEnum;
import com.get.financecenter.service.ReceiptMethodTypeService;
import com.get.financecenter.utils.GetAccountingCodeNameUtils;
import com.get.financecenter.vo.BaseSelectVo;
import com.get.financecenter.vo.ReceiptMethodTypeVo;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 收款方式类型管理服务实现类
 */
@Service("financeReceiptMethodTypeService")
public class ReceiptMethodTypeServiceImpl extends ServiceImpl<ReceiptMethodTypeMapper, ReceiptMethodType> implements ReceiptMethodTypeService {
    @Resource
    private ReceiptMethodTypeMapper receiptMethodTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private GetAccountingCodeNameUtils getAccountingCodeNameUtils;
    @Resource
    private VouchReceiptRegisterMapper vouchReceiptRegisterMapper;

    @Override
    public List<ReceiptMethodTypeVo> getReceiptMethodTypes(ReceiptMethodTypeDto receiptMethodTypeDto, Page page) {
        if (GeneralTool.isEmpty(receiptMethodTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isNotEmpty(receiptMethodTypeDto.getKeyWord())){
            receiptMethodTypeDto.setKeyWord(receiptMethodTypeDto.getKeyWord().replace(" ", "").trim());
        }
        LambdaQueryWrapper<ReceiptMethodType> wrapper = new LambdaQueryWrapper();
        IPage<ReceiptMethodType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);

        List<ReceiptMethodTypeVo> receiptMethodTypes = receiptMethodTypeMapper.getReceiptMethodTypes(pages, receiptMethodTypeDto);
        page.setAll((int) pages.getTotal());

        if (GeneralTool.isNotEmpty(receiptMethodTypes)){
            for (ReceiptMethodTypeVo paymentMethodTypeVo : receiptMethodTypes) {
                //处理科目类型参数
                if (GeneralTool.isNotEmpty(paymentMethodTypeVo.getFkAccountingItemId())){
                    String accountingName = getAccountingCodeNameUtils.setAccountingCodeName(paymentMethodTypeVo.getFkAccountingItemId());
                    paymentMethodTypeVo.setAccountingItemName(accountingName);
                }
                //处理关联类型名称参数
                if (GeneralTool.isNotEmpty(paymentMethodTypeVo.getRelationTargetKey())){
                    paymentMethodTypeVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(paymentMethodTypeVo.getRelationTargetKey()));
                }

            }

        }
        return receiptMethodTypes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(List<ReceiptMethodTypeDto> receiptMethodTypeDtos) {
        if (GeneralTool.isEmpty(receiptMethodTypeDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ReceiptMethodTypeDto receiptMethodTypeDto : receiptMethodTypeDtos) {
            ReceiptMethodType receiptMethodType = BeanCopyUtils.objClone(receiptMethodTypeDto, ReceiptMethodType::new);
            checkParam(receiptMethodType);
            receiptMethodType.setTypeName(receiptMethodType.getTypeName().replace(" ", "").trim());
            if (receiptMethodTypeMapper.checkName(receiptMethodType.getTypeName()) > 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name") +" (" + receiptMethodType.getTypeName()+")");
            }
            utilService.setCreateInfo(receiptMethodType);
            receiptMethodType.setViewOrder(receiptMethodTypeMapper.getMaxViewOrder());
            int insert = receiptMethodTypeMapper.insert(receiptMethodType);
            if (insert <= 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }

    }

    @Override
    public void updateReceiptMethodTypes(ReceiptMethodTypeDto receiptMethodTypeDto) {
        if (GeneralTool.isEmpty(receiptMethodTypeDto)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(receiptMethodTypeDto.getId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ReceiptMethodType searchReceiptMethodType = receiptMethodTypeMapper.selectById(receiptMethodTypeDto.getId());
        if (GeneralTool.isEmpty(searchReceiptMethodType)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_obj_null"));
        }
        ReceiptMethodType receiptMethodType = BeanCopyUtils.objClone(receiptMethodTypeDto, ReceiptMethodType::new);
        if (!receiptMethodType.getTypeName().equals(searchReceiptMethodType.getTypeName())){

            if (receiptMethodTypeMapper.checkName(receiptMethodType.getTypeName()) > 0){
                throw new GetServiceException(LocaleMessageUtils.getMessage("duplicate_name")  +" (" +  receiptMethodTypeDto.getTypeName()+")");
            }
            receiptMethodType.setTypeName(receiptMethodType.getTypeName().replace(" ", "").trim());
        }
        utilService.setUpdateInfo(receiptMethodType);
        int update = receiptMethodTypeMapper.updateById(receiptMethodType);
        if (update <= 0){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 删除收款方式类型
     * @param id
     */
    @Override
    public void deleteReceiptMethodType(Long id) {
        if (GeneralTool.isEmpty(id)){
            throw  new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<VouchReceiptRegister> vouchReceiptRegisters = vouchReceiptRegisterMapper.selectList(new LambdaQueryWrapper<VouchReceiptRegister>().eq(VouchReceiptRegister::getFkReceiptMethodTypeId, id));
        if (GeneralTool.isNotEmpty(vouchReceiptRegisters)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("cancel_obj_used_by_vouch_receipt_register"));
        }
        ReceiptMethodType receiptMethodType = receiptMethodTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(receiptMethodType)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        receiptMethodTypeMapper.deleteById(id);

    }

    /**
     * 交换类型的排序
     * @param ids
     */
    @Override
    public void sort(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        if (ids.size() != 2){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }

        List<ReceiptMethodType> receiptMethodTypes = receiptMethodTypeMapper.selectBatchIds(ids);
        if (receiptMethodTypes.size() != ids.size()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        Map<Long, Integer> orderMap = receiptMethodTypes.stream().collect(Collectors.toMap(ReceiptMethodType::getId, ReceiptMethodType::getViewOrder));
        List<ReceiptMethodType> updateList = new ArrayList<>();

        for (int i = 0; i < ids.size(); i++) {
            Long currentId = ids.get(i);
            Long swapId = ids.get((i + 1) % ids.size());
            if (orderMap.containsKey(swapId)) {
                ReceiptMethodType entity = receiptMethodTypeMapper.selectById(currentId);
                if (entity.getViewOrder() == orderMap.get(swapId)){
                    throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
                }
                entity.setViewOrder(orderMap.get(swapId));
                utilService.updateUserInfoToEntity(entity);
                updateList.add(entity);
            }
        }

        if (!updateList.isEmpty()) {
            receiptMethodTypeMapper.updateBatchById(updateList);
        }

    }


    /**
     * 拖拽类型的排序
     * @param start
     * @param end
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(Integer start, Integer end) {
        if (GeneralTool.isEmpty(start) || GeneralTool.isEmpty( end)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("sort_param_error"));
        }
        LambdaQueryWrapper<ReceiptMethodType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (end > start){
            lambdaQueryWrapper.between(ReceiptMethodType::getViewOrder,start,end).orderByDesc(ReceiptMethodType::getViewOrder);
        }else {
            lambdaQueryWrapper.between(ReceiptMethodType::getViewOrder,end,start).orderByDesc(ReceiptMethodType::getViewOrder);

        }
        List<ReceiptMethodType> receiptMethodTypes = list(lambdaQueryWrapper);
        List<ReceiptMethodType> updateList = new ArrayList<>();
        if (end > start){
            int finalEnd = end;
            List<ReceiptMethodType> sortedList = Lists.newArrayList();
            ReceiptMethodType receiptMethodTypeLast = receiptMethodTypes.get(receiptMethodTypes.size() - 1);
            sortedList.add(receiptMethodTypeLast);
            receiptMethodTypes.remove(receiptMethodTypes.size() - 1);
            sortedList.addAll(receiptMethodTypes);
            for (ReceiptMethodType receiptMethodType : sortedList) {
                receiptMethodType.setViewOrder(finalEnd);
                finalEnd--;
            }
            updateList.addAll(sortedList);
        }else {
            int finalStart = start;
            List<ReceiptMethodType> sortedList = Lists.newArrayList();
            ReceiptMethodType receiptMethodTypeFirst = receiptMethodTypes.get(0);
            receiptMethodTypes.remove(0);
            sortedList.addAll(receiptMethodTypes);
            sortedList.add(receiptMethodTypeFirst);
            for (ReceiptMethodType receiptMethodType : sortedList) {
                receiptMethodType.setViewOrder(finalStart);
                finalStart--;
            }
            updateList.addAll(sortedList);
        }

        if (GeneralTool.isNotEmpty(updateList)){
            boolean batch = updateBatchById(updateList);
            if (!batch){
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        }
    }

    @Override
    public List<BaseSelectVo> getReceiptMethodTypeDropDown() {
        LambdaQueryWrapper<ReceiptMethodType> receiptMethodTypeLambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 查询字段
        receiptMethodTypeLambdaQueryWrapper.select(ReceiptMethodType::getId, ReceiptMethodType::getTypeName, ReceiptMethodType::getRelationTargetKey, ReceiptMethodType::getFkAccountingItemId);
        List<ReceiptMethodType> receiptMethodTypes = receiptMethodTypeMapper.selectList(receiptMethodTypeLambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(receiptMethodTypes)){
            return receiptMethodTypes.stream().map(receiptMethodType -> {
                BaseSelectVo baseSelectVo = new BaseSelectVo();
                baseSelectVo.setId(receiptMethodType.getId());
                baseSelectVo.setName(receiptMethodType.getTypeName());
                if (GeneralTool.isNotEmpty(receiptMethodType.getRelationTargetKey())){
                    baseSelectVo.setRelationTargetKey(receiptMethodType.getRelationTargetKey());
                    baseSelectVo.setRelationTargetKeyName(RelationTargetKeyEnum.getNameByRelationTargetKey(receiptMethodType.getRelationTargetKey()));
                }
                baseSelectVo.setFkAccountingItemId(receiptMethodType.getFkAccountingItemId());
                baseSelectVo.setAccountingItemName(getAccountingCodeNameUtils.setAccountingCodeName(receiptMethodType.getFkAccountingItemId()));
                return baseSelectVo;
            }).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    private static void checkParam(ReceiptMethodType receiptMethodType) {
        if (GeneralTool.isEmpty(receiptMethodType.getTypeName())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_null"));
        }
        if (GeneralTool.isEmpty(receiptMethodType.getFkAccountingItemId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("accounting_item_id_null"));
        }
    }


}


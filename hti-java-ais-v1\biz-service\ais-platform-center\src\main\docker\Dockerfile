# 基础镜像
FROM adoptopenjdk/openjdk8-openj9:x86_64-alpine-jre8u312-b07_openj9-0.29.0

# 作者
MAINTAINER get

#声明一个挂载点，容器内此路径会对应宿主机的某个文件夹111
VOLUME /tmp

#COPY
ADD target/ais-platform-center.jar  ./app.jar

ENV SPRING_PROFILES_ACTIVE="dev"

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom","-Duser.timezone=Asia/Shanghai", "-jar", "app.jar"]

CMD ["--spring.profiles.active=${SPRING_PROFILES_ACTIVE}"]
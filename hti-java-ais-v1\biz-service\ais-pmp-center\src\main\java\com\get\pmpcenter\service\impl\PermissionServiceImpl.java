package com.get.pmpcenter.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.entity.AgentCommissionPlanInstitution;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanInstitution;
import com.get.pmpcenter.mapper.AgentCommissionPlanInstitutionMapper;
import com.get.pmpcenter.mapper.InstitutionCenterMapper;
import com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanInstitutionMapper;
import com.get.pmpcenter.mapper.PermissionCenterMapper;
import com.get.pmpcenter.service.PermissionService;
import com.get.pmpcenter.vo.common.InstitutionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/5/9
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class PermissionServiceImpl implements PermissionService {

    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionMapper providerCommissionPlanInstitutionMapper;
    @Autowired
    private AgentCommissionPlanInstitutionMapper agentCommissionPlanInstitutionMapper;

    @Override
    public void verifyCurrentUserCountry(Long planId, Long staffId, Integer type) {
        //获取指定用户的国家Ids列表
        List<Long> staffCountryIds = permissionCenterMapper.getStaffCountryIds(staffId);
        if (CollectionUtils.isEmpty(staffCountryIds)) {
            log.error("该员工没有业务国家,员工Id:{}", staffId);
//            throw new GetServiceException("审批人没有对应的业务国家权限，无法审批，请重新选择。");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_APPROVER_NO_COUNTRY_PERMISSION","审批人没有对应的业务国家权限，无法审批，请重新选择。"));
        }
        List<Long> toVerifyInstitutionIds;
        if (type.equals(1)) {
            //合同端
            toVerifyInstitutionIds = providerCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                            .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, planId))
                    .stream()
                    .map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId)
                    .collect(Collectors.toList());
        } else {
            //代理端
            toVerifyInstitutionIds = agentCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                            .eq(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, planId))
                    .stream()
                    .map(AgentCommissionPlanInstitution::getFkInstitutionId)
                    .collect(Collectors.toList());
        }
        List<Long> institutionCountryIds = institutionCenterMapper.getInstitutionListByIds(toVerifyInstitutionIds)
                .stream()
                .map(InstitutionVo::getCountryId)
                .distinct()
                .collect(Collectors.toList());

        boolean hasPermission = institutionCountryIds.stream()
                .anyMatch(staffCountryIds::contains);

        if (!hasPermission) {
            log.error("员工[{}]没有权限审批这些国家: {}", staffId, institutionCountryIds);
//            throw new GetServiceException("审批人没有对应的业务国家权限，无法审批，请重新选择。");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_APPROVER_NO_COUNTRY_PERMISSION","审批人没有对应的业务国家权限，无法审批，请重新选择。"));
        }
    }
}

package com.get.aisplatformcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aisplatformcenterap.vo.MBannerVo;
import com.get.aisplatformcenterap.entity.MBannerEntity;
import com.get.aisplatformcenterap.dto.BannerPutAwayParamsDto;
import com.get.aisplatformcenterap.dto.MBannerParamsDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_banner(banner后台管理)】的数据库操作Mapper
* @createDate 2024-12-16 18:03:59
* @Entity com.get.partnercenter.entity.MBannerEntity
*/
@Mapper
public interface MBannerMapper extends BaseMapper<MBannerEntity> {
    List<MBannerVo> searchBannerPage(IPage<MBannerVo> page, @Param("query") MBannerParamsDto mBannerParamsDto);

    int updatePutAway(BannerPutAwayParamsDto vo);

    MBannerVo getDetail(MBannerParamsDto mBannerParamsDto);

}





package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.vo.CurrencyTypeVo;
import com.get.financecenter.entity.CurrencyType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/9/17 11:46
 * @verison: 1.0
 * @description: 货币类型mapper
 */
@Mapper
public interface CurrencyTypeMapper extends BaseMapper<CurrencyType> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(CurrencyType record);

    /**
     * feign调用 根据币种编号查找币种名称
     *
     * @param num
     * @return
     */
    String getCurrencyNameByNum(@Param("num") String num);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * 根据PublicLevel获取币种
     * @param key
     * @return
     */
    List<CurrencyTypeVo> getCurrencyByPublicLevel(@Param("key")Integer key);
}
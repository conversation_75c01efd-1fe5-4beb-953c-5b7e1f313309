package com.von.ware;

import com.von.common.vo.SkuHasStockVo;
import com.von.ware.entity.PurchaseDetailEntity;
import com.von.ware.service.PurchaseDetailService;
import com.von.ware.service.WareSkuService;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.List;

@SpringBootTest
@RequiredArgsConstructor
class GulimallWareApplicationTests {


    private final PurchaseDetailService purchaseDetailService;


    private final WareSkuService wareSkuService;

    @Test
    void contextLoads() {
        List<PurchaseDetailEntity> list = this.purchaseDetailService.list();
        System.out.println(list);
    }

    @Test
    public void test28() throws Exception {
        List<SkuHasStockVo> skuHasStock = this.wareSkuService.getSkuHasStock(Arrays.asList(2L, 3L, 4L));
        System.out.println(skuHasStock);
    }

}

package com.get.aisplatformcenter.feign;

import com.get.aisplatformcenter.service.ULabelService;
import com.get.aisplatformcenter.service.UserInfoService;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.result.SearchBean;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.AgentLabelDto;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Set;


@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class PlatformCenterClient implements IPlatformCenterClient {

    private final UserInfoService userInfoService;
    private final ULabelService uLabelService;


    @Override
    public Result<Set<Long>> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName) {
        return Result.data(userInfoService.getUserIdsByParam(userName, fkAreaCityId, bdName));
    }

    @Override
    public Result<Map<Long, String>> getUserNickNamesByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getUserNickNamesByUserIds(userIds));
    }

    @Override
    public Result<Map<Long, String>> getCityNamesByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getCityNamesByUserIds(userIds));
    }

    @Override
    public Result<Map<Long, String>> getMobileByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getMobileByUserIds(userIds));
    }

    @Override
    public Result<Set<Long>> getUserIdsByNameOrMobile(String userName, String phoneNumber) {
        return Result.data(userInfoService.getUserIdsByNameOrMobile(userName, phoneNumber));
    }

    @Override
    public Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(Set<Long> userIds) {
        return Result.data(userInfoService.getUserInfoDtoByIds(userIds));
    }

    @Override
    public Result<LabelSearchAboutAgentVo> getLabelByLabelTypeIdAndKeyWord(SearchBean<AgentLabelDto> page) {
        return Result.data(uLabelService.getLabelByLabelTypeIdAndKeyWord(page.getData(), page));
    }

}

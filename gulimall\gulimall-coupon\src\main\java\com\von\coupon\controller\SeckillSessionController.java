package com.von.coupon.controller;

import com.von.common.utils.PageUtils;
import com.von.common.utils.R;
import com.von.coupon.entity.SeckillSessionEntity;
import com.von.coupon.service.SeckillSessionService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;



/**
 * 秒杀活动场次
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 23:29:50
 */
@RestController
@RequestMapping("coupon/seckillsession")
@RequiredArgsConstructor
public class SeckillSessionController {

    private final SeckillSessionService seckillSessionService;


    /**
     * 获取最新3天的活动场次和 sku数据
     *
     * @return
     * @throws Exception
     */
    @ApiOperation("获取最新3天的活动场次和 sku数据")
    @GetMapping("/getLatest3DaySession")
    public R getLatest3DaySession() throws Exception {
        List<SeckillSessionEntity> seckillSessionEntityList = this.seckillSessionService.getLatest3DaySessionAndSku();
                
        return R.ok().setData(seckillSessionEntityList);
    }

    /**
     * 列表
     */
    @GetMapping("/list")
    // @RequiresPermissions("coupon:seckillsession:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = seckillSessionService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @GetMapping("/info/{id}")
    // @RequiresPermissions("coupon:seckillsession:info")
    public R info(@PathVariable("id") Long id){
		SeckillSessionEntity seckillSession = seckillSessionService.getById(id);

        return R.ok().put("seckillSession", seckillSession);
    }

    /**
     * 保存
     */
    @PostMapping("/save")
    // @RequiresPermissions("coupon:seckillsession:save")
    public R save(@RequestBody SeckillSessionEntity seckillSession){
		seckillSessionService.save(seckillSession);

        return R.ok();
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    // @RequiresPermissions("coupon:seckillsession:update")
    public R update(@RequestBody SeckillSessionEntity seckillSession){
		seckillSessionService.updateById(seckillSession);

        return R.ok();
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete")
    // @RequiresPermissions("coupon:seckillsession:delete")
    public R delete(@RequestBody Long[] ids){
		seckillSessionService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

}

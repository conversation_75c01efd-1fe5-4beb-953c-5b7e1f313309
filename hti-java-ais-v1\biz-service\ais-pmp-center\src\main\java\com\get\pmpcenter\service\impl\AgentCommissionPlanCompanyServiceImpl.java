package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.entity.AgentCommissionPlanCompany;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.mapper.*;
import com.get.pmpcenter.service.AgentCommissionPlanCompanyService;
import com.get.pmpcenter.service.AgentCommissionPlanService;
import com.get.pmpcenter.service.InstitutionProviderCommissionPlanService;
import com.get.pmpcenter.service.InstitutionProviderContractService;
import com.get.pmpcenter.vo.agent.UserPlanIds;
import com.get.pmpcenter.vo.common.CompanyVo;
import com.get.pmpcenter.vo.institution.ProviderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionPlanCompanyServiceImpl extends ServiceImpl<AgentCommissionPlanCompanyMapper, AgentCommissionPlanCompany> implements AgentCommissionPlanCompanyService {

    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private AgentCommissionPlanCompanyMapper commissionPlanCompanyMapper;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private InstitutionProviderCommissionPlanMapper commissionPlanMapper;
    @Autowired
    private InstitutionProviderContractService contractService;
    @Autowired
    private AgentCommissionPlanMapper agentCommissionPlanMapper;
    @Autowired
    private InstitutionProviderCommissionPlanService commissionPlanService;
    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;

    @Override
    public List<CompanyVo> companyList() {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (companyIds.isEmpty()) {
            log.error("用户所属分公司列表为空,用户:{}", SecureUtil.getLoginId());
            return Collections.emptyList();
        }
        return permissionCenterMapper.getCompanyList(companyIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAgentCommissionPlanCompany(Long agentCommissionPlanId, Long companyId) {
        UserInfo user = SecureUtil.getUser();
        AgentCommissionPlanCompany planCompany = commissionPlanCompanyMapper.selectOne(new LambdaQueryWrapper<AgentCommissionPlanCompany>()
                .eq(AgentCommissionPlanCompany::getFkAgentCommissionPlanId, agentCommissionPlanId));
        if (Objects.nonNull(planCompany) && !planCompany.getFkCompanyId().equals(companyId)) {
            planCompany.setFkCompanyId(companyId);
            planCompany.setGmtModified(new Date());
            planCompany.setGmtModifiedUser(user.getLoginId());
            commissionPlanCompanyMapper.updateById(planCompany);
            return;
        }
        if (Objects.isNull(planCompany)) {
            planCompany = new AgentCommissionPlanCompany();
            planCompany.setFkAgentCommissionPlanId(agentCommissionPlanId);
            planCompany.setFkCompanyId(companyId);
            planCompany.setGmtCreate(new Date());
            planCompany.setGmtCreateUser(user.getLoginId());
            planCompany.setGmtModified(new Date());
            planCompany.setGmtModifiedUser(user.getLoginId());
            commissionPlanCompanyMapper.insert(planCompany);
        }
    }

    @Override
    public List<ProviderVo> agentProviderList(Long companyId) {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (CollectionUtils.isEmpty(companyIds)) {
            log.error("登录用户无companyIds:{}", JSONObject.toJSONString(SecureUtil.getUser()));
            return Collections.EMPTY_LIST;
        }
        if (!companyIds.contains(companyId)) {
            log.error("公司id:{}不在所属公司列表中:{}", companyId, JSONObject.toJSONString(companyIds));
            return Collections.emptyList();
        }

        //已过期的方案Ids-合同端
        List<Long> expirePlanIds = contractService.getExpirePlanIds(null);
        //当前用户可以看到的方案-合同端
        List<Long> userPermissionPlanIds = commissionPlanService.getUserPermissionPlanIds(null);

        UserPlanIds userPlanIds = agentCommissionPlanService.getUserPlanIds(companyId);
        List<Long> agentPlanIds = userPlanIds.getAgentPlanIds();
        List<Long> contractPlanIds = userPlanIds.getContractPlanIds();
        //查询可以看到的合同端的方案-有明细+已上架+没过期+已通过审核
        List<InstitutionProviderCommissionPlan> providerCommissionPlans = commissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                .exists("select 1 from m_institution_provider_commission where fk_institution_provider_commission_plan_id = m_institution_provider_commission_plan.id ")
                .eq(InstitutionProviderCommissionPlan::getIsActive, 1)
                .eq(InstitutionProviderCommissionPlan::getApprovalStatus, ApprovalStatusEnum.PASS.getCode())
                .in(InstitutionProviderCommissionPlan::getId, userPermissionPlanIds)
                .notIn(CollectionUtils.isNotEmpty(expirePlanIds), InstitutionProviderCommissionPlan::getId, expirePlanIds)
                .in(InstitutionProviderCommissionPlan::getId, contractPlanIds));
        //查询可以看到的代理端的方案
        List<AgentCommissionPlan> agentCommissionPlans = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                .exists(Objects.nonNull(companyId), "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id = m_agent_commission_plan.id and fk_company_id = " + companyId)
                .in(AgentCommissionPlan::getId, agentPlanIds)
        );
        List<Long> allProviderIds = new ArrayList<>();
        List<Long> contractProviderIds = providerCommissionPlans.stream().map(InstitutionProviderCommissionPlan::getFkInstitutionProviderId).distinct().collect(Collectors.toList());
        List<Long> agentProviderIds = agentCommissionPlans.stream().map(AgentCommissionPlan::getFkInstitutionProviderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(contractProviderIds)) {
            allProviderIds.addAll(contractProviderIds);
        }
        if (CollectionUtils.isNotEmpty(agentProviderIds)) {
            allProviderIds.addAll(agentProviderIds);
        }
        if (CollectionUtils.isEmpty(allProviderIds)) {
            allProviderIds.add(0L);
        }
        List<Long> countryIds = SecureUtil.getCountryIds();
        if (CollectionUtils.isEmpty(countryIds)){
            log.error("登录用户无countryIds:{}", JSONObject.toJSONString(SecureUtil.getCountryIds()));
            return Collections.EMPTY_LIST;
        }
        //根据分公司和当前用户的业务国别查询全部的提供商
        List<ProviderVo> userProviders = institutionCenterMapper.selectInstitutionProviderListByIds(Arrays.asList(companyId),
                countryIds,
                allProviderIds);

        //填充展示数据
        List<Long> finalAgentPlanIds = agentPlanIds;
        List<Long> finalContractPlanIds = contractPlanIds;

        List<Long> idList = userProviders.stream().map(ProviderVo::getInstitutionProviderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idList)) {
            long startTime = System.currentTimeMillis();
            Map<Long, Integer> activeCountMap = commissionPlanMapper.selectList(
                            new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                                    .in(InstitutionProviderCommissionPlan::getFkInstitutionProviderId, idList)
                                    .exists("select 1 from m_institution_provider_commission where fk_institution_provider_commission_plan_id = m_institution_provider_commission_plan.id ")
                                    .eq(InstitutionProviderCommissionPlan::getIsActive, 1)
                                    .eq(InstitutionProviderCommissionPlan::getApprovalStatus, ApprovalStatusEnum.PASS.getCode())
                                    .in(InstitutionProviderCommissionPlan::getId, userPermissionPlanIds)
                                    .notIn(CollectionUtils.isNotEmpty(expirePlanIds), InstitutionProviderCommissionPlan::getId, expirePlanIds)
                                    .in(InstitutionProviderCommissionPlan::getId, finalContractPlanIds)
                    ).stream()
                    .collect(Collectors.groupingBy(
                            InstitutionProviderCommissionPlan::getFkInstitutionProviderId,
                            Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
                    ));
            log.info("查询激活Map,花费时间: {}ms", System.currentTimeMillis() - startTime);

            long startTime2 = System.currentTimeMillis();
            Map<Long, Integer> allCountMap = agentCommissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                            .exists(Objects.nonNull(companyId), "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id = m_agent_commission_plan.id and fk_company_id = " + companyId)
                            .eq(AgentCommissionPlan::getIsActive, 1)
                            .in(AgentCommissionPlan::getFkInstitutionProviderId, idList)
                            .in(AgentCommissionPlan::getId, finalAgentPlanIds))
                    .stream()
                    .collect(Collectors.groupingBy(
                            AgentCommissionPlan::getFkInstitutionProviderId,
                            Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
                    ));
            log.info("查询代理MAP,花费时间: {}ms", System.currentTimeMillis() - startTime2);

            userProviders.stream().forEach(providerVo -> {
                providerVo.setAllPlanCount(allCountMap.getOrDefault(providerVo.getInstitutionProviderId(), 0));
                providerVo.setActivePlanCount(activeCountMap.getOrDefault(providerVo.getInstitutionProviderId(), 0));
            });
        }

//        userProviders.stream().forEach(providerVo -> {
////            providerVo.setAllPlanCount(0);
////            providerVo.setActivePlanCount(0);
//            //代理端已保存并且上架的代理方案数量
////            Integer allCount = agentCommissionPlanMapper.selectCount(new LambdaQueryWrapper<AgentCommissionPlan>()
////                    .exists(Objects.nonNull(companyId), "select id from r_agent_commission_plan_company where fk_agent_commission_plan_id = m_agent_commission_plan.id and fk_company_id = " + companyId)
////                    .eq(AgentCommissionPlan::getIsActive, 1)
////                    .eq(AgentCommissionPlan::getFkInstitutionProviderId, providerVo.getInstitutionProviderId())
////                    .in(AgentCommissionPlan::getId, finalAgentPlanIds)
////            );
//            //合同端的数量-审核通过并且是激活并且是在有效期内的
////            Integer activeCount = commissionPlanMapper.selectCount(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
////                    .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderId, providerVo.getInstitutionProviderId())
////                    .exists("select 1 from m_institution_provider_commission where fk_institution_provider_commission_plan_id = m_institution_provider_commission_plan.id ")
////                    .eq(InstitutionProviderCommissionPlan::getIsActive, 1)
////                    .eq(InstitutionProviderCommissionPlan::getApprovalStatus, ApprovalStatusEnum.PASS.getCode())
////                    .in(InstitutionProviderCommissionPlan::getId, userPermissionPlanIds)
////                    .notIn(CollectionUtils.isNotEmpty(expirePlanIds), InstitutionProviderCommissionPlan::getId, expirePlanIds)
////                    .in(InstitutionProviderCommissionPlan::getId, finalContractPlanIds)
////            );
//            providerVo.setAllPlanCount(allCountMap.getOrDefault(providerVo.getInstitutionProviderId(), 0));
//            providerVo.setActivePlanCount(activeCountMap.getOrDefault(providerVo.getInstitutionProviderId(), 0));
//        });
        return userProviders;
    }
}

package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.dto.institution.SaveInstitutionLabelDto;
import com.get.pmpcenter.entity.InstitutionCommissionLabel;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanInstitution;
import com.get.pmpcenter.entity.InstitutionProviderContract;
import com.get.pmpcenter.enums.LogEventEnum;
import com.get.pmpcenter.enums.LogTableEnum;
import com.get.pmpcenter.enums.LogTypeEnum;
import com.get.pmpcenter.mapper.*;
import com.get.pmpcenter.service.AgentCommissionPlanInstitutionService;
import com.get.pmpcenter.service.InstitutionCommissionLabelService;
import com.get.pmpcenter.service.InstitutionProviderCommissionPlanInstitutionService;
import com.get.pmpcenter.service.InstitutionProviderCommissionPlanService;
import com.get.pmpcenter.utils.GeneratorLogsUtil;
import com.get.pmpcenter.vo.common.InstitutionVo;
import com.get.pmpcenter.vo.institution.InstitutionLabelVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionListVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class InstitutionProviderCommissionPlanInstitutionServiceImpl extends ServiceImpl<InstitutionProviderCommissionPlanInstitutionMapper, InstitutionProviderCommissionPlanInstitution> implements InstitutionProviderCommissionPlanInstitutionService {

    @Autowired
    private InstitutionProviderCommissionPlanInstitutionMapper providerCommissionPlanInstitutionMapper;
    @Autowired
    private InstitutionProviderCommissionPlanMapper commissionPlanMapper;
    @Autowired
    private InstitutionProviderContractMapper contractMapper;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private AgentCommissionPlanInstitutionService agentCommissionPlanInstitutionService;
    @Autowired
    private InstitutionCommissionLabelMapper institutionCommissionLabelMapper;
    @Autowired
    private InstitutionCommissionLabelService institutionCommissionLabelService;
    @Autowired
    private InstitutionProviderCommissionPlanService commissionPlanService;

    @Override
    public List<LogDto> saveProviderCommissionPlanInstitution(List<Long> institutionIds, Long providerCommissionPlanId, Long contractId) {
        List<LogDto> logs = new ArrayList<>();
        UserInfo user = SecureUtil.getUser();
        List<InstitutionProviderCommissionPlanInstitution> institutionList = providerCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId));

        List<Long> currentList = institutionList.stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId).collect(Collectors.toList());
        Set<Long> currentSet = new HashSet<>(currentList);
        Set<Long> newSet = new HashSet<>(institutionIds);

        List<Long> toAdd = new ArrayList<>(newSet);
        List<InstitutionProviderCommissionPlanInstitution> toUpdate = new ArrayList<>();
        toAdd.removeAll(currentSet);

        //更新的
        if (CollectionUtils.isNotEmpty(institutionList)) {
            toUpdate = institutionList.stream()
                    .filter(item -> institutionIds.contains(item.getFkInstitutionId()))
                    .map(item -> {
                        item.setGmtModifiedUser(user.getLoginId());
                        item.setGmtModified(new Date());
                        return item;
                    }).collect(Collectors.toList());
        }
        // 找出需要删除的
        List<Long> toRemove = new ArrayList<>(currentSet);
        toRemove.removeAll(newSet);

        if (CollectionUtils.isNotEmpty(toAdd)) {
            List<InstitutionProviderCommissionPlanInstitution> saveList = toAdd.stream().map(item -> {
                InstitutionProviderCommissionPlanInstitution planInstitution = new InstitutionProviderCommissionPlanInstitution();
                planInstitution.setFkInstitutionId(item);
                planInstitution.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
                planInstitution.setGmtCreateUser(user.getLoginId());
                planInstitution.setGmtCreate(new Date());
                planInstitution.setGmtModifiedUser(user.getLoginId());
                planInstitution.setGmtModified(new Date());
                return planInstitution;
            }).collect(Collectors.toList());
            this.saveBatch(saveList);
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.ADD_INSTITUTION, user.getLoginId(),
                    toAdd.size(), contractId));

//            agentCommissionPlanInstitutionService.updateAgentCommissionPlanInstitution(toAdd, providerCommissionPlanId, Boolean.TRUE);
        }

        if (CollectionUtils.isNotEmpty(toUpdate)) {
            this.updateBatchById(toUpdate);
        }

        if (CollectionUtils.isNotEmpty(toRemove)) {
            this.remove(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                    .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                    .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId, toRemove));
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.DEL_INSTITUTION, user.getLoginId(),
                    toRemove.size(), contractId));

            //删除代理佣金方案关联的学校
//            agentCommissionPlanInstitutionService.updateAgentCommissionPlanInstitution(toRemove, providerCommissionPlanId, Boolean.FALSE);
        }

        return logs;
    }


    @Override
    public ProviderCommissionListVo getPlanInstitutionList(Integer type, Long id) {
        ProviderCommissionListVo result = new ProviderCommissionListVo();
        //获取学校列表
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (CollectionUtils.isEmpty(companyIds)) {
            log.error("登录用户无companyIds:{}", JSONObject.toJSONString(SecureUtil.getUser()));
            return result;
        }
        if (type.equals(1)) {
            //合同
            InstitutionProviderContract contract = contractMapper.selectById(id);
            if (Objects.isNull(contract)) {
                log.error("合同信息获取失败,合同不存在：contractId:{}", id);
                return result;
            }
            //合同端不做分公司的过滤
            List<InstitutionVo> currentInstitutionList = institutionCenterMapper.getInstitutionListBycompanyIdsAndCountryIds(contract.getFkInstitutionProviderId(),
                    null,
                    SecureUtil.getCountryIds());
            List<Long> currentInstitutionIds = currentInstitutionList.stream().map(InstitutionVo::getInstitutionId).distinct().collect(Collectors.toList());
            List<Long> planIds = commissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                            .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, id))
                    .stream().map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(planIds)) {
                List<InstitutionProviderCommissionPlanInstitution> planInstitutionList = providerCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                        .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, planIds));
                if (CollectionUtils.isNotEmpty(planInstitutionList)) {
                    List<Long> institutionIds = planInstitutionList.stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId).distinct().collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(currentInstitutionIds)) {
                        institutionIds = institutionIds.stream().filter(item -> currentInstitutionIds.contains(item)).collect(Collectors.toList());
                    } else {
                        institutionIds = currentInstitutionIds;
                    }
                    result.setInstitutionIds(institutionIds);
                    List<Long> finalInstitutionIds = institutionIds;
                    List<InstitutionVo> filterInstitution = currentInstitutionList.stream().filter(item -> finalInstitutionIds.contains(item.getInstitutionId())).collect(Collectors.toList());
                    filterInstitution.sort(Comparator
                            .comparing((InstitutionVo o) -> {
                                // 如果 institutionIds 为空，则全部按 name 排序（返回 false，让所有元素都在一起）
                                return !result.getInstitutionIds().isEmpty() && !result.getInstitutionIds().contains(o.getInstitutionId());
                            })
                            .thenComparing(o -> o.getName().toLowerCase()) // 按 name 首字母升序
                    );
                    result.setInstitutionList(filterInstitution);
                }
            }
        } else {
            //方案
            InstitutionProviderCommissionPlan providerCommissionPlan = commissionPlanMapper.selectById(id);
            if (Objects.isNull(providerCommissionPlan)) {
                log.error("方案信息获取失败,方案不存在：providerCommissionPlanId:{}", id);
                return result;
            }
            //合同端不做分公司的过滤
            List<InstitutionVo> currentInstitutionList = institutionCenterMapper.getInstitutionListBycompanyIdsAndCountryIds(providerCommissionPlan.getFkInstitutionProviderId(),
                    null,
                    SecureUtil.getCountryIds());
            List<Long> currentInstitutionIds = currentInstitutionList.stream().map(InstitutionVo::getInstitutionId).distinct().collect(Collectors.toList());
            List<InstitutionProviderCommissionPlanInstitution> planInstitutionList = providerCommissionPlanInstitutionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                    .eq(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, id));
            if (CollectionUtils.isNotEmpty(planInstitutionList)) {
                List<Long> institutionIds = planInstitutionList.stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId).distinct().collect(Collectors.toList());
                institutionIds = institutionIds.stream().filter(item -> currentInstitutionIds.contains(item)).collect(Collectors.toList());
                result.setInstitutionIds(institutionIds);
            }
            // 排序逻辑
            currentInstitutionList.sort(Comparator
                    .comparing((InstitutionVo o) -> {
                        // 如果 institutionIds 为空，则全部按 name 排序（返回 false，让所有元素都在一起）
                        return !result.getInstitutionIds().isEmpty() && !result.getInstitutionIds().contains(o.getInstitutionId());
                    })
                    .thenComparing(o -> o.getName().toLowerCase()) // 按 name 首字母升序
            );
            result.setInstitutionList(currentInstitutionList);
        }
        //填充学校标签数据和方案数
        List<Long> defInstitutionIds = result.getInstitutionList().stream().map(item -> item.getInstitutionId()).collect(Collectors.toList());
        Map<Long, Integer> planCountMap = commissionPlanService.institutionPlanCount(defInstitutionIds);
        result.getInstitutionList().stream().forEach(item -> {
            InstitutionLabelVo labelVo = new InstitutionLabelVo();
            labelVo.setNameLabels(institutionCommissionLabelMapper.selectBusinessLabel(item.getInstitutionId()));
            item.setInstitutionLabel(labelVo);
            item.setPlanCount(planCountMap.getOrDefault(item.getInstitutionId(), 0));
        });
        return result;
    }

    @Override
    public InstitutionLabelVo getInstitutionLabel(Long institutionId) {
        InstitutionLabelVo labelVo = new InstitutionLabelVo();
        labelVo.setNameLabels(institutionCommissionLabelMapper.selectBusinessLabel(institutionId));
        List<Long> labelIds = institutionCommissionLabelMapper.selectList(new LambdaQueryWrapper<InstitutionCommissionLabel>()
                        .eq(InstitutionCommissionLabel::getFkInstitutionId, institutionId))
                .stream().map(InstitutionCommissionLabel::getFkLabelId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(labelIds)) {
            labelVo.setCustomizeLabels(institutionCommissionLabelMapper.selectCustomizeLabel(labelIds));
        }
        return labelVo;
    }

    @Override
    public InstitutionLabelVo saveInstitutionLabel(SaveInstitutionLabelDto institutionLabelDto) {
        List<Long> currentList = institutionCommissionLabelMapper.selectList(new LambdaQueryWrapper<InstitutionCommissionLabel>()
                        .eq(InstitutionCommissionLabel::getFkInstitutionId, institutionLabelDto.getInstitutionId()))
                .stream().map(InstitutionCommissionLabel::getFkLabelId)
                .collect(Collectors.toList());

        Set<Long> currentSet = new HashSet<>(currentList);
        Set<Long> newSet = new HashSet<>(institutionLabelDto.getLabelIds());

        List<Long> toAdd = new ArrayList<>(newSet);
        toAdd.removeAll(currentSet);

        // 找出需要删除的
        List<Long> toRemove = new ArrayList<>(currentSet);
        toRemove.removeAll(newSet);

        if (CollectionUtils.isNotEmpty(toAdd)) {
            List<InstitutionCommissionLabel> saveList = toAdd.stream().map(item -> {
                InstitutionCommissionLabel planInstitution = new InstitutionCommissionLabel();
                planInstitution.setFkInstitutionId(institutionLabelDto.getInstitutionId());
                planInstitution.setFkLabelId(item);
                planInstitution.setGmtCreateUser(SecureUtil.getLoginId());
                planInstitution.setGmtCreate(new Date());
                planInstitution.setGmtModifiedUser(SecureUtil.getLoginId());
                planInstitution.setGmtModified(new Date());
                planInstitution.setFkCompanyId(institutionLabelDto.getCompanyId());
                return planInstitution;
            }).collect(Collectors.toList());
            institutionCommissionLabelService.saveBatch(saveList);
        }

        if (CollectionUtils.isNotEmpty(toRemove)) {
            institutionCommissionLabelService.remove(new LambdaQueryWrapper<InstitutionCommissionLabel>()
                    .eq(InstitutionCommissionLabel::getFkInstitutionId, institutionLabelDto.getInstitutionId())
                    .in(InstitutionCommissionLabel::getFkLabelId, toRemove));

        }
        return getInstitutionLabel(institutionLabelDto.getInstitutionId());
    }
}

package com.von.gulimall.car.feign;

import com.von.common.utils.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/12/3 14:40
 * @Version 1.0
 */
@FeignClient("gulimall-product")
public interface ProductFeignService {

    @GetMapping("/product/skuinfo/info/{skuId}")
    R info(@PathVariable("skuId") Long skuId);

    @GetMapping("/product/skusaleattrvalue/getSkuSaleAttrValuesBySkuId")
    R getSkuSaleAttrValuesBySkuId(@RequestParam("skuId") Long skuId);

    @GetMapping("/product/skuinfo/price/{skuId}")
    BigDecimal getPrice(@PathVariable("skuId") Long skuId);

}

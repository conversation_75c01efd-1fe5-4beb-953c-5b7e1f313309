package com.von.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @Date 2024/10/30 16:54
 * @Version 1.0
 */
public class WareConstant {

    /**
     * 采购单 状态枚举
     */
    @Getter
    @ToString
    @AllArgsConstructor
    public enum PurchaseStatusEnum {
        CREATE(0, "新建"),
        ASSIGNED(1, "已分配"),
        RECEIVE(2, "已领取"),
        FINISH(3, "已完成"),
        HASERROR(4, "有异常"),
        ;

        private Integer code;

        private String msg;

        private static final Map<Integer, PurchaseStatusEnum> PURCHASE_STATUS_ENUM_MAP = new HashMap<>();

        static {
            for (PurchaseStatusEnum purchaseStatusEnum : PurchaseStatusEnum.values()) {
                PURCHASE_STATUS_ENUM_MAP.put(purchaseStatusEnum.getCode(), purchaseStatusEnum);
            }
        }

        public static PurchaseStatusEnum valueOf(Integer code) {
            return PURCHASE_STATUS_ENUM_MAP.get(code);
        }

    }

    /**
     * 采购需求 状态枚举
     */
    @Getter
    @ToString
    @AllArgsConstructor
    public enum PurchaseDetailStatusEnum {
        CREATE(0, "新建"),
        ASSIGNED(1, "已分配"),
        BUYING(2, "长在采购"),
        FINISH(3, "已完成"),
        HASERROR(4, "采购失败"),
        ;

        private Integer code;

        private String msg;

        private static final Map<Integer, PurchaseStatusEnum> PURCHASE_STATUS_ENUM_MAP = new HashMap<>();

        static {
            for (PurchaseStatusEnum purchaseStatusEnum : PurchaseStatusEnum.values()) {
                PURCHASE_STATUS_ENUM_MAP.put(purchaseStatusEnum.getCode(), purchaseStatusEnum);
            }
        }

        public static PurchaseStatusEnum valueOf(Integer code) {
            return PURCHASE_STATUS_ENUM_MAP.get(code);
        }

    }

}

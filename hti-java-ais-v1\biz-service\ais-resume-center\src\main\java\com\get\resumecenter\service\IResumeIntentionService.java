package com.get.resumecenter.service;


import com.get.resumecenter.vo.ResumeIntentionVo;
import com.get.resumecenter.dto.ResumeIntentionDto;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 18:13
 * @Description: 职业意向
 **/
public interface IResumeIntentionService {

    /**
     * 根据简历ID获取职位意向
     *
     * @param resumeId
     * @return
     * @
     */
    ResumeIntentionVo getResumeIntentionDto(Long resumeId);


    /**
     * 职位意向编辑/新增与修改通用接口
     *
     * @param intentionVo
     * @return
     * @
     */
    Long updateResumeIntention(ResumeIntentionDto intentionVo);


    /**
     * 根据id
     *
     * @param id
     * @return
     * @
     */
    ResumeIntentionVo getResumeIntentionById(Long id);


}

package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dao.RemindTemplateMapper;
import com.get.remindercenter.dto.MultiUserTaskReminderEmailDto;
import com.get.remindercenter.dto.ProviderContractExpiryDto;
import com.get.remindercenter.dto.StudentOfferCreateEmailDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import com.get.salecenter.dto.ProjectRoleStaffDto;
import com.get.salecenter.entity.*;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.StudentOfferVo;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.vo.StudentRoleAndStaffVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Component("studentOfferCreateEmailHelper")
public class StudentOfferCreateEmailHelper extends EmailAbstractHelper{

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private ISaleCenterClient saleCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;


    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            StudentOfferCreateEmailDto studentOfferCreateEmailDto = assembleEmailData(emailSenderQueue);
            StringJoiner emailsCombined = new StringJoiner(", ");
            //根据发送人id获取发送人邮箱
            List<StaffVo> staff = permissionCenterClient.getStaffByIds(studentOfferCreateEmailDto.getStaffEmailSet());
            Map<Long, StaffVo> data = staff.stream()
                    .collect(Collectors.toMap(
                            StaffVo::getId,  // Key: StaffVo 的 ID
                            staffVo -> staffVo  // Value: StaffVo 本身
                    ));
            for (Long id : studentOfferCreateEmailDto.getStaffEmailSet()) {
                try {
                    String roleName = data.get(id).getFullName();
                    studentOfferCreateEmailDto.getMap().put("roleName", roleName);
                    String template = setEmailTemplate(studentOfferCreateEmailDto);
                    EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                    emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
                    emailSystemMQMessageDto.setTitle(studentOfferCreateEmailDto.getEmailTitle());
                    emailSystemMQMessageDto.setContent(template);
                    emailSystemMQMessageDto.setToEmail(data.get(id).getEmail());
                    //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    if (GeneralTool.isNotEmpty(data.get(id).getEmail())) {
                        emailsCombined.add(data.get(id).getEmail());
                    }
                }catch (Exception e){
                    // 记录发送失败的邮箱
                    String failedEmail = data.get(id) != null &&data.get(id).getEmail() != null ? data.get(id).getEmail() : "staffId:" + id;
                    failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}",  id, failedEmail, e.getMessage());
                }
            }
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        } catch (Exception e) {
            log.error("StudentOfferCreateEmailHelper error:{}", e);
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public StudentOfferCreateEmailDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        StudentOfferCreateEmailDto reminderDto = new StudentOfferCreateEmailDto();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);

        StudentOfferVo studentOffer = saleCenterClient.getStudentOfferDetail(emailSenderQueue.getFkTableId()).getData();
        //获取学生信息
        Student student = saleCenterClient.getStudentById(studentOffer.getFkStudentId()).getData();
        //获取代理信息
        Agent agent = saleCenterClient.getAgentById(studentOffer.getFkAgentId()).getData();

        Long bdId = studentOffer.getFkStaffId();
        String agentName = agent.getName();
        //项目成员
        List<StudentProjectRoleStaffVo> roleStaffList = studentOffer.getStudentProjectRoleStaffDtos();
        Set<Long> staffIds = roleStaffList.stream().map(StudentProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());;


        String countryName = institutionCenterClient.getCountryNameById(studentOffer.getFkAreaCountryId()).getData();
        String projectRole = getProjectRole(roleStaffList);

        StaffVo bd = permissionCenterClient.getStaffById(bdId).getData();

        Long fkCompanyId = null;
        if (GeneralTool.isNotEmpty(student.getFkCompanyId())) {
            fkCompanyId = student.getFkCompanyId();
        }else {
            fkCompanyId = 3L;
        }
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(fkCompanyId);
        String title = null;
        String bdName = null;
        String studentName = null;
        if(!versionValue2.equals("en")){
            studentName = student.getName() + "（" +student.getLastName() + " "+ student.getFirstName() + "）";
             title = "【系统提醒】学生："+ studentName +"，"+countryName+"申请方案项目成员指派";
             bdName = bd.getName() + "（" + bd.getNameEn() + "）";
        }else {
            studentName = student.getName();
            title = "【System Reminder】Student："+ studentName +"，"+countryName+"Application Plan Project Member Assignment";
            bdName =  bd.getNameEn();
        }

        Map<String,String> map = new HashMap<>();

        map.put("studentName",studentName);
        map.put("agentName",agentName);
        map.put("projectRole",projectRole);
        map.put("bdName",bdName);
        map.put("date", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        map.put("countryName",countryName);
        map.put("title",title);
        reminderDto.setMap(map);
        reminderDto.setLanguageCode(versionValue2);
        reminderDto.setStaffEmailSet(staffIds);
        reminderDto.setEmailTitle(title);
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
        return reminderDto;
    }

    /**
     * 设置模板
     * @param reminderDto
     * @return
     */
    private String setEmailTemplate(StudentOfferCreateEmailDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.REMINDER_EMAIL_CREATE_STUDENT_OFFER.getEmailTemplateKey()));
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }


    private String getProjectRole(List<StudentProjectRoleStaffVo> projectRoleStaffVos){
        Set<Long> roleIds = projectRoleStaffVos.stream().map(StudentProjectRoleStaffVo::getFkStudentProjectRoleId).collect(Collectors.toSet());
        Set<Long> staffIds = projectRoleStaffVos.stream().map(StudentProjectRoleStaffVo::getFkStaffId).collect(Collectors.toSet());
        //List<StudentProjectRole> projectRoles = studentProjectRoleMapper.selectBatchIds(roleIds);
        List<StudentProjectRole> projectRoles = saleCenterClient.getStudentProjectRoleListByRoleIds(roleIds);
        List<StaffVo> staffVos = permissionCenterClient.getStaffByIds(staffIds);
        Map<Long, StudentProjectRole> roleMap = projectRoles.stream().collect(Collectors.toMap(StudentProjectRole::getId, Function.identity()));
        Map<Long, StaffVo> staffDtoMap = staffVos.stream().collect(Collectors.toMap(StaffVo::getId, Function.identity()));
        StringBuilder stringBuilder = new StringBuilder();
        for (StudentProjectRoleStaffVo roleStaffVo : projectRoleStaffVos) {
            Long fkRoleId = roleStaffVo.getFkStudentProjectRoleId();
            Long fkStaffId = roleStaffVo.getFkStaffId();
            stringBuilder.append("【").append(roleMap.get(fkRoleId).getRoleName()).append("】 ")
                    .append(staffDtoMap.get(fkStaffId).getFullName()).append("，");
        }
        String str = stringBuilder.toString();
        if (str.endsWith("，")) {
            str = str.substring(0, str.lastIndexOf("，"));
        }
        return str;
    }
}

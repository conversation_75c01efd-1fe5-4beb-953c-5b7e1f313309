package com.get.remindercenter.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.vo.RemindTaskVo;
import com.get.remindercenter.vo.RemindTaskListVo;
import com.get.remindercenter.vo.ReminderTaskCountVo;
import com.get.remindercenter.service.RemindTaskService;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.dto.RemindTaskListDto;
import com.get.remindercenter.dto.RemindTaskToAppDto;
import com.get.remindercenter.dto.RemindTaskUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:10
 * Date: 2021/11/12
 * Description:提醒任务管理控制器
 */
@Api(tags = "提醒任务管理控制器")
@RestController
@RequestMapping("reminder/remindTask")
public class RemindTaskController {

    @Resource
    private RemindTaskService remindTaskService;

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:11:35 2021/11/12
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.LIST, description = "提醒中心/提醒任务管理/列表数据")
    @PostMapping("datas")
    public ResponseBo<RemindTaskVo> datas(@RequestBody RemindTaskListDto remindTaskListDto) {
        List<RemindTaskVo> datas = remindTaskService.datas(remindTaskListDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:35 2021/11/12
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.ADD, description = "提醒中心/提醒任务管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(RemindTaskUpdateDto.Add.class) RemindTaskUpdateDto remindTaskUpdateDto) {
        remindTaskService.add(remindTaskUpdateDto);
        return SaveResponseBo.ok();
    }


    /**
     * @Description: 批量新增任务
     * @Author: Jerry
     * @Date:12:57 2021/11/18
     */
    @VerifyLogin(IsVerify = false)
    @ApiIgnore
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody List<RemindTaskDto> remindTaskDtos) {
        remindTaskService.batchAdd(remindTaskDtos);
        return SaveResponseBo.ok();
    }

    @VerifyLogin(IsVerify = false)
    @ApiIgnore
    @PostMapping("batchAddToApp")
    public ResponseBo batchAddToApp(HttpServletRequest request) throws IOException {
        BufferedReader reader = request.getReader();
        String str, wholeStr = "";
        while ((str = reader.readLine()) != null) {
            wholeStr += str;
        }
        List<RemindTaskDto> remindTaskDtos =new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(wholeStr);
        JSONArray courseList = jsonObject.getJSONArray("datas");
        List<RemindTaskToAppDto> remindTaskToAppDtos = GeneralTool.copy(courseList, RemindTaskToAppDto.class);
        remindTaskToAppDtos.stream().forEach(d->{
            RemindTaskDto remindTaskDto = new RemindTaskDto();
            remindTaskDto.setFkStaffId(Long.valueOf(d.getFkStaffId()));
            remindTaskDto.setStatus(1);
            remindTaskDto.setFkTableName(d.getFkTableName());
            remindTaskDto.setFkTableId(Long.valueOf(d.getFkTableId()));
            remindTaskDto.setStartTime(new Date());
            remindTaskDto.setRemindMethod("1");
            remindTaskDto.setTaskBgColor(d.getTaskBgColor());
            remindTaskDto.setTaskRemark(d.getTaskRemark());
            remindTaskDto.setTaskTitle(d.getTaskTitle());
            remindTaskDto.setFkRemindEventTypeKey(d.getFkRemindEventTypeKey());
            remindTaskDto.setGmtCreateUser("issue");
            remindTaskDtos.add(remindTaskDto);
        });
        remindTaskService.batchAdd(remindTaskDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @Description: 批量修改任务
     * @Author: Jerry
     * @Date:15:29 2021/11/25
     */
    @ApiIgnore
    @PostMapping("batchUpdate")
    public ResponseBo batchUpdate(@RequestBody List<RemindTaskDto> remindTaskDtos, @RequestParam("fkTableName") String fkTableName,
                                  @RequestParam("fkTableId") Long fkTableId) {
        remindTaskService.batchUpdate(remindTaskDtos, fkTableName, fkTableId);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description: 批量修改任务(支持表的多个Id)
     * @Author: Jerry
     * @Date:17:38 2021/12/1
     */
    @ApiIgnore
    @PostMapping("batchUpdateByTableIds")
    public ResponseBo batchUpdateByTableIds(@RequestBody List<RemindTaskDto> remindTaskDtos, @RequestParam("fkTableName") String fkTableName,
                                            @RequestParam("fkTableIds") Set<Long> fkTableIds) {
        remindTaskService.batchUpdateByTableIds(remindTaskDtos, fkTableName, fkTableIds);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:36 2021/11/12
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "更新", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.EDIT, description = "提醒中心/提醒任务管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(RemindTaskUpdateDto.Update.class) RemindTaskUpdateDto remindTaskUpdateDto) {

        // 或者打印格式化的JSON字符串
        System.out.println(JSONObject.toJSONString(remindTaskUpdateDto, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteDateUseDateFormat));

        remindTaskService.update(remindTaskUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:36 2021/11/12
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.DETAIL, description = "提醒中心/提醒任务管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<RemindTaskVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(remindTaskService.detail(id));
    }

    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:36 2021/11/12
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.DELETE, description = "提醒中心/提醒任务管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        remindTaskService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @Description: 获取任务总数
     * @Author: Jerry
     * @Date:16:47 2021/11/16
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取任务总数", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.LIST, description = "提醒中心/提醒任务管理/获取任务总数")
    @PostMapping("getTaskCount")
    public ResponseBo<ReminderTaskCountVo> getTaskCount() {
        return new ListResponseBo<>(remindTaskService.getTaskCount());
    }

    /**
     * 获取提醒列表
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "根据key获取提醒列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.LIST, description = "提醒中心/提醒任务管理/根据key获取提醒列表")
    @PostMapping("getRemindTaskDatas")
    public ResponseBo<RemindTaskListVo> getRemindTaskDatas(@RequestBody SearchBean<RemindTaskListDto> page) {
        List<RemindTaskListVo> datas = remindTaskService.getRemindTaskDatas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }
}

package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.OtherTypeVo;
import com.get.resumecenter.entity.OtherType;
import com.get.resumecenter.service.IOtherTypeService;
import com.get.resumecenter.dto.OtherTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/31
 * @TIME: 10:32
 * @Description:
 **/
@Api(tags = "简历其他类型管理")
@RestController
@RequestMapping("resume/othertype")
public class OtherTypeController {
    @Resource
    IOtherTypeService otherTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.resumecenter.vo.OtherTypeVo>
     * @Description: 列表数据
     * @Param [otherTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.LIST, description = "人才中心/简历其他类型管理/查询简历其他类型")
    @PostMapping("datas")
    public ResponseBo<OtherTypeVo> datas(@RequestBody OtherTypeDto otherTypeDto) {
        List<OtherTypeVo> datas = otherTypeService.datas(otherTypeDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/简历其他类型管理/简历其他类型详情")
    @GetMapping("/{id}")
    public ResponseBo<OtherTypeVo> detail(@PathVariable("id") Long id) {
        OtherTypeVo data = otherTypeService.findOtherTypeById(id);
        OtherTypeVo otherTypeVo = BeanCopyUtils.objClone(data, OtherTypeVo::new);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", otherTypeVo);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param otherTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历其他类型管理/新增简历其他类型")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(OtherTypeDto.Add.class) OtherTypeDto otherTypeDto) {
        return SaveResponseBo.ok(this.otherTypeService.addOtherType(otherTypeDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "人才中心/简历其他类型管理/删除简历其他类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.otherTypeService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param otherTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/简历其他类型管理/更新简历其他类型")
    @PostMapping("update")
    public ResponseBo<OtherTypeVo> update(@RequestBody @Validated(OtherTypeDto.Update.class) OtherTypeDto otherTypeDto) {
        return UpdateResponseBo.ok(otherTypeService.updateOtherType(otherTypeDto));
    }


    /**
     * 批量新增信息
     *
     * @param otherTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历其他类型管理/批量保存简历其他类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(OtherTypeDto.Add.class) ValidList<OtherTypeDto> otherTypeDtos) {
        otherTypeService.batchAdd(otherTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "其他主题类型下拉", notes = "")
    @GetMapping("getOtherTypeSelect")
    public ResponseBo<BaseSelectEntity> getOtherTypeSelect() {
        return new ListResponseBo<>(otherTypeService.getOtherTypeSelect());
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [studentOfferItemStepVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/简历其他类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<OtherTypeDto> otherTypeDtos) {
        otherTypeService.movingOrder(otherTypeDtos);
        return ResponseBo.ok();
    }

}

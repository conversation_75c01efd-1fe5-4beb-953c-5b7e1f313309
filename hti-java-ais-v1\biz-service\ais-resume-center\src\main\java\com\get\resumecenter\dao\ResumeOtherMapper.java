package com.get.resumecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.resumecenter.vo.ResumeOtherVo;
import com.get.resumecenter.entity.ResumeOther;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

;

/**
 * <AUTHOR>
 */
@Mapper
public interface ResumeOtherMapper extends BaseMapper<ResumeOther> {

    int insertSelective(ResumeOther record);

    ResumeOtherVo selectByOtherId(Long id);

    List<ResumeOtherVo> selectByResumeId(Long resumeId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除校验
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    Bo<PERSON>an isExistByOtherTypeId(Long otherTypeId);

}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.von.ware.dao.PurchaseDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.von.ware.entity.PurchaseEntity" id="purchaseMap">
        <result property="id" column="id"/>
        <result property="assigneeId" column="assignee_id"/>
        <result property="assigneeName" column="assignee_name"/>
        <result property="phone" column="phone"/>
        <result property="priority" column="priority"/>
        <result property="status" column="status"/>
        <result property="wareId" column="ware_id"/>
        <result property="amount" column="amount"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>
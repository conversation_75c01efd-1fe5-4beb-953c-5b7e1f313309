package com.get.pmpcenter.event.listener;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.event.ExpireAgentCommissionPlanEvent;
import com.get.pmpcenter.service.AgentCommissionPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:19
 * @Version 1.0
 */
@Service
@Slf4j
public class ExpireAgentCommissionPlanListener {

    @Autowired
    private AgentCommissionPlanService commissionPlanService;

    @Async("commissionLogTaskExecutor")
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void onExpireAgentCommissionPlan(ExpireAgentCommissionPlanEvent event) {
        if (CollectionUtils.isEmpty(event.getExpirePlanIds())) {
            return;
        }
        List<AgentCommissionPlan> unActiveList = commissionPlanService.list(new LambdaQueryWrapper<AgentCommissionPlan>()
                .eq(AgentCommissionPlan::getIsActive, 1)
                .in(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, event.getExpirePlanIds()));
        if (CollectionUtils.isNotEmpty(unActiveList)) {
            log.info("批量更新过期方案：{}", unActiveList.stream().map(AgentCommissionPlan::getId).toArray());
            unActiveList.stream().forEach(plan -> {
                plan.setIsActive(0);
                plan.setGmtModified(new Date());
            });
            commissionPlanService.updateBatchById(unActiveList);
        }
    }
}

# 基础镜像
FROM 192.168.2.32/hti-java-ais/arthas-cn:latest

RUN apk --update --no-cache add tini

# 作者
MAINTAINER David.xie

#声明一个挂载点，容器内此路径会对应宿主机的某个文件夹111
VOLUME /tmp

#COPY
ADD target/ais-pmp-center.jar  ./app.jar

ENV SPRING_PROFILES_ACTIVE="dev"

ENV JAVA_OPTS="-Djava.security.egd=file:/dev/./urandom","-Duser.timezone=Asia/Shanghai"

ENV SPRING_OPTS="--spring.profiles.active=${SPRING_PROFILES_ACTIVE}"

ENTRYPOINT /sbin/tini -- java -jar app.jar $JAVA_OPTS $SPRING_OPTS
package com.von.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/11/7 14:00
 * @Version 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuHasStockVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long skuId;

    private Boolean hasStock;

}

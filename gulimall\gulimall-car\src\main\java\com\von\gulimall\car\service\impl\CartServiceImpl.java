package com.von.gulimall.car.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.von.common.constant.CartConstant;
import com.von.common.utils.R;
import com.von.gulimall.car.feign.ProductFeignService;
import com.von.gulimall.car.interceptor.CartInterceptor;
import com.von.gulimall.car.service.CartService;
import com.von.gulimall.car.vo.Cart;
import com.von.gulimall.car.vo.CartItem;
import com.von.gulimall.car.vo.SkuInfoVo;
import com.von.gulimall.car.vo.UserInfoTo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/12/3 14:03
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CartServiceImpl implements CartService {


    private final StringRedisTemplate redisTemplate;


    private final ProductFeignService productFeignService;


    private final ThreadPoolExecutor executor;

    @Override
    public CartItem addToCart(Long skuId, Integer num) throws ExecutionException, InterruptedException {
        BoundHashOperations<String, Object, Object> cartOps = this.getCartOps();
        if (ObjectUtil.isNull(cartOps)) {
            return null;
        }
        String cartRedis = (String) cartOps.get(skuId.toString());
        if (StrUtil.isBlank(cartRedis)) {
            CartItem cartItem = new CartItem();
            // 异步查询商品的详细信息并构造数据
            CompletableFuture<Void> getSkuInfoTask = CompletableFuture.runAsync(() -> {
                R skuInfoR = this.productFeignService.info(skuId);
                if (!skuInfoR.isOk()) {
                    return;
                }
                SkuInfoVo skuInfoVo = skuInfoR.getData("skuInfo", new TypeReference<SkuInfoVo>() {});
                cartItem.setCheck(true);
                cartItem.setCount(num);
                cartItem.setImage(skuInfoVo.getSkuDefaultImg());
                cartItem.setTitle(skuInfoVo.getSkuTitle());
                cartItem.setSkuId(skuId);
                cartItem.setPrice(skuInfoVo.getPrice());
            }, executor);

            // 异步查询sku属性
            CompletableFuture<Void> skuAttrTask = CompletableFuture.runAsync(() -> {
                R skuSaleAttrR = this.productFeignService.getSkuSaleAttrValuesBySkuId(skuId);
                if (!skuSaleAttrR.isOk()) {
                    return;
                }
                List<String> skuAttrs = skuSaleAttrR.getData(new TypeReference<List<String>>() {
                });
                cartItem.setSkuAttr(skuAttrs);
            }, executor);
            CompletableFuture.allOf(skuAttrTask, getSkuInfoTask).get();
            cartOps.put(skuId.toString(), JSON.toJSONString(cartItem));

            return cartItem;
        } else {
            CartItem cartItem = JSON.parseObject(cartRedis, CartItem.class);
            cartItem.setCount(cartItem.getCount() + num);
            cartOps.put(skuId.toString(), JSON.toJSONString(cartItem));
            return cartItem;
        }
    }

    @Override
    public CartItem getCartItem(Long skuId) {
        BoundHashOperations<String, Object, Object> cartOps = this.getCartOps();
        if (ObjectUtil.isNull(cartOps)) {
            return null;
        }
        String cartItemStr = (String) cartOps.get(skuId.toString());
        CartItem cartItem = JSON.parseObject(cartItemStr, CartItem.class);
        return cartItem;
    }

    /**
     * 获取全部的购物车对象
     *
     * @return
     */
    @Override
    public Cart cartList() throws ExecutionException, InterruptedException {
        Cart cart = new Cart();
        UserInfoTo userInfoTo = CartInterceptor.threadLocal.get();
        if (ObjectUtil.isNull(userInfoTo)) {
            return null;
        }
        String cartKey = CartConstant.CART_REDIS_PREFIX + userInfoTo.getUserKey();

        if (ObjectUtil.isNotNull(userInfoTo.getUserId())) {
            // 登录
            String cartId = CartConstant.CART_REDIS_PREFIX + userInfoTo.getUserId();
            // 临时登录的全部购物车数据
            List<CartItem> temporaryCartItems = this.getCartItems(cartKey);
            if (CollectionUtil.isNotEmpty(temporaryCartItems)) {
                // 临时购物车有数据, 合并到登录后的购物车
                for (CartItem temporaryCartItem : temporaryCartItems) {
                    this.addToCart(temporaryCartItem.getSkuId(), temporaryCartItem.getCount());
                }
            }
            // 获取登录用户的购物车数据
            List<CartItem> cartItems = this.getCartItems(cartId);
            cart.setItems(cartItems);

            // 删除临时购物车数据
            this.redisTemplate.delete(cartKey);
        } else {
            // 临时登录
            List<CartItem> temporaryCartItems = this.getCartItems(cartKey);
            if (CollectionUtil.isNotEmpty(temporaryCartItems)) {
                cart.setItems(temporaryCartItems);
            }
        }

        return cart;
    }

    /**
     * 修改选中状态
     *
     * @param skuId
     * @param check
     */
    @Override
    public void checkItem(Long skuId, Integer check) {
        BoundHashOperations<String, Object, Object> cartOps = this.getCartOps();
        CartItem cartItem = this.getCartItem(skuId);
        if (ObjectUtil.isNull(cartItem)) {
            log.info("修改失败");
            return;
        }
        cartItem.setCheck(check != 0);
        cartOps.put(skuId.toString(), JSON.toJSONString(cartItem));
    }

    @Override
    public void changeItemCount(Long skuId, Integer num) {
        BoundHashOperations<String, Object, Object> cartOps = this.getCartOps();
        CartItem cartItem = this.getCartItem(skuId);
        if (ObjectUtil.isNull(cartItem)) {
            log.info("修改失败");
            return;
        }
        cartItem.setCount(num);
        cartOps.put(skuId.toString(), JSON.toJSONString(cartItem));
    }

    @Override
    public void deleteItem(Long skuId) {
        BoundHashOperations<String, Object, Object> cartOps = this.getCartOps();
        cartOps.delete(skuId.toString());
    }

    @Override
    public List<CartItem> getCurrentUserCheckCartItems() {
        log.info("查询购物车数据");
        UserInfoTo userInfoTo = CartInterceptor.threadLocal.get();
        if (ObjectUtil.isNull(userInfoTo)) {
            return null;
        }
        String cartKey = CartConstant.CART_REDIS_PREFIX + userInfoTo.getUserId();
        List<CartItem> cartItems = this.getCartItems(cartKey);
        List<CartItem> checkCartItems = cartItems.stream()
                .filter(CartItem::getCheck)
                .map(cartItem -> {
                    // TODO 此处优化 : 可以批量查询需要的价格并构造数据
                    // 远程查询最新的价格并构造数据
                    BigDecimal price = this.productFeignService.getPrice(cartItem.getSkuId());
                    cartItem.setPrice(price);
                    return cartItem;
                })
                .collect(Collectors.toList());

        return checkCartItems;
    }

    /**
     * 获取临时购物车全部数据
     *
     * @param cartKey
     * @return
     */
    private List<CartItem> getCartItems(String cartKey) {
        BoundHashOperations<String, Object, Object> cartOps = this.redisTemplate.boundHashOps(cartKey);
        List<Object> cartObjList = cartOps.values();
        if (CollectionUtil.isEmpty(cartObjList)) {
            return ListUtil.empty();
        }
        List<CartItem> cartItemList = cartObjList.stream().map(cartObj -> {
            String cartStr = cartObj.toString();
            CartItem cartItem = JSON.parseObject(cartStr, CartItem.class);
            return cartItem;
        }).collect(Collectors.toList());

        return cartItemList;
    }

    /**
     * 获取redis操作对象
     *
     * @return
     */
    private BoundHashOperations<String, Object, Object> getCartOps() {
        UserInfoTo userInfoTo = CartInterceptor.threadLocal.get();
        if (ObjectUtil.isNull(userInfoTo)) {
            return null;
        }
        String cartKey = CartConstant.CART_REDIS_PREFIX;
        if (ObjectUtil.isNotNull(userInfoTo.getUserId())) {
            // 登录用户
            cartKey += userInfoTo.getUserId();
        } else {
            // 临时用户
            cartKey += userInfoTo.getUserKey();
        }
        BoundHashOperations<String, Object, Object> cartOpt = this.redisTemplate.boundHashOps(cartKey);

        return cartOpt;
    }



}

package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.AgentCommissionPlanApproval;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentCommissionPlanApprovalMapper extends BaseMapper<AgentCommissionPlanApproval> {


    /**
     * 获取审批列表
     * @param plaIds
     * @return
     */
    List<AgentCommissionPlanApproval> getAgentApprovalList(List<Long> plaIds);
}

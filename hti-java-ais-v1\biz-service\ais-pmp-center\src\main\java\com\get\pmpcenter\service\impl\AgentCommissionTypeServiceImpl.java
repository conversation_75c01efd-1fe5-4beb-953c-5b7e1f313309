package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeDto;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.entity.AgentCommissionType;
import com.get.pmpcenter.entity.AgentCommissionTypeAgent;
import com.get.pmpcenter.mapper.AgentCommissionPlanMapper;
import com.get.pmpcenter.mapper.AgentCommissionTypeAgentMapper;
import com.get.pmpcenter.mapper.AgentCommissionTypeMapper;
import com.get.pmpcenter.service.AgentCommissionTypeService;
import com.get.pmpcenter.vo.agent.AgentCommissionTypeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionTypeServiceImpl extends ServiceImpl<AgentCommissionTypeMapper, AgentCommissionType> implements AgentCommissionTypeService {

    @Resource
    private AgentCommissionTypeMapper agentCommissionTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private AgentCommissionTypeAgentMapper agentCommissionTypeAgentMapper;
    @Resource
    private AgentCommissionPlanMapper agentCommissionPlanMapper;

    @Override
    public List<AgentCommissionType> getAgentCommissionTypeList(Long companyId) {
        return agentCommissionTypeMapper.selectList(new LambdaQueryWrapper<AgentCommissionType>()
                .eq(AgentCommissionType::getFkCompanyId, companyId)
//                .eq(AgentCommissionType::getIsActive, 1)
                .orderByDesc(AgentCommissionType::getViewOrder));
    }

    private final int is_Active = 1;
//    private final int is_NotActive = 0;

    /**
     * 新增代理佣金类型
     *
     * @param agentCommissionTypeDto
     * @return
     */
    @Override
    public int add(AgentCommissionTypeDto agentCommissionTypeDto) {
        if (GeneralTool.isEmpty(agentCommissionTypeDto)) {
            if (GeneralTool.isEmpty(agentCommissionTypeDto.getTypeName())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_null"));
            }

        }
        if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getFkCompanyId())) {
            if (!SecureUtil.validateCompany(agentCommissionTypeDto.getFkCompanyId())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }

        } else if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getCompanyIds())) {
            if (!SecureUtil.validateCompanys(agentCommissionTypeDto.getCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        if (GeneralTool.isEmpty(agentCommissionTypeDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        Integer maxViewOrder = agentCommissionTypeMapper.selectMaxViewOrderNumByAll();
        AgentCommissionType agentCommissionType = BeanCopyUtils.objClone(agentCommissionTypeDto, AgentCommissionType::new);
        utilService.setCreateInfo(agentCommissionType);
        agentCommissionType.setIsActive(is_Active);
        agentCommissionType.setFkCompanyId(agentCommissionTypeDto.getFkCompanyId());
        agentCommissionType.setViewOrder(maxViewOrder + 1);
        agentCommissionType.setIsShowOnly(agentCommissionTypeDto.getIsShowOnly());
        if (StringUtils.isNotBlank(agentCommissionTypeDto.getCommissionRateDefault())) {
            boolean valid = isValidJsonWithDefault(agentCommissionTypeDto.getCommissionRateDefault());
            if (!valid) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "parameter_missing", "参数缺失"));
            }
            agentCommissionType.setCommissionRateDefault(agentCommissionTypeDto.getCommissionRateDefault());
        }
        agentCommissionType.setCommissionRateDefault(agentCommissionTypeDto.getCommissionRateDefault());
        return agentCommissionTypeMapper.insert(agentCommissionType);
    }

    /**
     * 查询代理佣金类型列表
     *
     * @param agentCommissionTypeDto
     * @param page
     * @return
     */
    @Override
    public List<AgentCommissionTypeVo> dataList(AgentCommissionTypeDto agentCommissionTypeDto, Page page) {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        List<Long> companyIds = SecureUtil.getCompanyIds();
        //公司权限限制，只可查询自己的公司数据
        if (GeneralTool.isNotEmpty(agentCommissionTypeDto)) {
            if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(agentCommissionTypeDto.getFkCompanyId())) {
                    return Collections.emptyList();
                }

            } else if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getCompanyIds())) {
                if (!SecureUtil.validateCompanys(agentCommissionTypeDto.getCompanyIds())) {
                    return Collections.emptyList();
                }
            } else {
                if (companyIds.size() > 1) {
                    agentCommissionTypeDto.setCompanyIds(companyIds);
                } else {
                    agentCommissionTypeDto.setFkCompanyId(fkCompanyId);
                }
            }
        }

        IPage<AgentCommissionTypeVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<AgentCommissionTypeVo> agentCommissionTypeList = agentCommissionTypeMapper.getAgentCommissionTypeList(agentCommissionTypeDto, iPage);
        if (GeneralTool.isNotEmpty(agentCommissionTypeList)) {
            Set<Long> companyIdList = agentCommissionTypeList.stream().map(AgentCommissionTypeVo::getFkCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());
            //根据公司ids获取公司名称
            Map<Long, String> companyNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(companyIds)) {
                Result<Map<Long, String>> result1 = permissionCenterClient.getCompanyNamesByIds(companyIdList);
                if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                    companyNamesByIds = result1.getData();
                }
            }
            for (AgentCommissionTypeVo agentCommissionTypeVo : agentCommissionTypeList) {
                agentCommissionTypeVo.setCompanyName(companyNamesByIds.get(agentCommissionTypeVo.getFkCompanyId()));
//                CommissionRate保留一位小数
                if (GeneralTool.isNotEmpty(agentCommissionTypeVo.getCommissionRate())) {
                    agentCommissionTypeVo.setCommissionRate(agentCommissionTypeVo.getCommissionRate().setScale(1, BigDecimal.ROUND_HALF_UP));
                }
            }
            page.setAll((int) iPage.getTotal());
            return agentCommissionTypeList;
        } else {
            return Collections.emptyList();
        }

    }


    @Override
    public Long update(AgentCommissionTypeDto agentCommissionTypeDto) {
        if (GeneralTool.isNotEmpty(agentCommissionTypeDto)) {
            if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getId())) {
                AgentCommissionType result = agentCommissionTypeMapper.selectById(agentCommissionTypeDto.getId());
                if (GeneralTool.isNotEmpty(result)) {
//                    LambdaUpdateWrapper<AgentCommissionType> wrapper = new LambdaUpdateWrapper<>(agentCommissionType);
//                    wrapper.eq(AgentCommissionType::getId, agentCommissionTypeDto.getId());
//                    utilService.setUpdateInfo(agentCommissionType);
//                    if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getIsActive())) {
//                        wrapper.set(AgentCommissionType::getIsActive, agentCommissionTypeDto.getIsActive());
//                    }
//                    if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getTypeName())) {
//                        wrapper.set(AgentCommissionType::getTypeName, agentCommissionTypeDto.getTypeName());
//                    }
//                    if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getCommissionRate())) {
//                        wrapper.set(AgentCommissionType::getCommissionRate, agentCommissionTypeDto.getCommissionRate());
//                    }
//                    if (GeneralTool.isNotEmpty(agentCommissionTypeDto.getRemark())) {
//                        wrapper.set(AgentCommissionType::getRemark, agentCommissionTypeDto.getRemark());
//                    }
//
//                    int update = agentCommissionTypeMapper.update(null,wrapper);
//                    if (update < 0) {
//                        throw new GetServiceException(LocaleMessageUtils.getMessage("update_error"));
//                    }

                    AgentCommissionType agentCommissionType = BeanCopyUtils.objClone(agentCommissionTypeDto, AgentCommissionType::new);
                    if (StringUtils.isNotBlank(agentCommissionTypeDto.getCommissionRateDefault())) {
                        boolean valid = isValidJsonWithDefault(agentCommissionTypeDto.getCommissionRateDefault());
                        if (!valid) {
                            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "parameter_missing", "参数缺失"));
                        }
                        agentCommissionType.setCommissionRateDefault(agentCommissionTypeDto.getCommissionRateDefault());
                    }
                    utilService.setUpdateInfo(agentCommissionType);
                    agentCommissionTypeMapper.updateById(agentCommissionType);
//                    return findAgentCommissionTypeById(agentCommissionType.getId());
                    return findAgentCommissionTypeById(agentCommissionTypeDto.getId()).getId();

                }
            }

        }
        throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_id_null"));
        }
        List<AgentCommissionPlan> agentCommissionPlanCount = agentCommissionPlanMapper.selectByFkAgentCommissionTypeId(id);
        if (GeneralTool.isNotEmpty(agentCommissionPlanCount)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_agent_commission_type_use"));
        }
        //根据id查询代理佣金类型和代理关系表0
        List<AgentCommissionTypeAgent> agentCommissionTypeAgents = agentCommissionTypeAgentMapper.selectByAgentCommissionTypeId(id);
        if (GeneralTool.isNotEmpty(agentCommissionTypeAgents)) {
            List<Long> ids = agentCommissionTypeAgents.stream().map(AgentCommissionTypeAgent::getId).collect(Collectors.toList());
            agentCommissionTypeAgentMapper.deleteByIds(ids);
        }
        agentCommissionTypeMapper.deleteById(id);

    }

    @Override
    public AgentCommissionTypeVo findAgentCommissionTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AgentCommissionType agentCommissionType = agentCommissionTypeMapper.selectById(id);
        AgentCommissionTypeVo agentCommissionTypeVo = BeanCopyUtils.objClone(agentCommissionType, AgentCommissionTypeVo::new);
        return agentCommissionTypeVo;
    }

    @Override
    public void sortAgentCommissionType(List<AgentCommissionTypeDto> agentCommissionTypeDtos) {
        if (GeneralTool.isEmpty(agentCommissionTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AgentCommissionType moveDataOne = BeanCopyUtils.objClone(agentCommissionTypeDtos.get(0), AgentCommissionType::new);
        AgentCommissionType agentCommissionTypeMoveDataOne = agentCommissionTypeMapper.selectById(moveDataOne.getId());
        if (GeneralTool.isNotEmpty(agentCommissionTypeMoveDataOne) && !agentCommissionTypeMoveDataOne.getViewOrder().equals(moveDataOne.getViewOrder())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
        }
        Integer oneViewOrder = moveDataOne.getViewOrder();
        AgentCommissionType moveDataTwo = BeanCopyUtils.objClone(agentCommissionTypeDtos.get(1), AgentCommissionType::new);
        AgentCommissionType agentCommissionTypeMoveDataTwo = agentCommissionTypeMapper.selectById(moveDataTwo.getId());
        if (GeneralTool.isNotEmpty(agentCommissionTypeMoveDataTwo) && !agentCommissionTypeMoveDataTwo.getViewOrder().equals(moveDataTwo.getViewOrder())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
        }
        Integer twoViewOrder = moveDataTwo.getViewOrder();
        moveDataOne.setViewOrder(twoViewOrder);
        utilService.updateUserInfoToEntity(moveDataOne);
        moveDataTwo.setViewOrder(oneViewOrder);
        utilService.updateUserInfoToEntity(moveDataTwo);
        agentCommissionTypeMapper.updateById(moveDataOne);
        agentCommissionTypeMapper.updateById(moveDataTwo);
    }

    /**
     * 判断字符串是否是合法 JSON 且包含 DEFAULT 字段
     *
     * @param jsonStr 待判断字符串
     * @return true 表示是合法 JSON 且包含 DEFAULT，false 否则
     */
    public static boolean isValidJsonWithDefault(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) return false;

        try {
            JSONObject jsonObject = JSON.parseObject(jsonStr.trim());
            return jsonObject.containsKey("DEFAULT");
        } catch (JSONException e) {
            return false;
        }
    }


}

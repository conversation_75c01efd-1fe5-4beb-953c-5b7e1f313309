package com.get.resumecenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.resumecenter.vo.SkillTypeVo;
import com.get.resumecenter.dto.SkillTypeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 10:56
 * @Description:技能类型管理业务类
 **/
public interface ISkillTypeService {
    /**
     * @return java.util.List<com.get.resumecenter.vo.SkillTypeVo>
     * @Description: 列表数据
     * @Param [skillTypeDto]
     * <AUTHOR>
     */
    List<SkillTypeVo> datas(SkillTypeDto skillTypeDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    SkillTypeVo findSkillTypeById(Long id);

    /**
     * 修改
     *
     * @param skillTypeDto
     * @return
     */
    SkillTypeVo updateSkillType(SkillTypeDto skillTypeDto);

    /**
     * 保存
     *
     * @param skillTypeDto
     * @return
     */
    Long addSkillType(SkillTypeDto skillTypeDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param skillTypeDtos
     * @return
     */
    void batchAdd(List<SkillTypeDto> skillTypeDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getSkillTypeSelect();

    /**
     * @return java.lang.String
     * @Description: 根据id查询名称
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    String getNameByTypeId(Long industryTypeId);

    /**
     * @return void
     * @Description: 上移下移
     * @Param [skillTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<SkillTypeDto> skillTypeDtos);
}

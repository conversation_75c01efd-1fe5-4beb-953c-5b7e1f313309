package com.get.resumecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.resumecenter.entity.Resume;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

;

@Mapper
public interface ResumeMapper extends BaseMapper<Resume> {

    int insertSelective(Resume record);


    Boolean isExistByResumeTypeId(Long resumeTypeId);

    Resume getByGuid(@Param("guid") String guid);
}
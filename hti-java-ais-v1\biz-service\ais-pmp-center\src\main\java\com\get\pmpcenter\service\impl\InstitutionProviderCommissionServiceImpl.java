package com.get.pmpcenter.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.alibaba.nacos.common.utils.UuidUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.dto.common.UpdateCommissionStatusDto;
import com.get.pmpcenter.dto.institution.SaveProviderCommissionDto;
import com.get.pmpcenter.entity.*;
import com.get.pmpcenter.enums.*;
import com.get.pmpcenter.event.publisher.CommissionChangedEventPublisher;
import com.get.pmpcenter.mapper.*;
import com.get.pmpcenter.service.*;
import com.get.pmpcenter.utils.EntityCompareUtil;
import com.get.pmpcenter.utils.GeneratorLogsUtil;
import com.get.pmpcenter.utils.ListComparisonUtil;
import com.get.pmpcenter.vo.common.CommissionMajorLevelVo;
import com.get.pmpcenter.vo.common.MajorLevelVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionListVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionPlanVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class InstitutionProviderCommissionServiceImpl extends ServiceImpl<InstitutionProviderCommissionMapper, InstitutionProviderCommission> implements InstitutionProviderCommissionService {

    @Autowired
    private InstitutionProviderCommissionMapper providerCommissionMapper;
    @Autowired
    private InstitutionProviderCommissionPlanMapper commissionPlanMapper;
    @Autowired
    private MajorLevelCustomMajorLevelMapper customMajorLevelMapper;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionService planInstitutionService;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionService commissionPlanInstitutionService;
    @Autowired
    private InstitutionProviderCommissionMajorLevelCustomService commissionMajorLevelCustomService;
    @Autowired
    private InstitutionProviderCommissionMajorLevelCustomMapper commissionMajorLevelCustomMapper;
    @Autowired
    private MajorLevelCustomMapper levelCustomMapper;
    @Autowired
    private CommissionChangedEventPublisher commissionChangedEventPublisher;
    @Autowired
    private AgentCommissionService agentCommissionService;
    @Autowired
    private AgentCommissionMajorLevelCustomService agentCommissionMajorLevelCustomService;
    @Autowired
    private InstitutionProviderContractService providerContractService;
    @Autowired
    private PmpMediaAndAttachedService pmpMediaAndAttachedService;
    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;


    @Override
    public ProviderCommissionListVo getProviderCommissionList(Long providerCommissionPlanId) {
        ProviderCommissionListVo result = new ProviderCommissionListVo();
        List<InstitutionProviderCommission> allCommissionList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId));
        ProviderCommissionListVo planInstitutionList = planInstitutionService.getPlanInstitutionList(2, providerCommissionPlanId);
        result.setInstitutionIds(planInstitutionList.getInstitutionIds());
        result.setInstitutionList(planInstitutionList.getInstitutionList());
        if (CollectionUtils.isEmpty(allCommissionList)) {
            //返回默认
            List<ProviderCommissionListVo.CommissionInfo> defaultcommissionList = getDefaultcommissionList(providerCommissionPlanId);
            result.setCommissionList(defaultcommissionList);
            return result;
        }
        //单项佣金方案
        result.setCommissionList(getCommissionList(providerCommissionPlanId));
        //组合佣金方案
        result.setCombinationList(getCombinationList(providerCommissionPlanId));
        //bonus佣金方案
        result.setBonusList(getBonusInfoList(providerCommissionPlanId));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProviderCommission(SaveProviderCommissionDto saveProviderCommissionDto, Boolean saveLog) {
        InstitutionProviderCommissionPlan commissionPlan = commissionPlanMapper.selectById(saveProviderCommissionDto.getCommissionPlanId());
        if (Objects.isNull(commissionPlan)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        //校验权限
        if (commissionPlan.getIsLocked().equals(1) && !SecureUtil.getLoginId().equals(commissionPlan.getGmtCreateUser())) {
            log.error("更新方案失败,没有权限,当前操作人:{},合同创建人:{}", SecureUtil.getLoginId(), commissionPlan.getGmtCreateUser());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_LOCKED", "方案已锁定,没有权限操作"));
        }
        List<Long> institutionIds = saveProviderCommissionDto.getInstitutionIds();
        if (CollectionUtils.isEmpty(institutionIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_CONTRACT_SCHOOL_MISSING", "学校不能为空"));
        }

        //合同没有附件不能保存
        int contractMediaCount = pmpMediaAndAttachedService.count(new LambdaQueryWrapper<PmpMediaAndAttached>()
                .eq(PmpMediaAndAttached::getFkTableName, MediaTableEnum.PROVIDER_CONTRACT.getCode())
                .eq(PmpMediaAndAttached::getFkTableId, commissionPlan.getFkInstitutionProviderContractId()));
        if (contractMediaCount < 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_CONTRACT_FILE_MISSING", "合同附件暂未上传,请先上传合同附件"));
        }
        List<LogDto> logs = new ArrayList<>();
        UserInfo user = SecureUtil.getUser();
        //保存佣金计划明细-单个
        List<LogDto> commissionLogs = saveCommissionList(saveProviderCommissionDto.getCommissionList(), saveProviderCommissionDto.getCommissionPlanId(), user.getLoginId(), commissionPlan.getFkInstitutionProviderContractId());
        //保存组合佣金明细
        List<LogDto> combinationLogs = saveCombinationList(saveProviderCommissionDto.getCombinationList(), saveProviderCommissionDto.getCommissionPlanId(), user.getLoginId(), commissionPlan.getFkInstitutionProviderContractId());
        //保存bonus佣金明细
        List<LogDto> bonusLogs = saveBonusInfoList(saveProviderCommissionDto.getBonusList(), saveProviderCommissionDto.getCommissionPlanId(), user.getLoginId(), commissionPlan.getFkInstitutionProviderContractId());
        //保存学校
        List<LogDto> institutionLogs = commissionPlanInstitutionService.saveProviderCommissionPlanInstitution(institutionIds, saveProviderCommissionDto.getCommissionPlanId(), commissionPlan.getFkInstitutionProviderContractId());
        //发布事件
        if (saveLog) {
            logs.addAll(institutionLogs);
            logs.addAll(commissionLogs);
            logs.addAll(combinationLogs);
            logs.addAll(bonusLogs);
            commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
        }
        //修改佣金方案审核状态
        commissionPlan.setGmtModified(new Date());
        commissionPlan.setGmtModifiedUser(SecureUtil.getLoginId());
        //如果方案处于审核中,自动上锁,并且方案状态保持不变
        if (commissionPlan.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            commissionPlan.setIsLocked(1);
        } else {
            //如果处于非审核中,保存后就变回未提交状态
            commissionPlan.setApprovalStatus(ApprovalStatusEnum.UN_COMMITTED.getCode());
        }
        commissionPlanMapper.updateById(commissionPlan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delCommissionByPlanId(Long planId) {
        //佣金明细与课程等级关联表
        List<InstitutionProviderCommission> commissionList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, planId));
        List<Long> commissionIds = commissionList.stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(commissionIds)) {
            commissionMajorLevelCustomMapper.delete(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                    .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, commissionIds));
        }
        //佣金明细
        providerCommissionMapper.delete(new LambdaQueryWrapper<InstitutionProviderCommission>()
                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, planId));

    }

    private List<LogDto> saveCommissionList(List<ProviderCommissionListVo.CommissionInfo> commissionList,
                                            Long providerCommissionPlanId, String loginId, Long contractId) {
        AtomicReference<Boolean> isEdit = new AtomicReference<>(Boolean.FALSE);
        //过滤没有课程等级ID的
        List<ProviderCommissionListVo.CommissionInfo> noLevelList = commissionList.stream().filter(commissionInfo -> Objects.isNull(commissionInfo.getLevelId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noLevelList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_COURSE_LEVEL_MISSING", "课程等级信息缺失"));
        }
        List<LogDto> logs = new ArrayList<>();
        List<InstitutionProviderCommissionMajorLevelCustom> providerCommissionMajorLevelCustomList = new ArrayList<>();
        List<Long> currentList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                        .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                        .eq(InstitutionProviderCommission::getCommissionType, 1))
                .stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList());

        //编辑的
        List<ProviderCommissionListVo.CommissionInfo> updateList = commissionList.stream()
                .filter(commissionInfo -> Objects.nonNull(commissionInfo.getId()) && commissionInfo.getId() > 0)
                .collect(Collectors.toList());
        //新增的
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<Long> updateIds = updateList.stream()
                    .map(ProviderCommissionListVo.CommissionInfo::getId)
                    .collect(Collectors.toList());

            commissionList = commissionList.stream()
                    .filter(commissionInfo -> Objects.isNull(commissionInfo.getId())
                            || !updateIds.contains(commissionInfo.getId()) || commissionInfo.getId().equals(0L))
                    .collect(Collectors.toList());
        }
        //删除的
        List<Long> updateIds = updateList.stream().map(ProviderCommissionListVo.CommissionInfo::getId).distinct().collect(Collectors.toList());
        currentList.removeAll(updateIds);
        Map<Long, Long> commissionLevelMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(updateIds)) {
            commissionLevelMap = commissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                            .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, updateIds))
                    .stream()
                    .collect(Collectors.toMap(
                            InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId,
                            InstitutionProviderCommissionMajorLevelCustom::getFkMajorLevelCustomId,
                            (existing, replacement) -> existing // 解决重复 key 的情况，保留已有值
                    ));
        }
        List<Long> deleteLevelIds = new ArrayList<>();
        List<Long> toRemoveInstitutionProviderCommissionIds = new ArrayList<>();
        Map<Long, Long> finalCommissionLevelMap = commissionLevelMap;
        List<InstitutionProviderCommission> updateCommissions = updateList.stream().map(commissionInfo -> {
            InstitutionProviderCommission commission = providerCommissionMapper.selectById(commissionInfo.getId());
            if (Objects.isNull(commission)) {
                return null;
            }
            InstitutionProviderCommission originalCommission = new InstitutionProviderCommission();
            BeanCopyUtils.copyProperties(commission, originalCommission);

            BeanCopyUtils.copyProperties(commissionInfo, commission);
            commission.setTitle(commissionInfo.getCustomName());
            commission.setTitleChn(commissionInfo.getCustomNameChn());
            commission.setGmtModified(new Date());
            commission.setGmtModifiedUser(loginId);
            commission.setCommissionType(1);

            Long levelId = finalCommissionLevelMap.get(commissionInfo.getId());
            if (Objects.nonNull(levelId) && !levelId.equals(commissionInfo.getLevelId())) {
                //修改了等级，需要删除原有关联
                deleteLevelIds.add(commission.getId());
                InstitutionProviderCommissionMajorLevelCustom relation = new InstitutionProviderCommissionMajorLevelCustom();
                relation.setFkInstitutionProviderCommissionId(commission.getId());
                relation.setFkMajorLevelCustomId(commissionInfo.getLevelId());
                relation.setGmtModified(new Date());
                relation.setGmtModifiedUser(loginId);
                relation.setGmtCreate(new Date());
                relation.setGmtCreateUser(loginId);
                providerCommissionMajorLevelCustomList.add(relation);
            }

            //值对比
            boolean change = EntityCompareUtil.compareFields(originalCommission, commission);
            if (change) {
                isEdit.set(Boolean.TRUE);
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanId, LogEventEnum.UPDATE_COMMISSION, loginId,
                        commission.getTitle(), contractId));
            }
            return commission;
        }).collect(Collectors.toList());
        updateCommissions.stream().filter(Objects::nonNull);

        if (CollectionUtils.isNotEmpty(updateCommissions)) {
            this.updateBatchById(updateCommissions);
            if (CollectionUtils.isNotEmpty(deleteLevelIds)) {
                commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                        .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, deleteLevelIds));
                toRemoveInstitutionProviderCommissionIds.addAll(deleteLevelIds);
            }
        }
        List<InstitutionProviderCommission> insertCommissions = commissionList.stream().map(commissionInfo -> {
            InstitutionProviderCommission commission = new InstitutionProviderCommission();
            BeanCopyUtils.copyProperties(commissionInfo, commission);
            commission.setTitle(commissionInfo.getCustomName());
            commission.setTitleChn(commissionInfo.getCustomNameChn());
            commission.setGmtModified(new Date());
            commission.setGmtModifiedUser(loginId);
            commission.setGmtCreate(new Date());
            commission.setGmtCreateUser(loginId);
            commission.setCommissionType(1);
            if (Objects.isNull(commissionInfo.getFkInstitutionProviderCommissionPlanId())) {
                commission.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
            }
            return commission;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertCommissions)) {
            isEdit.set(Boolean.TRUE);
            //记录日志
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.ADD_COMMISSION, loginId,
                    insertCommissions.size(), contractId));

            this.saveBatch(insertCommissions);
            for (int i = 0; i < insertCommissions.size(); i++) {
                ProviderCommissionListVo.CommissionInfo commissionInfo = commissionList.get(i);
                InstitutionProviderCommission commission = insertCommissions.get(i);
                InstitutionProviderCommissionMajorLevelCustom relation = new InstitutionProviderCommissionMajorLevelCustom();
                relation.setFkInstitutionProviderCommissionId(commission.getId());
                relation.setFkMajorLevelCustomId(commissionInfo.getLevelId());
                relation.setGmtModified(new Date());
                relation.setGmtModifiedUser(loginId);
                relation.setGmtCreate(new Date());
                relation.setGmtCreateUser(loginId);
                providerCommissionMajorLevelCustomList.add(relation);
            }
        }
        if (CollectionUtils.isNotEmpty(currentList)) {
            this.removeByIds(currentList);
            toRemoveInstitutionProviderCommissionIds.addAll(currentList);
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.DEL_COMMISSION, loginId,
                    currentList.size(), contractId));
            commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                    .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, currentList));
        }
        if (CollectionUtils.isNotEmpty(providerCommissionMajorLevelCustomList)) {
            commissionMajorLevelCustomService.saveBatch(providerCommissionMajorLevelCustomList);
        }

        //删除代理那边关联的明细
//        if (CollectionUtils.isNotEmpty(toRemoveInstitutionProviderCommissionIds)) {
//            List<Long> delAgentCommissionIds = agentCommissionService.list(new LambdaQueryWrapper<AgentCommission>()
//                            .in(AgentCommission::getFkInstitutionProviderCommissionId, toRemoveInstitutionProviderCommissionIds))
//                    .stream().map(AgentCommission::getId).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(delAgentCommissionIds)) {
//                agentCommissionMajorLevelCustomService.remove(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
//                        .in(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, delAgentCommissionIds));
//                agentCommissionService.removeByIds(delAgentCommissionIds);
//            }
//        }

        //修改代理佣金方案修改标记-0623调整为审核通过之后再修改
//        if (isEdit.get()) {
//            agentCommissionPlanService.update(new LambdaUpdateWrapper<AgentCommissionPlan>()
//                    .set(AgentCommissionPlan::getIsInstitutionProviderCommissionModify, 1)
//                    .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId));
//        }
        return logs;
    }


    private List<LogDto> saveCombinationList(List<ProviderCommissionListVo.CombinationInfo> combinationList,
                                             Long providerCommissionPlanId, String loginId, Long contractId) {
        List<LogDto> logs = new ArrayList<>();
        if (CollectionUtils.isEmpty(combinationList)) {
            List<InstitutionProviderCommission> delList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                    .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                    .eq(InstitutionProviderCommission::getCommissionType, 3));
            if (CollectionUtils.isNotEmpty(delList)) {
                Map<String, List<InstitutionProviderCommission>> packageList = delList.stream()
                        .collect(Collectors.groupingBy(InstitutionProviderCommission::getPackageKey));
                List<Long> delIds = delList.stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList());
                this.removeByIds(delIds);
                commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                        .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, delIds));
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanId, LogEventEnum.DEL_COMBINATION_COMMISSION, loginId,
                        packageList.size(), contractId));
            }
            return logs;
        }
        List<String> newPackageKeys = new ArrayList<>();
        List<InstitutionProviderCommissionMajorLevelCustom> providerCommissionMajorLevelCustomList = new ArrayList<>();
        List<ProviderCommissionListVo.CombinationInfo> newCombinationList = combinationList.stream()
                .filter(combinationInfo -> StringUtils.isBlank(combinationInfo.getPackageKey())).collect(Collectors.toList());

        //新增
        for (ProviderCommissionListVo.CombinationInfo combinationInfo : newCombinationList) {
            List<ProviderCommissionListVo.CommissionInfo> commissionList = combinationInfo.getCommissionInfoList();
            String packageKey = UuidUtils.generateUuid() + RandomUtil.randomString(5);
            List<InstitutionProviderCommission> insertList = commissionList.stream().map(commissionInfo -> {
                InstitutionProviderCommission commission = new InstitutionProviderCommission();
                BeanCopyUtils.copyProperties(commissionInfo, commission);
                commission.setTitle(commissionInfo.getCustomName());
                commission.setTitleChn(commissionInfo.getCustomNameChn());
                commission.setGmtModified(new Date());
                commission.setGmtModifiedUser(loginId);
                commission.setGmtCreate(new Date());
                commission.setGmtCreateUser(loginId);
                commission.setCommissionType(3);
                commission.setPackageKey(packageKey);
                commission.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
                commission.setPackageNameChn(combinationInfo.getPackageNameChn());
                commission.setPackageName(combinationInfo.getPackageName());
                if (Objects.isNull(commissionInfo.getFkInstitutionProviderCommissionPlanId())) {
                    commission.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
                }
                return commission;
            }).collect(Collectors.toList());
            newPackageKeys.add(packageKey);
            this.saveBatch(insertList);
            for (int i = 0; i < insertList.size(); i++) {
                ProviderCommissionListVo.CommissionInfo commissionInfo = commissionList.get(i);
                InstitutionProviderCommission commission = insertList.get(i);
                InstitutionProviderCommissionMajorLevelCustom relation = new InstitutionProviderCommissionMajorLevelCustom();
                relation.setFkInstitutionProviderCommissionId(commission.getId());
                relation.setFkMajorLevelCustomId(commissionInfo.getLevelId());
                relation.setGmtModified(new Date());
                relation.setGmtModifiedUser(loginId);
                relation.setGmtCreate(new Date());
                relation.setGmtCreateUser(loginId);
                providerCommissionMajorLevelCustomList.add(relation);
            }
        }
        if (CollectionUtils.isNotEmpty(newPackageKeys)) {
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.ADD_COMBINATION_COMMISSION, loginId,
                    newPackageKeys.size(), contractId));
        }

        //编辑
        List<ProviderCommissionListVo.CombinationInfo> updateCombinationList = combinationList.stream()
                .filter(combinationInfo -> StringUtils.isNotBlank(combinationInfo.getPackageKey())).collect(Collectors.toList());

        for (ProviderCommissionListVo.CombinationInfo combinationInfo : updateCombinationList) {
            AtomicReference<Boolean> change = new AtomicReference<>(false);
            List<Long> newIds = new ArrayList<>();
            List<ProviderCommissionListVo.CommissionInfo> commissionList = combinationInfo.getCommissionInfoList();
            //找出没有id的,就是新增的
            List<InstitutionProviderCommission> insertList = commissionList.stream().filter(commissionInfo -> Objects.isNull(commissionInfo.getId()) || commissionInfo.getId() < 1)
                    .map(commissionInfo -> {
                        InstitutionProviderCommission commission = new InstitutionProviderCommission();
                        BeanCopyUtils.copyProperties(commissionInfo, commission);
                        commission.setTitle(commissionInfo.getCustomName());
                        commission.setTitleChn(commissionInfo.getCustomNameChn());
                        commission.setGmtModified(new Date());
                        commission.setGmtModifiedUser(loginId);
                        commission.setGmtCreate(new Date());
                        commission.setGmtCreateUser(loginId);
                        commission.setCommissionType(3);
                        commission.setPackageName(combinationInfo.getPackageName());
                        commission.setPackageKey(combinationInfo.getPackageKey());
                        commission.setPackageNameChn(combinationInfo.getPackageNameChn());
                        if (Objects.isNull(commissionInfo.getFkInstitutionProviderCommissionPlanId())) {
                            commission.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
                        }
                        return commission;
                    }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(insertList)) {
                this.saveBatch(insertList);
                List<ProviderCommissionListVo.CommissionInfo> insertCommissionList = commissionList.stream()
                        .filter(commissionInfo -> Objects.isNull(commissionInfo.getId()) || commissionInfo.getId() < 1)
                        .collect(Collectors.toList());
                for (int i = 0; i < insertList.size(); i++) {
                    ProviderCommissionListVo.CommissionInfo commissionInfo = insertCommissionList.get(i);
                    InstitutionProviderCommission commission = insertList.get(i);
                    InstitutionProviderCommissionMajorLevelCustom relation = new InstitutionProviderCommissionMajorLevelCustom();
                    relation.setFkInstitutionProviderCommissionId(commission.getId());
                    relation.setFkMajorLevelCustomId(commissionInfo.getLevelId());
                    relation.setGmtModified(new Date());
                    relation.setGmtModifiedUser(loginId);
                    relation.setGmtCreate(new Date());
                    relation.setGmtCreateUser(loginId);
                    providerCommissionMajorLevelCustomList.add(relation);
                }
                newIds.addAll(insertList.stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList()));
                change.set(true);
            } else {
                change.set(false);
            }
            //要编辑的
            Map<Long, Long> levelMap = new HashMap<>();
            List<InstitutionProviderCommission> updateCommissions = commissionList.stream().filter(commissionInfo -> Objects.nonNull(commissionInfo.getId()) && commissionInfo.getId() > 0)
                    .map(commissionInfo -> {
                        InstitutionProviderCommission commission = providerCommissionMapper.selectById(commissionInfo.getId());
                        if (Objects.isNull(commission)) {
                            return null;
                        }
                        InstitutionProviderCommission originalCommission = new InstitutionProviderCommission();
                        BeanCopyUtils.copyProperties(commission, originalCommission);

                        BeanCopyUtils.copyProperties(commissionInfo, commission);
                        commission.setTitle(commissionInfo.getCustomName());
                        commission.setTitleChn(commissionInfo.getCustomNameChn());
                        commission.setGmtModified(new Date());
                        commission.setGmtModifiedUser(loginId);
                        commission.setCommissionType(3);
                        commission.setPackageName(combinationInfo.getPackageName());
                        commission.setPackageKey(combinationInfo.getPackageKey());
                        commission.setPackageNameChn(combinationInfo.getPackageNameChn());
                        levelMap.put(commission.getId(), commissionInfo.getLevelId());

                        if (!change.get()) {
                            change.set(EntityCompareUtil.compareFields(originalCommission, commission));
                        }
                        return commission;
                    }).collect(Collectors.toList());
            updateCommissions.stream().filter(Objects::nonNull);
            if (CollectionUtils.isNotEmpty(updateCommissions)) {
                this.updateBatchById(updateCommissions);
                Set<Long> keySet = levelMap.keySet();
                keySet.stream().forEach(levelId -> {
                    InstitutionProviderCommissionMajorLevelCustom relation = new InstitutionProviderCommissionMajorLevelCustom();
                    relation.setFkInstitutionProviderCommissionId(levelId);
                    relation.setFkMajorLevelCustomId(levelMap.get(levelId));
                    relation.setGmtModified(new Date());
                    relation.setGmtModifiedUser(loginId);
                    relation.setGmtCreate(new Date());
                    relation.setGmtCreateUser(loginId);
                    providerCommissionMajorLevelCustomList.add(relation);
                });
                //删除原本的关联关系
                commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                        .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, keySet));
            }
            //删除的
            List<Long> currentIds = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                            .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                            .eq(InstitutionProviderCommission::getCommissionType, 3)
                            .eq(InstitutionProviderCommission::getPackageKey, combinationInfo.getPackageKey())
                            .notIn(CollectionUtils.isNotEmpty(newIds), InstitutionProviderCommission::getId, newIds))
                    .stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList());
            currentIds.removeAll(updateCommissions.stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(currentIds)) {
                change.set(true);
                this.removeByIds(currentIds);
                commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                        .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, currentIds));
            }

            if (change.get()) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanId, LogEventEnum.UPDATE_COMBINATION_COMMISSION, loginId,
                        combinationInfo.getPackageName(), contractId));
            }
        }

        //删除整个package
        List<InstitutionProviderCommission> currentList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                .eq(InstitutionProviderCommission::getCommissionType, 3)
                .notIn(CollectionUtils.isNotEmpty(newPackageKeys), InstitutionProviderCommission::getPackageKey, newPackageKeys));
        if (CollectionUtils.isNotEmpty(currentList)) {
            //找出传过来有packageKey的
            List<String> currentPackageKeys = currentList.stream().map(InstitutionProviderCommission::getPackageKey).distinct().collect(Collectors.toList());
            currentPackageKeys.removeAll(updateCombinationList.stream().map(ProviderCommissionListVo.CombinationInfo::getPackageKey).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(currentPackageKeys)) {
                List<Long> delIds = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                                .eq(InstitutionProviderCommission::getCommissionType, 3)
                                .in(InstitutionProviderCommission::getPackageKey, currentPackageKeys))
                        .stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList());
                this.removeByIds(delIds);
                commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                        .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, delIds));
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanId, LogEventEnum.DEL_COMBINATION_COMMISSION, loginId,
                        currentPackageKeys.size(), contractId));
            }
        }

        if (CollectionUtils.isNotEmpty(providerCommissionMajorLevelCustomList)) {
            commissionMajorLevelCustomService.saveBatch(providerCommissionMajorLevelCustomList);
        }
        return logs;
    }

    private List<LogDto> saveBonusInfoList(List<ProviderCommissionListVo.BonusInfo> bonusList, Long providerCommissionPlanId,
                                           String loginId, Long contractId) {

        List<ProviderCommissionListVo.BonusInfo> noLevelIdList = bonusList.stream().filter(bonus -> CollectionUtils.isEmpty(bonus.getLevelIds())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noLevelIdList)) {
//            throw new GetServiceException("课程等级信息缺失");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_COURSE_LEVEL_MISSING", "课程等级信息缺失"));
        }
        List<LogDto> logs = new ArrayList<>();
        List<InstitutionProviderCommission> currentList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                .eq(InstitutionProviderCommission::getCommissionType, 2));
        if (CollectionUtils.isEmpty(bonusList)) {
            List<Long> delIds = currentList.stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delIds)) {

                this.removeByIds(delIds);
                commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                        .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, delIds));
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanId, LogEventEnum.DEL_BONUS_COMMISSION, loginId,
                        delIds.size(), contractId));
                return logs;
            }
        }
        List<InstitutionProviderCommissionMajorLevelCustom> providerCommissionMajorLevelCustomList = new ArrayList<>();
        List<Long> currentIds = currentList.stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList());
        //编辑
        List<ProviderCommissionListVo.BonusInfo> updateList = bonusList.stream()
                .filter(bonusInfo -> Objects.nonNull(bonusInfo.getId()) && bonusInfo.getId() > 0)
                .collect(Collectors.toList());

        //删除的
        List<Long> updateIds = updateList.stream().map(ProviderCommissionListVo.BonusInfo::getId).distinct().collect(Collectors.toList());
        currentIds.removeAll(updateIds);

        //编辑
        List<InstitutionProviderCommission> updateCommissions = updateList.stream().map(bonusInfo -> {
            InstitutionProviderCommission commission = providerCommissionMapper.selectById(bonusInfo.getId());
            if (Objects.isNull(commission)) {
                return null;
            }
            InstitutionProviderCommission originalCommission = new InstitutionProviderCommission();
            BeanCopyUtils.copyProperties(commission, originalCommission);

            BeanCopyUtils.copyProperties(bonusInfo, commission);
            commission.setGmtModified(new Date());
            commission.setGmtModifiedUser(loginId);
            commission.setCommissionType(2);

            boolean change = EntityCompareUtil.compareFields(originalCommission, commission);
            if (change) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanId, LogEventEnum.UPDATE_BONUS_COMMISSION, loginId, "", contractId));
            }
            //对比课程等级
            if (!change) {
                List<Long> newLevelIds = bonusInfo.getLevelIds();
                List<Long> oldLevelIds = commissionMajorLevelCustomService.list(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                                .eq(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, commission.getId()))
                        .stream().map(InstitutionProviderCommissionMajorLevelCustom::getFkMajorLevelCustomId).collect(Collectors.toList());
                boolean equal = ListComparisonUtil.areListsEqual(newLevelIds, oldLevelIds);
                if (!equal) {
                    logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.PROVIDER_PLAN,
                            providerCommissionPlanId, LogEventEnum.UPDATE_BONUS_COMMISSION, loginId, "", contractId));
                }
            }

            bonusInfo.getLevelIds().forEach(levelId -> {
                InstitutionProviderCommissionMajorLevelCustom relation = new InstitutionProviderCommissionMajorLevelCustom();
                relation.setFkInstitutionProviderCommissionId(commission.getId());
                relation.setFkMajorLevelCustomId(levelId);
                relation.setGmtModified(new Date());
                relation.setGmtModifiedUser(loginId);
                relation.setGmtCreate(new Date());
                relation.setGmtCreateUser(loginId);
                providerCommissionMajorLevelCustomList.add(relation);
            });
            return commission;
        }).collect(Collectors.toList());
        updateCommissions.stream().filter(Objects::nonNull);
        if (CollectionUtils.isNotEmpty(updateCommissions)) {
            this.updateBatchById(updateCommissions);
            commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                    .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, updateIds));
        }
        //新增
        List<ProviderCommissionListVo.BonusInfo> insertList = bonusList.stream().filter(bonus -> Objects.isNull(bonus.getId()) || bonus.getId() < 1).collect(Collectors.toList());
        List<InstitutionProviderCommission> insertCommissions = insertList.stream().map(bonusInfo -> {
            InstitutionProviderCommission commission = new InstitutionProviderCommission();
            BeanCopyUtils.copyProperties(bonusInfo, commission);
            commission.setGmtModified(new Date());
            commission.setGmtModifiedUser(loginId);
            commission.setGmtCreate(new Date());
            commission.setGmtCreateUser(loginId);
            commission.setCommissionType(2);
            if (Objects.isNull(bonusInfo.getFkInstitutionProviderCommissionPlanId())) {
                commission.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
            }
            return commission;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertCommissions)) {
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.ADD_BONUS_COMMISSION, loginId,
                    insertCommissions.size(), contractId));
            this.saveBatch(insertCommissions);
            for (int i = 0; i < insertCommissions.size(); i++) {
                ProviderCommissionListVo.BonusInfo commissionInfo = insertList.get(i);
                InstitutionProviderCommission commission = insertCommissions.get(i);
                commissionInfo.getLevelIds().forEach(levelId -> {
                    InstitutionProviderCommissionMajorLevelCustom relation = new InstitutionProviderCommissionMajorLevelCustom();
                    relation.setFkInstitutionProviderCommissionId(commission.getId());
                    relation.setFkMajorLevelCustomId(levelId);
                    relation.setGmtModified(new Date());
                    relation.setGmtModifiedUser(loginId);
                    relation.setGmtCreate(new Date());
                    relation.setGmtCreateUser(loginId);
                    providerCommissionMajorLevelCustomList.add(relation);
                });
            }
        }
        if (CollectionUtils.isNotEmpty(currentIds)) {
            this.removeByIds(currentIds);
            commissionMajorLevelCustomService.remove(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                    .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, currentIds));
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.DEL_BONUS_COMMISSION, loginId,
                    currentIds.size(), contractId));
        }
        if (CollectionUtils.isNotEmpty(providerCommissionMajorLevelCustomList)) {
            commissionMajorLevelCustomService.saveBatch(providerCommissionMajorLevelCustomList);
        }
        return logs;
    }

    private List<ProviderCommissionListVo.CommissionInfo> getDefaultcommissionList(Long providerCommissionPlanId) {
        InstitutionProviderCommissionPlan commissionPlan = commissionPlanMapper.selectById(providerCommissionPlanId);
        if (Objects.isNull(commissionPlan)) {
            log.error("获取佣金方案明细列表失败,佣金方案计划不存在，id:{}", providerCommissionPlanId);
//            throw new GetServiceException("方案不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        //查询通用等级
        List<MajorLevelVo> levelList = customMajorLevelMapper.selectMajorLevel(1);
        if (CollectionUtils.isEmpty(levelList)) {
            return new ArrayList<>();
        }
        List<ProviderCommissionListVo.CommissionInfo> list = levelList.stream().map(level -> {
            ProviderCommissionListVo.CommissionInfo commissionInfo = new ProviderCommissionListVo.CommissionInfo();
            commissionInfo.setLevelId(level.getLevelId());
            commissionInfo.setCustomName(level.getCustomName());
            commissionInfo.setCustomNameChn(level.getCustomNameChn());
            commissionInfo.setCommissionType(1);
            commissionInfo.setFkInstitutionProviderId(commissionPlan.getFkInstitutionProviderId());
            commissionInfo.setFkInstitutionProviderCommissionPlanId(commissionPlan.getId());
            commissionInfo.setIsActive(1);
            commissionInfo.setViewOrder(level.getViewOrder());
            commissionInfo.setParentLevelId(level.getParentLevelId());
            commissionInfo.setParentLevelName(level.getParentLevelName());
            commissionInfo.setParentLevelNameChn(level.getParentLevelNameChn());
            return commissionInfo;
        }).collect(Collectors.toList());
        return list;
    }

    private List<ProviderCommissionListVo.CommissionInfo> getCommonCommissionList(List<InstitutionProviderCommission> commissionList) {
        if (CollectionUtils.isEmpty(commissionList)) {
            return new ArrayList<>();
        }
        List<CommissionMajorLevelVo> commissionMajorLevelVos = commissionMajorLevelCustomService.getCommissionMajorLevelList(commissionList.stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList()));
        Map<Long, List<CommissionMajorLevelVo>> commissionLevelGroup = commissionMajorLevelVos.stream()
                .collect(Collectors.groupingBy(CommissionMajorLevelVo::getCommissionId));
        List<ProviderCommissionListVo.CommissionInfo> infoList = commissionList.stream().map(commissionInfo -> {
                    ProviderCommissionListVo.CommissionInfo info = new ProviderCommissionListVo.CommissionInfo();
                    BeanCopyUtils.copyProperties(commissionInfo, info);
                    info.setLevelId(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getLevelId());
                    info.setCustomName(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getCustomName());
                    info.setCustomNameChn(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getCustomNameChn());
                    info.setViewOrder(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getViewOrder());
                    info.setParentLevelId(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getParentLevelId());
                    info.setParentLevelName(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getParentLevelName());
                    info.setParentLevelNameChn(Objects.isNull(commissionLevelGroup.get(commissionInfo.getId())) ? null : commissionLevelGroup.get(commissionInfo.getId()).get(0).getParentLevelNameChn());
                    return info;
                }).sorted(Comparator.comparing(ProviderCommissionListVo.CommissionInfo::getViewOrder, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
        return infoList;
    }

    @Override
    public List<ProviderCommissionListVo.CommissionInfo> getCommissionList(Long providerCommissionPlanId) {
        List<InstitutionProviderCommission> commissionList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                .eq(InstitutionProviderCommission::getCommissionType, 1));
        return getCommonCommissionList(commissionList);
    }

    @Override
    public List<ProviderCommissionListVo.CombinationInfo> getCombinationList(Long providerCommissionPlanId) {
        List<InstitutionProviderCommission> combinationList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                .eq(InstitutionProviderCommission::getCommissionType, 3));
        Map<String, List<InstitutionProviderCommission>> packageList = combinationList.stream()
                .collect(Collectors.groupingBy(InstitutionProviderCommission::getPackageKey));
//        List<ProviderCommissionListVo.CombinationInfo> combinationInfoList = packageList.entrySet().stream().map(entry -> {
        List<ProviderCommissionListVo.CombinationInfo> combinationInfoList = packageList.entrySet().stream()
                .sorted(Comparator.comparing(entry -> entry.getValue().get(0).getId())) // ⭐ 根据第一个 AgentCommission 的 id 升序排序
                .map(entry -> {
                    ProviderCommissionListVo.CombinationInfo info = new ProviderCommissionListVo.CombinationInfo();
                    info.setPackageKey(entry.getKey());
                    info.setPackageName(entry.getValue().get(0).getPackageName());
                    info.setPackageNameChn(entry.getValue().get(0).getPackageNameChn());
                    List<ProviderCommissionListVo.CommissionInfo> commissionInfoList = getCommonCommissionList(entry.getValue());
                    info.setCommissionInfoList(commissionInfoList);
                    List<Long> commissionIds = entry.getValue().stream().map(InstitutionProviderCommission::getId).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(commissionIds)) {
                        List<Long> levelIds = commissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                                .in(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, commissionIds)
                        ).stream().map(InstitutionProviderCommissionMajorLevelCustom::getFkMajorLevelCustomId).distinct().collect(Collectors.toList());
                        info.setLevelIds(levelIds);
                        if (CollectionUtils.isNotEmpty(levelIds)) {
                            List<String> names = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                                            .in(MajorLevelCustom::getId, levelIds))
                                    .stream().map(MajorLevelCustom::getCustomName).distinct().collect(Collectors.toList());
                            info.setLevelNames(names);
                        }
                    }
                    return info;
                }).collect(Collectors.toList());
        return combinationInfoList;
    }

    @Override
    public List<ProviderCommissionListVo.BonusInfo> getBonusInfoList(Long providerCommissionPlanId) {
        List<InstitutionProviderCommission> bonusList = providerCommissionMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommission>()
                .eq(InstitutionProviderCommission::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                .eq(InstitutionProviderCommission::getCommissionType, 2));
        List<ProviderCommissionListVo.BonusInfo> bonusInfoList = bonusList.stream().map(commissionInfo -> {
            ProviderCommissionListVo.BonusInfo bonusInfo = new ProviderCommissionListVo.BonusInfo();
            BeanCopyUtils.copyProperties(commissionInfo, bonusInfo);
            List<Long> levelIds = commissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionMajorLevelCustom>()
                    .eq(InstitutionProviderCommissionMajorLevelCustom::getFkInstitutionProviderCommissionId, commissionInfo.getId())
            ).stream().map(InstitutionProviderCommissionMajorLevelCustom::getFkMajorLevelCustomId).distinct().collect(Collectors.toList());
            bonusInfo.setLevelIds(levelIds);
            if (CollectionUtils.isNotEmpty(levelIds)) {
                List<String> names = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                                .in(MajorLevelCustom::getId, levelIds))
                        .stream().map(MajorLevelCustom::getCustomName).distinct().collect(Collectors.toList());
                bonusInfo.setLevelNames(names);
            }
            return bonusInfo;
        }).collect(Collectors.toList());
        return sortBonusInfoList(bonusInfoList);
    }

    @Override
    public ProviderCommissionVo getProviderCommissionAndPermission(Long providerCommissionPlanId) {
        InstitutionProviderCommissionPlan plan = commissionPlanMapper.selectById(providerCommissionPlanId);
        if (Objects.isNull(plan)) {
            log.error("获取佣金方案详情失败,佣金方案计划不存在，id:{}", providerCommissionPlanId);
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        ProviderCommissionVo result = new ProviderCommissionVo();
        ProviderCommissionListVo providerCommissionList = getProviderCommissionList(providerCommissionPlanId);
        BeanCopyUtils.copyProperties(providerCommissionList, result);
        result.setIsLocked(Objects.nonNull(plan.getIsLocked()) ? plan.getIsLocked() : 0);
        result.setApprovalStatus(Objects.nonNull(plan.getApprovalStatus()) ? plan.getApprovalStatus() : ApprovalStatusEnum.UN_COMMITTED.getCode());
        ProviderCommissionPlanVo commissionPlanVo = new ProviderCommissionPlanVo();
        BeanCopyUtils.copyProperties(plan, commissionPlanVo);
        ProviderCommissionPlanVo planPermission = providerContractService.checkPlanPermission(commissionPlanVo);
        result.setLockPermission(planPermission.getLockPermission());
        result.setApprovalPermission(planPermission.getApprovalPermission());
        result.setPendingApprovalType(planPermission.getPendingApprovalType());
        result.setCurrentLoginId(SecureUtil.getLoginId());
        return result;
    }

    @Override
    public void updateCommissionStatus(UpdateCommissionStatusDto statusDto) {
        InstitutionProviderCommission commission = this.getById(statusDto.getCommissionId());
        if (Objects.isNull(commission)) {
            log.error("修改佣金明细状态失败,佣金明细不存在，id:{}", statusDto.getCommissionId());
            return;
        }
        commission.setIsActive(statusDto.getIsActive());
        commission.setGmtModified(new Date());
        commission.setGmtModifiedUser(SecureUtil.getLoginId());
        this.updateById(commission);
        //下架明细-同步到代理端下架明细
        if (statusDto.getIsActive().equals(0)) {
            agentCommissionService.update(new LambdaUpdateWrapper<AgentCommission>()
                    .set(AgentCommission::getIsActive, statusDto.getIsActive())
                    .set(AgentCommission::getGmtModified, new Date())
                    .set(AgentCommission::getGmtModifiedUser, SecureUtil.getLoginId())
                    .eq(AgentCommission::getFkInstitutionProviderCommissionId, statusDto.getCommissionId()));
        } else {
            //上架明细，只操作采购端，但需要标记修改标记，提示用户所作的修改。
            agentCommissionPlanService.update(new LambdaUpdateWrapper<AgentCommissionPlan>()
                    .set(AgentCommissionPlan::getIsInstitutionProviderCommissionModify, 1)
                    .set(AgentCommissionPlan::getGmtModified, new Date())
                    .set(AgentCommissionPlan::getGmtModifiedUser, SecureUtil.getLoginId())
                    .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, commission.getFkInstitutionProviderCommissionPlanId()));
        }
        //操作日志
        List<LogDto> logs = new ArrayList<>();
        InstitutionProviderCommissionPlan providerCommissionPlan = commissionPlanMapper.selectById(commission.getFkInstitutionProviderCommissionPlanId());
        logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.UPDATE, LogTableEnum.PROVIDER_PLAN,
                commission.getFkInstitutionProviderCommissionPlanId(), LogEventEnum.UPDATE_COMMISSION, SecureUtil.getLoginId(),
                commission.getTitle(), Objects.nonNull(providerCommissionPlan) ? providerCommissionPlan.getFkInstitutionProviderContractId() : 0L));
        commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
    }

    private List<ProviderCommissionListVo.BonusInfo> sortBonusInfoList(List<ProviderCommissionListVo.BonusInfo> bonusInfoList) {
        if (CollectionUtils.isEmpty(bonusInfoList)) {
            return new ArrayList<>();
        }
        List<Long> allLevelIds = bonusInfoList.stream()
                .filter(bonus -> CollectionUtils.isNotEmpty(bonus.getLevelIds()))
                .flatMap(bonus -> bonus.getLevelIds().stream())
                .distinct()
                .collect(Collectors.toList());
        //排序,优先排只有一个课程的，只有一个课程的根据viewOrder降序排序，多个课程等级的根据viewOrder的总和降序
        if (CollectionUtils.isEmpty(allLevelIds)) {
            return bonusInfoList;
        }
        Map<Long, Integer> levelViewOrderMap = levelCustomMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                        .in(MajorLevelCustom::getId, allLevelIds)).stream()
                .collect(Collectors.toMap(
                        MajorLevelCustom::getId,
                        MajorLevelCustom::getViewOrder,
                        Integer::max));
        bonusInfoList.sort((a, b) -> {
            int aSize = a.getLevelIds() != null ? a.getLevelIds().size() : 0;
            int bSize = b.getLevelIds() != null ? b.getLevelIds().size() : 0;

            // 1. 先按 levelId 数量，1个的排前面
            if (aSize == 1 && bSize != 1) {
                return -1;
            }
            if (aSize != 1 && bSize == 1) {
                return 1;
            }

            // 2. 如果都是 size==1，就用 viewOrder 倒序
            if (aSize == 1 && bSize == 1) {
                Integer aOrder = levelViewOrderMap.getOrDefault(a.getLevelIds().get(0), 0);
                Integer bOrder = levelViewOrderMap.getOrDefault(b.getLevelIds().get(0), 0);
                return Integer.compare(bOrder, aOrder);
            }

            // 3. 都是 size > 1，按 viewOrder 总和倒序
            int aSum = a.getLevelIds().stream()
                    .mapToInt(id -> levelViewOrderMap.getOrDefault(id, 0))
                    .sum();
            int bSum = b.getLevelIds().stream()
                    .mapToInt(id -> levelViewOrderMap.getOrDefault(id, 0))
                    .sum();
            return Integer.compare(bSum, aSum);
        });
        return bonusInfoList;
    }
}

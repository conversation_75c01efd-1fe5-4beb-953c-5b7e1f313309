<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.AgentCommissionTypeMapper">

    <select id="getAgentCommissionTypeList" resultType="com.get.pmpcenter.vo.agent.AgentCommissionTypeVo">
        select a.id,
        a.fk_company_id AS fkCompanyId,
        a.type_name AS typeName,
        a.commission_rate AS commissionRate,
        a.remark,
        a.view_order AS viewOrder,
        a.is_active AS isActive,
        a.gmt_create_user AS gmtCreateUser,
        a.gmt_create AS gmtCreate,
        a.is_show_only AS isShowOnly,
        a.commission_rate_default AS commissionRateDefault
        from ais_pmp2_center.u_agent_commission_type a
        <where>
            <if test="agentCommissionTypeDto != null">
                <!-- 公司ID精确匹配 -->
                <if test="agentCommissionTypeDto.fkCompanyId != null">
                    AND a.fk_company_id =
                    #{agentCommissionTypeDto.fkCompanyId}
                </if>
                <!-- 公司ID列表查询 -->
                <if test="agentCommissionTypeDto.companyIds != null and agentCommissionTypeDto.companyIds.size() > 0">
                    AND a.fk_company_id IN
                    <foreach collection="agentCommissionTypeDto.companyIds" item="companyId"
                             open="(" separator="," close=")">
                        #{companyId}
                    </foreach>
                </if>
                <!-- 类型名称模糊查询 -->
                <if test="agentCommissionTypeDto.typeName != null and agentCommissionTypeDto.typeName != ''">
                    AND a.type_name LIKE CONCAT('%',
                    #{agentCommissionTypeDto.typeName},
                    '%'
                    )
                </if>
                <!-- 激活状态 -->
                <if test="agentCommissionTypeDto.isActive != null">
                    AND a.is_active =
                    #{agentCommissionTypeDto.isActive}
                </if>
            </if>
        </where>
        ORDER BY a.view_order DESC
    </select>

    <select id="selectMaxViewOrderNumByAll" resultType="java.lang.Integer">
        select  COALESCE(MAX(view_order), 0) from ais_pmp2_center.u_agent_commission_type
    </select>
</mapper>
package com.von.ware.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.von.common.utils.PageUtils;
import com.von.common.utils.Query;
import com.von.common.utils.R;
import com.von.ware.dao.WareInfoDao;
import com.von.ware.entity.WareInfoEntity;
import com.von.ware.feign.MemberFeignService;
import com.von.ware.service.WareInfoService;
import com.von.ware.vo.FareVo;
import com.von.ware.vo.MemberAddressVo;
import lombok.RequiredArgsConstructor;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Service("wareInfoService")
@RequiredArgsConstructor
public class WareInfoServiceImpl extends ServiceImpl<WareInfoDao, WareInfoEntity> implements WareInfoService {


    private final MemberFeignService memberFeignService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<WareInfoEntity> wrapper = new QueryWrapper<>();
        Object keyObj = params.get("key");
        if (ObjectUtil.isNotNull(keyObj)) {
            String key = keyObj.toString();
            wrapper.eq("id", key).or()
                    .like("name", key).or()
                    .like("address", key).or()
                    .like("areacode", key);
        }

        IPage<WareInfoEntity> page = this.page(
                new Query<WareInfoEntity>().getPage(params),
                wrapper
        );

        return new PageUtils(page);
    }

    /**
     * 根据地址获取运费信息
     *
     * @param addrId
     * @return
     */
    @Override
    public FareVo getFare(Long addrId) {
        R r = this.memberFeignService.info(addrId);
        if (!r.isOk()) {
            return null;
        }
        MemberAddressVo memberAddressVo = r.getData("memberReceiveAddress", new TypeReference<MemberAddressVo>() {});
        FareVo fareVo = new FareVo();
        // TODO 计算运费
        fareVo.setFare(new BigDecimal(5));
        fareVo.setMemberAddressVo(memberAddressVo);

        return fareVo;
    }

    @Override
    public void test() {
        WareInfoServiceImpl wareInfoService = (WareInfoServiceImpl) AopContext.currentProxy();
        List<WareInfoEntity> list = wareInfoService.list();

        System.out.println(list);
    }

}
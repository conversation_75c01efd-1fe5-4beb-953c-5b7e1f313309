package com.get.aisplatformcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.MMessageMapper;
import com.get.aisplatformcenter.service.MMessageService;
import com.get.aisplatformcenterap.vo.MMessageVo;
import com.get.aisplatformcenterap.entity.MMessageEntity;
import com.get.aisplatformcenterap.dto.DeletePatchParamsDto;
import com.get.aisplatformcenterap.dto.MMessagePutAwayParamsDto;
import com.get.aisplatformcenterap.dto.MMessageParamsDto;
import com.get.common.result.Page;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【m_message(消息记录)】的数据库操作Service实现
* @createDate 2024-12-16 18:03:59
*/
@Service
public class MMessageServiceImpl extends ServiceImpl<MMessageMapper, MMessageEntity>
    implements MMessageService {
    @Autowired
    MMessageMapper  messageMapper;
    @Autowired
    private UtilService utilService;

    @Override
    public List<MMessageVo> searchPage(MMessageParamsDto params, Page page) {
        IPage<MMessageVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<MMessageVo>  result=messageMapper.searchPage(pages,params);
        int totalCount = (int)pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return result;
    }

    @Override
    public Long saveOrUpdateMessage(MMessageEntity entity) {
        if (entity == null) {
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,

                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","insert_vo_null!"));
        }
        try{
            if(entity.getWebMetaDescription()!=null && entity.getWebMetaDescription().length()>0){
                entity.setWebMetaDescription(java.net.URLDecoder.decode(entity.getWebMetaDescription(), "utf-8"));
            }

            if(Objects.isNull(entity.getWeight()) ){
                entity.setWeight(0);
            }

            utilService.updateUserInfoToEntity(entity);
            if(entity.getId()==null){
                entity.setStatus(1);
                messageMapper.insert(entity);
            }else {
                messageMapper.updateById(entity);
            }

        }catch(Exception e){
           log.error(e.getMessage());
        }


        return entity.getId();
    }

    @Override
    public Long putAwayMessage(MMessagePutAwayParamsDto vo) {

        if (vo == null) {

            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,

                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","数据为空!"));
        }

        if(vo.getType()==2){//上架
            messageMapper.updateByIds(vo.getType(),vo.getIds());
        } else if (vo.getType()==1) {//下架
            LambdaQueryWrapper<MMessageEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(MMessageEntity::getStatus, 2);
            int num=messageMapper.selectCount(wrapper);
            if(num<=1){
                throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,

                        LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","在架消息，最少保留一条在架!"));
            }
            messageMapper.updateByIds(vo.getType(),vo.getIds());
        }


        return 0l;
    }

    @Override
    public Long deleteOne(Long id) {

        MMessageEntity dbnum=messageMapper.selectById(id);
        if(GeneralTool.isEmpty(dbnum)){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","删除数据不存在!"));
        }
        if(dbnum.getStatus()!=null && dbnum.getStatus()==2){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,

                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","上架数据不能删除!"));
        }


        messageMapper.deleteById(id);
        return id;
    }

    @Override
    public void deleteBatch(DeletePatchParamsDto patchVo) {
        if(GeneralTool.isEmpty(patchVo.getIds())){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","ID不能为空!"));
        }
        List<MMessageEntity> list=messageMapper.selectBatchIds(Arrays.asList(patchVo.getIds()));
        Set<Integer> statusArr=list.stream().map(MMessageEntity::getStatus).collect(Collectors.toSet());
        if(statusArr.contains(2)){
                throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,

                        LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","上架数据不能删除!"));
        }
        messageMapper.deleteBatchIds(Arrays.asList(patchVo.getIds()));
    }


}





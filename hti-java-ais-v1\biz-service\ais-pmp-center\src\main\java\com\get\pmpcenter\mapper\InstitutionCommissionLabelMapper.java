package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.InstitutionCommissionLabel;
import com.get.pmpcenter.vo.institution.CustomizeLabelVo;
import com.get.pmpcenter.vo.institution.NameLabel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface InstitutionCommissionLabelMapper extends BaseMapper<InstitutionCommissionLabel> {

    /**
     * 根据学校Id查询业务标签
     *
     * @param institutionId
     * @return
     */
    List<NameLabel> selectBusinessLabel(@Param("institutionId") Long institutionId);

    /**
     * 根据标签Id查询自定义标签详情
     *
     * @param labelIds
     * @return
     */
    List<CustomizeLabelVo> selectCustomizeLabel(@Param("labelIds") List<Long> labelIds);

    /**
     * 查询自定义标签类型
     *
     * @return
     */
    List<CustomizeLabelVo> selectCustomizeLabelType();


    /**
     * 根据标签类型和关键字查询自定义标签
     * @param labelTypeId
     * @param keyword
     * @return
     */
    List<CustomizeLabelVo> selectCustomizeLabelByTypeAndKeyword(@Param("labelTypeId") Long labelTypeId,
                                                                @Param("keyword") String keyword);
}

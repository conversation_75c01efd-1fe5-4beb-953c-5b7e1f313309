package com.get.rocketmq.feign;

import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.rocketmq.producer.EmailProducer;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSenderQueueDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class RocKetMqCenterClient implements IRocKetMqCenterClient {

//    @Resource
//    private CustomMailQueueListener customMailQueueListener;
//
//    @Resource
//    private SystemMailQueueListener systemMailQueueListener;

    @Resource
    private EmailProducer emailProducer;

    @Override
    @VerifyLogin(IsVerify = false)
    public void getSystemSendEmail(EmailSystemMQMessageDto emailSystemMQMessageDto) {
        emailProducer.sendSystemMail(emailSystemMQMessageDto);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void specifiedPersonSendEmail(EmailCustomMQMessageDto emailCustomMQMessageDto) {
        emailProducer.sendCustomMail(emailCustomMQMessageDto);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void setSendEmailTaskToMq(EmailSenderQueueDto emailSenderQueueDto) {
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        BeanCopyUtils.copyProperties(emailSenderQueueDto,emailSenderQueue);
        emailProducer.sendEMailTaskToMq(emailSenderQueue);
    }
}

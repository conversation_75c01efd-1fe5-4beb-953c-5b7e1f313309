package com.von.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * 商品服务 常量枚举类集合
 *
 *
 * <AUTHOR>
 * @Date 2024/11/7 下午8:43
 * @Version 1.0
 */
public class ProductConstant {

    @Getter
    @ToString
    @AllArgsConstructor
    public enum StatusEnum {

        NEW_SPU(0, "新建"),

        SPU_UP(1, "商品上架"),

        SPU_DOWN(2, "商品下架");

        private int code;

        private String msg;

        private static final Map<Integer, StatusEnum> STATUS_ENUM_MAP = new HashMap<>();

        static {
            for (StatusEnum statusEnum : StatusEnum.values()) {
                STATUS_ENUM_MAP.put(statusEnum.getCode(), statusEnum);
            }
        }

        public static StatusEnum valueOf(int code) {
            return STATUS_ENUM_MAP.get(code);
        }

    }

}

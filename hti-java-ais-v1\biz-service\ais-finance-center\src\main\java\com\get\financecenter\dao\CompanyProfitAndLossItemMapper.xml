<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.CompanyProfitAndLossItemMapper">

    <select id="getCompanyProfitAndLossItem" resultType="com.get.financecenter.vo.CompanyProfitAndLossItemVo">
        SELECT
            mcpali.id,
            mcpali.fk_company_id,
            mcpali.title,
            mcpali.show_mode,
            mcpali.fk_accounting_item_id,
            mcpali.direction_value,
            mcpali.view_order,
            mcpali.color_code,
            mcpali.gmt_create,
            mcpali.gmt_create_user,
            mcpali.gmt_modified,
            mcpali.gmt_modified_user
        FROM m_company_profit_and_loss_item mcpali
        WHERE 1=1
        <if test="companyProfitAndLossItemDto.fkCompanyId != null and companyProfitAndLossItemDto.fkCompanyId != ''">
            AND mcpali.fk_company_id = #{companyProfitAndLossItemDto.fkCompanyId}
        </if>
        <if test="companyProfitAndLossItemDto.title != null and companyProfitAndLossItemDto.title != ''">
            AND mcpali.title LIKE CONCAT('%',#{companyProfitAndLossItemDto.title},'%')
        </if>
        <if test="companyProfitAndLossItemDto.fkAccountingItemId != null and companyProfitAndLossItemDto.fkAccountingItemId != ''">
            AND mcpali.fk_accounting_item_id = #{companyProfitAndLossItemDto.fkAccountingItemId}
        </if>
        <if test="companyProfitAndLossItemDto.directionValue != null and companyProfitAndLossItemDto.directionValue != ''">
            AND mcpali.direction_value = #{companyProfitAndLossItemDto.directionValue}
        </if>
        <if test="companyProfitAndLossItemDto.showMode != null and companyProfitAndLossItemDto.showMode != ''">
            AND mcpali.show_mode = #{companyProfitAndLossItemDto.showMode}
        </if>
        ORDER BY mcpali.view_order DESC
    </select>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        select
            IFNULL(max(view_order)+1,0) view_order
        from
            m_company_profit_and_loss_item

    </select>

    <update id="updateBatchById">
        <foreach collection="updateList" item="item" index="index" separator=";">
            UPDATE m_company_profit_and_loss_item
            SET
            <if test="item.viewOrder != null and item.viewOrder !=''">
                view_order = #{item.viewOrder},
            </if>
            gmt_modified = #{item.gmtModified},
            gmt_modified_user = #{item.gmtModifiedUser}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <insert id="batchInsert">
        INSERT INTO m_company_profit_and_loss_item (
            fk_company_id,
            title,
            show_mode,
            fk_accounting_item_id,
            direction_value,
            view_order,
            color_code,
            gmt_create,
            gmt_create_user,
            gmt_modified,
            gmt_modified_user
        )
        VALUES
        <foreach collection="companyProfitAndLossItemList" item="item" index="index" separator=",">
            (#{item.fkCompanyId},#{item.title},#{item.showMode},#{item.fkAccountingItemId},#{item.directionValue},#{item.viewOrder},
            #{item.colorCode},#{item.gmtCreate},#{item.gmtCreateUser},#{item.gmtModified},#{item.gmtModifiedUser})
        </foreach>

    </insert>
</mapper>
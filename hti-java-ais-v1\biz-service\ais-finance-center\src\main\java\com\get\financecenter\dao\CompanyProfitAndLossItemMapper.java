package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.CompanyProfitAndLossItemDto;
import com.get.financecenter.entity.CompanyProfitAndLossItem;
import com.get.financecenter.vo.CompanyProfitAndLossItemVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 公司损益表项管理Mapper
 */
@Mapper
public interface CompanyProfitAndLossItemMapper extends BaseMapper<CompanyProfitAndLossItem> {

    List<CompanyProfitAndLossItemVo> getCompanyProfitAndLossItem(IPage<CompanyProfitAndLossItem> pages,@Param("companyProfitAndLossItemDto") CompanyProfitAndLossItemDto companyProfitAndLossItemDto);

    /**
     * 获取最大排序值
     *
     * @return
     */
    Integer getMaxViewOrder();

    void updateBatchById(@Param("updateList") List<CompanyProfitAndLossItem> updateList);

    void batchInsert(@Param("companyProfitAndLossItemList") List<CompanyProfitAndLossItem> companyProfitAndLossItemList);
}


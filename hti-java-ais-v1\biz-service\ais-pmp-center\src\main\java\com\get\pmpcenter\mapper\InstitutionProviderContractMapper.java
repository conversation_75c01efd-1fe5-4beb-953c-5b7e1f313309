package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.pmpcenter.dto.institution.ProviderContractPageDto;
import com.get.pmpcenter.entity.InstitutionProviderContract;
import com.get.pmpcenter.vo.institution.ProviderContractVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface InstitutionProviderContractMapper extends BaseMapper<InstitutionProviderContract> {

    /**
     * 分页查询合同
     *
     * @param page
     * @param param
     * @return
     */
    List<ProviderContractVo> getProviderContractPage(IPage<ProviderContractVo> page, @Param("param") ProviderContractPageDto param,
                                                     @Param("companyIds") List<Long> companyIds,
                                                     @Param("contractIds") List<Long> contractIds);

    /**
     * 合同基本信息详情
     *
     * @param id
     * @return
     */
    ProviderContractVo getProviderContractById(Long id);


    /**
     * 根据国家id查询合同id(根据提供商绑定的学校对应的国家查询)
     *
     * @param countryIds
     * @return
     */
    List<Long> selectContractIdsByCountryIds(@Param("countryIds") List<Long> countryIds);


    /**
     * 查询到期的合同
     * @param institutionProviderId
     * @return
     */
    List<InstitutionProviderContract> selectExpireContract(@Param("institutionProviderId") Long institutionProviderId);

}

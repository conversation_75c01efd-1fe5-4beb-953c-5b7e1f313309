package com.get.remindercenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.component.AcceptOfferDeadlineReminderEmailHelper;
import com.get.remindercenter.component.EmailAbstractHelper;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.RemindTaskMapper;
import com.get.remindercenter.dto.AcceptOfferDeadlineReminderDto;
import com.get.remindercenter.dto.EmailParamsDto;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.RemindTask;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.service.EmailSenderQueueService;
import com.get.remindercenter.service.RemindTaskService;
import com.get.remindercenter.utils.SmsUtils;
import com.get.salecenter.vo.StudentOfferItemSendEmailVo;
import com.get.workflowcenter.vo.ActHiTaskInstVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.validator.internal.util.stereotypes.Lazy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 邮件发送器队列业务逻辑层
 */
@Service
@Slf4j
public class EmailSenderQueueServiceImpl extends BaseServiceImpl<EmailSenderQueueMapper, EmailSenderQueue> implements EmailSenderQueueService {

    @Resource(name = "emailHelperMap")
    private Map<String, EmailAbstractHelper> emailHelperMap;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private RemindTaskMapper remindTaskMapper;



    /**
     * 发送邮件
     *
     * @param
     * @return
     */
    @Override
    public Boolean sendMqEmail(EmailSenderQueue emailSenderQueue) {
        emailHelperMap.get(emailSenderQueue.getFkEmailTypeKey()).sendMail(emailSenderQueue);
        return null;
    }

    @Override
    public List<EmailSenderQueue> findDueEmailTasks() {
        //获取当前时间大于执行任务时间的数据
        Date nowDate = new Date();
        List<EmailSenderQueue> emailSenderQueues=emailSenderQueueMapper.findDueEmailTasks(nowDate);
        return  GeneralTool.isEmpty(emailSenderQueues) ? new ArrayList<>() : emailSenderQueues;
    }

    @Override
    public List<EmailSenderQueue> getEmailSenderQueues() {
        return  emailSenderQueueMapper.getEmailSenderQueues();
    }



    @Override
    public Boolean performEmailTasks(EmailSenderQueue emailSenderQueue) {
        String msg = null;
        try {
            log.info("执行任务开始----------------执行任务id：" +emailSenderQueue.getId());
            if (GeneralTool.isEmpty(emailSenderQueue)) {
                log.error("邮件任务不存在");
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_does_not_exist"));
            }
            if (GeneralTool.isEmpty(emailSenderQueue.getOperationStatus()) || emailSenderQueue.getOperationStatus().equals(1)) {
                log.error("任务未开启");
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_not_open"));
            }
            //根据模板类型key来进行发送邮件
           this.sendMqEmail(emailSenderQueue);
            return true;
        } catch (Exception e) {
            log.error("发送邮件异常：", e);
            //记录错误日志，并且错误次数+1
            Integer tryTimes = GeneralTool.isEmpty(emailSenderQueue.getOperationCount()) ? 0 : emailSenderQueue.getOperationCount();
            ++tryTimes;
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(tryTimes);
            emailSenderQueue.setGmtModified(new Date());
            emailSenderQueue.setGmtModifiedUser(emailSenderQueue.getGmtCreateUser());
            emailSenderQueueMapper.updateById(emailSenderQueue);
            return false;
        }

    }

    /**
     * 批量新增邮件任务
     * @param queueList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchAddEmailQueue(List<EmailSenderQueue> queueList) {
        for (EmailSenderQueue queue:queueList){
            queue.setOperationStatus(0);
            queue.setOperationCount(0);
            utilService.updateUserInfoToEntity(queue);
            emailSenderQueueMapper.insert(queue);
        }
        return true;
    }

    @Override
    public Boolean updateEmailQueue(EmailSenderQueue emailSenderQueue) {
        emailSenderQueueMapper.updateById(emailSenderQueue);
        return true;
    }

    @Override
    public void batchDelete(List<EmailSenderQueue> emailSenderQueues, Date optTime) {
        LambdaQueryWrapper<EmailSenderQueue> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        for(EmailSenderQueue emailSenderQueue:emailSenderQueues){
            if (GeneralTool.isNotEmpty(optTime)) {
                lambdaQueryWrapper.le(EmailSenderQueue::getOperationTime, optTime);
            }
            lambdaQueryWrapper.eq(EmailSenderQueue::getId,emailSenderQueue.getId());
            lambdaQueryWrapper.eq(EmailSenderQueue::getFkEmailTypeKey,emailSenderQueue.getFkEmailTypeKey());
            lambdaQueryWrapper.eq(EmailSenderQueue::getFkTableId,emailSenderQueue.getFkTableId());
            lambdaQueryWrapper.eq(EmailSenderQueue::getFkTableName,emailSenderQueue.getFkTableName());
            lambdaQueryWrapper.eq(EmailSenderQueue::getFkDbName,emailSenderQueue.getFkDbName());
            emailSenderQueueMapper.delete(lambdaQueryWrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(EmailSenderQueue emailSenderQueue, Date optTime) {
        LambdaQueryWrapper<EmailSenderQueue> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(EmailSenderQueue::getFkEmailTypeKey,emailSenderQueue.getFkEmailTypeKey());
        lambdaQueryWrapper.eq(EmailSenderQueue::getFkTableId,emailSenderQueue.getFkTableId());
        lambdaQueryWrapper.eq(EmailSenderQueue::getFkTableName,emailSenderQueue.getFkTableName());
        lambdaQueryWrapper.eq(EmailSenderQueue::getOperationStatus,0);
        emailSenderQueueMapper.delete(lambdaQueryWrapper);
    }
}

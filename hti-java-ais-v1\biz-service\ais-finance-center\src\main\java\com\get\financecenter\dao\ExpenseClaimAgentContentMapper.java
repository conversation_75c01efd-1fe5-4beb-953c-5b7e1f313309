package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.ExpenseClaimAgentContentDto;
import com.get.financecenter.entity.ExpenseClaimAgentContent;
import com.get.financecenter.vo.ExpenseClaimAgentContentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExpenseClaimAgentContentMapper extends BaseMapper<ExpenseClaimAgentContent> {

    List<ExpenseClaimAgentContentVo> getExpenseClaimAgentContents(IPage<ExpenseClaimAgentContent> pages, @Param("expenseClaimAgentContentDto") ExpenseClaimAgentContentDto expenseClaimAgentContentDto);

    boolean selectByName(String name);

    Integer getMaxViewOrder();

    void updateBatchById(@Param("updateList") List<ExpenseClaimAgentContent> updateList);

    int checkName(@Param("name") String name);
}
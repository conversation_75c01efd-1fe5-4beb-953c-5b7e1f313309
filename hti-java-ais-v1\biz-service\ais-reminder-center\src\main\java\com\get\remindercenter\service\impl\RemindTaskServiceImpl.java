package com.get.remindercenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.cache.CacheNames;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.RemindTaskMapper;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.dto.RemindTaskListDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.vo.ReminderTaskCountVo;
import com.get.remindercenter.entity.RemindTask;
import com.get.remindercenter.entity.RemindTaskQueue;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.service.RemindTaskService;
import com.get.remindercenter.vo.RemindTaskListVo;
import com.get.remindercenter.dto.RemindTaskUpdateDto;
import com.get.remindercenter.vo.RemindTaskVo;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.dto.ActReProcdefDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 12:23
 * Date: 2021/11/15
 * Description:提醒任务管理业务实现类
 */
@Service
@Slf4j
public class RemindTaskServiceImpl implements RemindTaskService {

    @Resource
    private UtilService utilService;
    @Resource
    private RemindTaskMapper remindTaskMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private RemindTaskQueueService remindTaskQueueService;
    @Resource
    private IOfficeCenterClient officeCenterClient;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;

    @Resource
    private EmailSenderQueueServiceImpl emailSenderQueueService;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    private static final String cache_key = "task_count";

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:12:24 2021/11/15
     */
    @Override
    public List<RemindTaskVo> datas(RemindTaskListDto remindTaskListVo) {
        List<RemindTask> remindTasks = remindTaskMapper.getRemindTaskList(remindTaskListVo, GetAuthInfo.getStaffId());
        if (GeneralTool.isEmpty(remindTasks)) {
            return new ArrayList<>();
        }
        List<RemindTaskVo> remindTaskDtos = new ArrayList<>();
        for (RemindTask remindTask : remindTasks) {
            RemindTaskVo remindTaskDto = BeanCopyUtils.objClone(remindTask, RemindTaskVo::new);
            dealStringToListDay(remindTaskDto);
            isCycle(remindTaskDto);
            remindMethodToRemindMethodList(remindTaskDto);
            remindTaskDtos.add(remindTaskDto);
        }
        return remindTaskDtos;
    }


    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:12:24 2021/11/15
     */
    @Override
    public void add(RemindTaskUpdateDto remindTaskUpdateVo) {
        //RemindTaskUpdateDto TaskRemark解码 base64
        try {
            remindTaskUpdateVo.setTaskRemark(new String(Base64.getDecoder().decode(remindTaskUpdateVo.getTaskRemark()), "UTF-8"));
        } catch (Exception e) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
            remindTaskUpdateVo.setTaskRemark(remindTaskUpdateVo.getTaskRemark());
        }
        if (GeneralTool.isEmpty(remindTaskUpdateVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //校验结束时间是否大于等于开始时间
        validateTime(remindTaskUpdateVo.getStartTime(), remindTaskUpdateVo.getEndTime());
        //判断任务开始时间是否大于当前时间，不是的话不允许新增
        Date nowDate = new Date();
        if (remindTaskUpdateVo.getStartTime().before(nowDate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_start_time_need_greater_now"));
        }
        if (GeneralTool.isEmpty(remindTaskUpdateVo.getFkDbName())) {
            remindTaskUpdateVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
        }
        if (GeneralTool.isEmpty(remindTaskUpdateVo.getFkTableName())) {
            remindTaskUpdateVo.setFkTableName(TableEnum.REMIND_TASK.key);
        }
        RemindTask remindTask = BeanCopyUtils.objClone(remindTaskUpdateVo, RemindTask::new);
        dealListDayToString(remindTask, remindTaskUpdateVo);
        //获取当前登陆人
        Long fkStaffId = SecureUtil.getStaffId();
        String[] remindMethodList = remindTaskUpdateVo.getRemindMethod();
        for (String remindMethod : remindMethodList) {
            //校验手机号或者邮箱不能为空
            validateEmailAndMobile(Integer.valueOf(remindMethod), fkStaffId);
        }
        String remindMethods = remindMethodToRemindMethodList(remindTaskUpdateVo);
        //不需要拆分，按原来的逻辑走
        if (!remindTaskUpdateVo.isNeedToSplit()) {
            //生成任务guid
            String taskGuid = UUID.randomUUID().toString();
            remindTask.setFkStaffId(fkStaffId);
            remindTask.setTaskGuid(taskGuid);
            remindTask.setRemindMethod(remindMethods);
            if (GeneralTool.isEmpty(remindTaskUpdateVo.getFkDbName())) {
                remindTask.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
            }
            utilService.updateUserInfoToEntity(remindTask);
            //清空redis的任务
            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
            remindTaskMapper.insert(remindTask);
            //新增任务到提醒任务执行队列表
            addRemindTaskQueue(remindTask);
            return;
        }
        //拆分新增任务
        addRemindTaskQueueSplit(remindTask, remindTaskUpdateVo, remindMethods);
    }


    /**
     * @Description: 批量新增任务
     * @Author: Jerry
     * @Date:13:00 2021/11/18
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchAdd(List<RemindTaskDto> remindTaskVos) {
        for (RemindTaskDto remindTaskVo : remindTaskVos) {
            if (GeneralTool.isEmpty(remindTaskVo.getFkDbName())) {
                remindTaskVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
            }
            if (GeneralTool.isEmpty(remindTaskVo.getFkTableName())) {
                remindTaskVo.setFkTableName(TableEnum.REMIND_TASK.key);
            }
            RemindTask remindTask = BeanCopyUtils.objClone(remindTaskVo, RemindTask::new);
            log.info("********"+remindTask.getTaskRemark());
            if (GeneralTool.isNotEmpty(remindTask.getTaskRemark())){
                Map<String,String> map = new HashMap<>();
                map.put("startDivHead","<div");
                map.put("startDiv","<div>");
                map.put("endDiv","</div>");
                map.put("br","<br>");
                String taskRemark = GetStringUtils.getReminderTemplate(map, remindTask.getTaskRemark());
                if (GeneralTool.isNotEmpty(taskRemark)) {
                    if (taskRemark.contains("&gt;")){
                        taskRemark = taskRemark.replace("&gt;", ">");
                    }
                    if (taskRemark.contains("&lt;")){
                        taskRemark = taskRemark.replace("&lt;", "<");
                    }
                    remindTask.setTaskRemark(taskRemark);
                }else {
                    remindTask.setTaskRemark(taskRemark);
                }

            }
            log.info("==========="+remindTask.getTaskRemark());

            //生成任务guid
            String taskGuid = UUID.randomUUID().toString();
            if (GeneralTool.isNotEmpty(remindTask.getTaskLink()) && remindTask.getTaskLink().contains("&amp;")) {
                remindTask.setTaskLink(remindTask.getTaskLink().replace("&amp;", "&"));
            }
            remindTask.setTaskGuid(taskGuid);
            utilService.updateUserInfoToEntity(remindTask);
            //清空redis的任务
            //清空的应为任务通知的人的redis缓存，不一定是登录人
//            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(remindTask.getFkStaffId()), cache_key);
            remindTaskMapper.insert(remindTask);
            //新增任务到提醒任务执行队列表
            addBatchRemindTaskQueue(remindTask);
        }
        return true;
    }

    /**
     * @Description: 批量新增任务
     * @Author: lucky
     * @Date:13:00 2025/05/27
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchAddTask(List<RemindTaskDto> remindTaskVos) {
        List<EmailSenderQueue> emailSenderQueues = new ArrayList<>();
        for (RemindTaskDto remindTaskVo : remindTaskVos) {
            if (GeneralTool.isEmpty(remindTaskVo.getFkDbName())) {
                remindTaskVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
            }
            if (GeneralTool.isEmpty(remindTaskVo.getFkTableName())) {
                remindTaskVo.setFkTableName(TableEnum.REMIND_TASK.key);
            }
            RemindTask remindTask = BeanCopyUtils.objClone(remindTaskVo, RemindTask::new);
            log.info("********"+remindTask.getTaskRemark());
//            if (GeneralTool.isNotEmpty(remindTask.getTaskRemark())){
//                Map<String,String> map = new HashMap<>();
//                map.put("startDivHead","<div");
//                map.put("startDiv","<div>");
//                map.put("endDiv","</div>");
//                map.put("br","<br>");
//                String taskRemark = GetStringUtils.getReminderTemplate(map, remindTask.getTaskRemark());
//                if (GeneralTool.isNotEmpty(taskRemark)) {
//                    if (taskRemark.contains("&gt;")){
//                        taskRemark = taskRemark.replace("&gt;", ">");
//                    }
//                    if (taskRemark.contains("&lt;")){
//                        taskRemark = taskRemark.replace("&lt;", "<");
//                    }
//                    remindTask.setTaskRemark(taskRemark);
//                }else {
//                    remindTask.setTaskRemark(taskRemark);
//                }
//
//            }
            log.info("==========="+remindTask.getTaskRemark());

            //生成任务guid
            String taskGuid = UUID.randomUUID().toString();
//            if (GeneralTool.isNotEmpty(remindTask.getTaskLink()) && remindTask.getTaskLink().contains("&amp;")) {
//                remindTask.setTaskLink(remindTask.getTaskLink().replace("&amp;", "&"));
//            }
            remindTask.setTaskGuid(taskGuid);
            utilService.updateUserInfoToEntity(remindTask);
            //清空redis的任务
            //清空的应为任务通知的人的redis缓存，不一定是登录人
//            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(remindTask.getFkStaffId()), cache_key);
            remindTaskMapper.insert(remindTask);
        }

        return true;
    }

    /**
     * 批量修改任务
     * @param remindTaskVos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateTaskNew(List<RemindTaskDto> remindTaskVos) {
        Map<String, List<RemindTaskDto>> map = remindTaskVos.stream().collect(Collectors.groupingBy(RemindTaskDto::getFkRemindEventTypeKey));
        for (Map.Entry<String, List<RemindTaskDto>> entry : map.entrySet()) {
            RemindTaskDto remindTaskVo = entry.getValue().get(0);
            LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = Wrappers.<RemindTask>lambdaQuery()
                    .eq(RemindTask::getFkTableName, remindTaskVo.getFkTableName())
                    .eq(RemindTask::getFkTableId, remindTaskVo.getFkTableId())
                    .eq(RemindTask::getFkRemindEventTypeKey,remindTaskVo.getFkRemindEventTypeKey());
            List<RemindTask> remindTasks = remindTaskMapper.selectList(lambdaQueryWrapper);
            Set<Long> staffIds = remindTasks.stream().filter(t -> GeneralTool.isNotEmpty(t.getFkStaffId()))
                    .map(RemindTask::getFkStaffId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(remindTasks)) {
                //任务ids
               // Set<Long> fkRemindTaskIds = remindTasks.stream().map(RemindTask::getId).collect(Collectors.toSet());
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setFkTableName(remindTaskVo.getFkTableName());
                emailSenderQueue.setFkTableId(remindTaskVo.getFkTableId());
                emailSenderQueue.setFkEmailTypeKey(remindTaskVo.getFkRemindEventTypeKey());
                emailSenderQueueService.delete(emailSenderQueue,null);
                if (GeneralTool.isNotEmpty(staffIds)){
                    for (Long staffId : staffIds) {
                        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(staffId), cache_key);
                    }
                }
                //删除任务
                remindTaskMapper.delete(lambdaQueryWrapper);
            }
        }

        if (GeneralTool.isNotEmpty(remindTaskVos)) {
            //新增新任务
            batchAddTask(remindTaskVos);
        }
        return true;
    }

    /**
     * 批量删除邮件任务和队列
     * @param remindTaskDtos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchDeleteTaskNew(List<RemindTaskDto> remindTaskDtos) {
        Map<String, List<RemindTaskDto>> map = remindTaskDtos.stream().collect(Collectors.groupingBy(RemindTaskDto::getFkRemindEventTypeKey));
        for (Map.Entry<String, List<RemindTaskDto>> entry : map.entrySet()) {
            RemindTaskDto remindTaskVo = entry.getValue().get(0);
            LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = Wrappers.<RemindTask>lambdaQuery()
                    .eq(RemindTask::getFkTableName, remindTaskVo.getFkTableName())
                    .eq(RemindTask::getFkTableId, remindTaskVo.getFkTableId())
                    .eq(RemindTask::getFkRemindEventTypeKey,remindTaskVo.getFkRemindEventTypeKey());
            List<RemindTask> remindTasks = remindTaskMapper.selectList(lambdaQueryWrapper);
            Set<Long> staffIds = remindTasks.stream().filter(t -> GeneralTool.isNotEmpty(t.getFkStaffId()))
                    .map(RemindTask::getFkStaffId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(remindTasks)) {
                //任务ids
                // Set<Long> fkRemindTaskIds = remindTasks.stream().map(RemindTask::getId).collect(Collectors.toSet());
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setFkTableName(remindTaskVo.getFkTableName());
                emailSenderQueue.setFkTableId(remindTaskVo.getFkTableId());
                emailSenderQueue.setFkEmailTypeKey(remindTaskVo.getFkRemindEventTypeKey());
                emailSenderQueueService.delete(emailSenderQueue,null);
                if (GeneralTool.isNotEmpty(staffIds)){
                    for (Long staffId : staffIds) {
                        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(staffId), cache_key);
                    }
                }
                //删除任务
                remindTaskMapper.delete(lambdaQueryWrapper);
            }
        }
        return true;
    }


    /**
     * @Description: 批量修改任务
     * @Author: Jerry
     * @Date:15:30 2021/11/25
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdate(List<RemindTaskDto> remindTaskVos, String fkTableName, Long fkTableId) {
//        Example example = new Example(RemindTask.class);
//        example.createCriteria().andEqualTo("fkTableName",fkTableName).andEqualTo("fkTableId",fkTableId);
        LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = Wrappers.<RemindTask>lambdaQuery()
                .eq(RemindTask::getFkTableName, fkTableName)
                .eq(RemindTask::getFkTableId, fkTableId);
        List<RemindTask> remindTasks = remindTaskMapper.selectList(lambdaQueryWrapper);
        Set<Long> staffIds = remindTasks.stream().filter(t -> GeneralTool.isNotEmpty(t.getFkStaffId()))
                .map(RemindTask::getFkStaffId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(remindTasks)) {
            //任务ids
            Set<Long> fkRemindTaskIds = remindTasks.stream().map(RemindTask::getId).collect(Collectors.toSet());
            //根据任务ids删除执行任务
            remindTaskQueueService.batchDelete(fkRemindTaskIds, null);
            //清空redis的任务
            //清空的应为任务通知的人的redis缓存，不一定是登录人
//            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
            if (GeneralTool.isNotEmpty(staffIds)){
                for (Long staffId : staffIds) {
                    CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(staffId), cache_key);
                }
            }
            //删除任务
            remindTaskMapper.delete(lambdaQueryWrapper);
        }
        if (GeneralTool.isNotEmpty(remindTaskVos)) {
            //新增新任务
            batchAdd(remindTaskVos);
        }
        return true;
    }


    /**
     * @Description: 批量修改任务(根据fkRemindEventTypeKey进行删除)
     * @Author: Jerry
     * @Date:15:30 2021/11/25
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateNew(List<RemindTaskDto> remindTaskVos) {

        Map<String, List<RemindTaskDto>> map = remindTaskVos.stream().collect(Collectors.groupingBy(RemindTaskDto::getFkRemindEventTypeKey));
        for (Map.Entry<String, List<RemindTaskDto>> entry : map.entrySet()) {
            RemindTaskDto remindTaskVo = entry.getValue().get(0);
            LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = Wrappers.<RemindTask>lambdaQuery()
                    .eq(RemindTask::getFkTableName, remindTaskVo.getFkTableName())
                    .eq(RemindTask::getFkTableId, remindTaskVo.getFkTableId())
                    .eq(RemindTask::getFkRemindEventTypeKey,remindTaskVo.getFkRemindEventTypeKey());
            List<RemindTask> remindTasks = remindTaskMapper.selectList(lambdaQueryWrapper);
            Set<Long> staffIds = remindTasks.stream().filter(t -> GeneralTool.isNotEmpty(t.getFkStaffId()))
                    .map(RemindTask::getFkStaffId).collect(Collectors.toSet());
            if (GeneralTool.isNotEmpty(remindTasks)) {
                //任务ids
                Set<Long> fkRemindTaskIds = remindTasks.stream().map(RemindTask::getId).collect(Collectors.toSet());
                //根据任务ids删除执行任务
                remindTaskQueueService.batchDelete(fkRemindTaskIds, null);
                //清空redis的任务
                //清空的应为任务通知的人的redis缓存，不一定是登录人
//            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
                if (GeneralTool.isNotEmpty(staffIds)){
                    for (Long staffId : staffIds) {
                        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(staffId), cache_key);
                    }
                }
                //删除任务
                remindTaskMapper.delete(lambdaQueryWrapper);
            }
        }


//        for (RemindTaskVo remindTaskVo : remindTaskVos) {
            //        Example example = new Example(RemindTask.class);
//        example.createCriteria().andEqualTo("fkTableName",fkTableName).andEqualTo("fkTableId",fkTableId);
//            LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = Wrappers.<RemindTask>lambdaQuery()
//                    .eq(RemindTask::getFkTableName, remindTaskVo.getFkTableName())
//                    .eq(RemindTask::getFkTableId, remindTaskVo.getFkTableId())
//                    .eq(RemindTask::getFkRemindEventTypeKey,remindTaskVo.getFkRemindEventTypeKey());
//            List<RemindTask> remindTasks = remindTaskMapper.selectList(lambdaQueryWrapper);
//            Set<Long> staffIds = remindTasks.stream().filter(t -> GeneralTool.isNotEmpty(t.getFkStaffId()))
//                    .map(RemindTask::getFkStaffId).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(remindTasks)) {
//                //任务ids
//                Set<Long> fkRemindTaskIds = remindTasks.stream().map(RemindTask::getId).collect(Collectors.toSet());
//                //根据任务ids删除执行任务
//                remindTaskQueueService.batchDelete(fkRemindTaskIds, null);
//                //清空redis的任务
//                //清空的应为任务通知的人的redis缓存，不一定是登录人
////            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
//                if (GeneralTool.isNotEmpty(staffIds)){
//                    for (Long staffId : staffIds) {
//                        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(staffId), cache_key);
//                    }
//                }
//                //修改任务为取消状态
//                RemindTask remindTask = new RemindTask();
//                remindTask.setStatus(0);
//                remindTaskMapper.update(remindTask,lambdaQueryWrapper);
//            }
//        }
        if (GeneralTool.isNotEmpty(remindTaskVos)) {
            //新增新任务
            batchAdd(remindTaskVos);
        }
        return true;
    }

    /**
     * @Description: 批量修改任务(根据fkRemindEventTypeKey进行删除)
     * @Author: Jerry
     * @Date:15:30 2021/11/25
     */
    @Override
    @Async
    public Boolean batchUpdatebatchImport(List<RemindTaskDto> remindTaskVos) {
        Map<Long, List<RemindTaskDto>> collect = remindTaskVos.stream().collect(Collectors.groupingBy(RemindTaskDto::getFkTableId));
        for (Map.Entry<Long, List<RemindTaskDto>> map1 : collect.entrySet()) {
            List<RemindTaskDto> value = map1.getValue();
            Map<String, List<RemindTaskDto>> map = value.stream().collect(Collectors.groupingBy(RemindTaskDto::getFkRemindEventTypeKey));
            for (Map.Entry<String, List<RemindTaskDto>> entry : map.entrySet()) {
                RemindTaskDto remindTaskVo = entry.getValue().get(0);
                LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = Wrappers.<RemindTask>lambdaQuery()
                        .eq(RemindTask::getFkTableName, remindTaskVo.getFkTableName())
                        .eq(RemindTask::getFkTableId, remindTaskVo.getFkTableId())
                        .eq(RemindTask::getFkRemindEventTypeKey,remindTaskVo.getFkRemindEventTypeKey());
                List<RemindTask> remindTasks = remindTaskMapper.selectList(lambdaQueryWrapper);
                Set<Long> staffIds = remindTasks.stream().filter(t -> GeneralTool.isNotEmpty(t.getFkStaffId()))
                        .map(RemindTask::getFkStaffId).collect(Collectors.toSet());
                if (GeneralTool.isNotEmpty(remindTasks)) {
                    //任务ids
                    Set<Long> fkRemindTaskIds = remindTasks.stream().map(RemindTask::getId).collect(Collectors.toSet());
                    //根据任务ids删除执行任务
                    remindTaskQueueService.batchDelete(fkRemindTaskIds, null);
                    //清空redis的任务
                    //清空的应为任务通知的人的redis缓存，不一定是登录人
//            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
                    if (GeneralTool.isNotEmpty(staffIds)){
                        for (Long staffId : staffIds) {
                            CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(staffId), cache_key);
                        }
                    }
                    //修改任务为取消状态
                    remindTaskMapper.delete(lambdaQueryWrapper);
                }
            }
        }
        if (GeneralTool.isNotEmpty(remindTaskVos)) {
            //新增新任务
            batchAdd(remindTaskVos);
        }
        return true;
    }



    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:12:24 2021/11/15
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(RemindTaskUpdateDto remindTaskUpdateVo) {
        if (GeneralTool.isEmpty(remindTaskUpdateVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        RemindTask remindTask = remindTaskMapper.selectById(remindTaskUpdateVo.getId());
        if (GeneralTool.isEmpty(remindTask)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //校验结束时间是否大于等于开始时间
        validateTime(remindTaskUpdateVo.getStartTime(), remindTaskUpdateVo.getEndTime());
        //判断任务开始时间是否大于当前时间，不是的话不允许更新
        Date nowDate = new Date();
        if (remindTaskUpdateVo.getStartTime().before(nowDate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_start_time_need_greater_now"));
        }
        String[] remindMethodList = remindTaskUpdateVo.getRemindMethod();
        for (String remindMethod : remindMethodList) {
            //校验手机号或者邮箱不能为空
            validateEmailAndMobile(Integer.valueOf(remindMethod), remindTask.getFkStaffId());
        }
        String remindMethods = remindMethodToRemindMethodList(remindTaskUpdateVo);
        if (GeneralTool.isEmpty(remindTaskUpdateVo.getFkDbName())) {
            remindTaskUpdateVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
        }
        BeanUtils.copyProperties(remindTaskUpdateVo, remindTask);
        dealListDayToString(remindTask, remindTaskUpdateVo);
        remindTask.setRemindMethod(remindMethods);
        //不需要拆分，按原来的逻辑走
        if (!remindTaskUpdateVo.isNeedToSplit()) {
            utilService.updateUserInfoToEntity(remindTask);
            remindTaskMapper.updateByPrimaryKey(remindTask);
            //删除提醒任务执行队列表
            remindTaskQueueService.delete(remindTaskUpdateVo.getId(), null);
            //新增任务到提醒任务执行队列表
            addRemindTaskQueue(remindTask);
            return;
        }
        //删除原来的任务
        remindTaskMapper.deleteById(remindTask.getId());
        //清空redis的任务
        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
        //删除提醒任务执行队列表
        remindTaskQueueService.delete(remindTaskUpdateVo.getId(), null);
        //拆分新增任务
        addRemindTaskQueueSplit(remindTask, remindTaskUpdateVo, remindMethods);
    }


    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:12:24 2021/11/15
     */
    @Override
    public RemindTaskVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        RemindTask remindTask = remindTaskMapper.selectById(id);
        if (GeneralTool.isEmpty(remindTask)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        RemindTaskVo remindTaskDto = BeanCopyUtils.objClone(remindTask, RemindTaskVo::new);
        dealStringToListDay(remindTaskDto);
        isCycle(remindTaskDto);
        remindMethodToRemindMethodList(remindTaskDto);
        return remindTaskDto;
    }


    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:12:24 2021/11/15
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        remindTaskMapper.deleteById(id);
        //删除提醒任务执行队列表
        remindTaskQueueService.delete(id, null);

        //清空redis的任务
        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
    }


//    /**
//     * @Description: 获取任务总数
//     * @Author: Jerry
//     * @Date:16:49 2021/11/16
//     */
//    @Override
//    public Integer getTaskCount() {
//        List<RemindTask> remindTasks = remindTaskMapper.getTaskCount(GetAuthInfo.getStaffId(),new Date());
//        return GeneralTool.isEmpty(remindTasks) ? 0 : remindTasks.size();
//    }

    /**
     * @Description: 获取任务总数
     * @Author: Jerry
     * @Date:16:49 2021/11/16
     */
    @Override
    public List<ReminderTaskCountVo> getTaskCount() {
        List<ReminderTaskCountVo> reminderTaskCountDtos = new ArrayList<>();
        Long staffId = SecureUtil.getStaffId();
        Object value = CacheUtil.get(CacheNames.TASK_CACHE, String.valueOf(staffId), cache_key);
        if (GeneralTool.isNotEmpty(value)) {
            reminderTaskCountDtos = JSONObject.parseObject(value.toString(), new TypeReference<List<ReminderTaskCountVo>>() {
            });
        } else {
            List<String> keys = ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.REMINDER_MODULAR);
            List<String> k2 = ProjectKeyEnum.getValuesByKeys(ProjectKeyEnum.REMINDER_COUNT);
            List<RemindTask> remindTasks = remindTaskMapper.getTaskCount(staffId, new Date(), true, keys);
            remindTasks.addAll(remindTaskMapper.getTaskCount(staffId, new Date(), false, k2));
//            Map<String, List<RemindTask>> map = remindTasks.stream().collect(Collectors.groupingBy(RemindTask::getFkDbName));
            //不要删别人代码  注释不行吗 改了逻辑请验证下是否正确
            Map<String, List<RemindTask>> map = new HashMap<>();

            keys.addAll(k2);
            for (RemindTask remindTask : remindTasks) {
                for (String key : keys) {
                    if (GeneralTool.isNotEmpty(remindTask.getFkDbName())&&remindTask.getFkDbName().contains(key)){
                        if (GeneralTool.isEmpty(map.get(key))){
                            List<RemindTask> remindTaskList = new ArrayList<>();
                            remindTaskList.add(remindTask);
                            map.put(key,remindTaskList);
                        }else {
                            map.get(key).add(remindTask);
                        }
                    }
                }
            }

            k2.clear();
            List<RemindTask> tasks;
            for (String key : keys) {
                ReminderTaskCountVo reminderTaskCountDto = new ReminderTaskCountVo();
                reminderTaskCountDto.setKey(key);
                tasks = map.get(key);
                if (tasks != null) {
                    reminderTaskCountDto.setCount(tasks.size());
                } else {
                    reminderTaskCountDto.setCount(0);
                }
                reminderTaskCountDtos.add(reminderTaskCountDto);
            }

            ReminderTaskCountVo reminderTaskCountDto = new ReminderTaskCountVo();
            reminderTaskCountDto.setKey("sum");
            reminderTaskCountDto.setCount(remindTasks.size());
            reminderTaskCountDtos.add(reminderTaskCountDto);
            List<ReminderTaskCountVo> officeReminderTaskCountDtos = officeCenterClient.getApplicationAndApprovalCount(staffId).getData();
            reminderTaskCountDtos.addAll(officeReminderTaskCountDtos);

            Integer officeCentCount = 0;
            for (ReminderTaskCountVo officeReminderTaskCountDto : officeReminderTaskCountDtos) {
                officeCentCount += officeReminderTaskCountDto.getCount();
            }


            //查询待办和待签列表 设置提醒数 确保和列表数据一致
            Integer toDoCount = null;
            Integer toSignedCount = null;
            SearchBean<ActReProcdefDto> actReProcdefVoSearchBean = new SearchBean<ActReProcdefDto>();
            ActReProcdefDto actReProcdefDto = new ActReProcdefDto();
            actReProcdefVoSearchBean.setData(actReProcdefDto);
            actReProcdefVoSearchBean.setShowCount(50);
            actReProcdefVoSearchBean.setCurrentPage(1);
            actReProcdefVoSearchBean.setTotalResult(0);
            ResponseBo<ActRuTaskVo> toDoResponseBo = workflowCenterClient.getToDoMatter(actReProcdefVoSearchBean).getData();
            if (GeneralTool.isNotEmpty(toDoResponseBo)){
                toDoCount = toDoResponseBo.getPage().getTotalResult();
            }
            ResponseBo<ActRuTaskVo> toSignedResponseBo = workflowCenterClient.getToSignedMatter(actReProcdefVoSearchBean).getData();
            if (GeneralTool.isNotEmpty(toSignedResponseBo)){
                toSignedCount = toSignedResponseBo.getPage().getTotalResult();
            }

            List<ReminderTaskCountVo> myAppCountDto = reminderTaskCountDtos.stream().filter(r -> r.getKey().equals(ProjectKeyEnum.OFFICE_CENTER_APPROVAL.key)).collect(Collectors.toList());
            Integer myAppCount = myAppCountDto.get(0).getCount();
            List<ReminderTaskCountVo> allAppCountDto = reminderTaskCountDtos.stream().filter(r -> r.getKey().equals(ProjectKeyEnum.OFFICE_CENTER_ALL_FORM.key)).collect(Collectors.toList());
            Integer allAppCount = allAppCountDto.get(0).getCount();
            Integer finalToDoCount = toDoCount;
            Integer finalToSignedCount = toSignedCount;
            Integer workflowCount = finalToDoCount+finalToSignedCount;
            Integer finalOfficeCentCount = officeCentCount;
            List<String> apiResourceKeys = SecureUtil.getApiResourceKeys();
            String allFormPermission = "officeLeaveApplicationForm.AllForms";
            reminderTaskCountDtos = reminderTaskCountDtos.stream().map(r->{
                if (r.getKey().equals(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key)){
                    if (GeneralTool.isNotEmpty(finalToDoCount)){
                        if (!finalToDoCount.equals(r.getCount())){
                            r.setCount(finalToDoCount);
                        }
                    }
                }
                if (r.getKey().equals(ProjectKeyEnum.WORKFLOW_CENTER_TO_SIGN.key)){
                    if (GeneralTool.isNotEmpty(finalToSignedCount)){
                        if (!finalToSignedCount.equals(r.getCount())){
                            r.setCount(finalToSignedCount);
                        }
                    }
                }
                if (r.getKey().equals(ProjectKeyEnum.WORKFLOW_CENTER.key)){
                    if (!workflowCount.equals(r.getCount())){
                        r.setCount(workflowCount);
                    }
                }
                if (r.getKey().equals(ProjectKeyEnum.OFFICE_CENTER.key)){
                    if (apiResourceKeys.contains(allFormPermission)||SecureUtil.getStaffInfo().getIsAdmin()){
                        r.setCount(allAppCount==null?0:allAppCount);
                    }else {
                        r.setCount(myAppCount==null?0:myAppCount);
                    }
                }
                return r;
            }).collect(Collectors.toList());

            CacheUtil.put(CacheNames.TASK_CACHE, String.valueOf(staffId), cache_key, JSON.toJSONString(reminderTaskCountDtos));
        }
        return reminderTaskCountDtos;
    }

    /**
     * @Description: 批量修改任务(支持表的多个Id)
     * @Author: Jerry
     * @Date:17:40 2021/12/1
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchUpdateByTableIds(List<RemindTaskDto> remindTaskVos, String fkTableName, Set<Long> fkTableIds) {
        LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(RemindTask::getFkTableName, fkTableName);
        lambdaQueryWrapper.in(RemindTask::getFkTableId, fkTableIds);
        List<RemindTask> remindTasks = remindTaskMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(remindTasks)) {
            //任务ids
            Set<Long> fkRemindTaskIds = remindTasks.stream().map(RemindTask::getId).collect(Collectors.toSet());
            //根据任务ids删除执行任务
            remindTaskQueueService.batchDelete(fkRemindTaskIds, null);
            //删除任务
            remindTaskMapper.delete(lambdaQueryWrapper);
        }
        if (GeneralTool.isNotEmpty(remindTaskVos)) {
            //新增新任务
            batchAdd(remindTaskVos);
        }
        return true;
    }

    @Override
    public Boolean batchDeleteByTableId(String fkTableName, Long fkTableId, List<String> fkRemindEventTypeKeys) {
        LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = Wrappers.<RemindTask>lambdaQuery()
                .eq(RemindTask::getFkTableName, fkTableName)
                .eq(RemindTask::getFkTableId, fkTableId)
                .in(RemindTask::getFkRemindEventTypeKey,fkRemindEventTypeKeys);

        List<RemindTask> remindTasks = remindTaskMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(remindTasks)) {
            //任务ids
            Set<Long> fkRemindTaskIds = remindTasks.stream().map(RemindTask::getId).collect(Collectors.toSet());
            //根据任务ids删除执行任务
            remindTaskQueueService.batchDelete(fkRemindTaskIds, null);
        }
        //修改任务为取消状态
        RemindTask remindTask = new RemindTask();
        remindTask.setStatus(0);
        remindTaskMapper.update(remindTask,lambdaQueryWrapper);
        return true;
    }

    @Override
    public List<RemindTaskListVo> getRemindTaskDatas(RemindTaskListDto remindTaskListVo, Page page) {

        if (GeneralTool.isEmpty(remindTaskListVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<RemindTaskListVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<RemindTaskListVo> datas=remindTaskMapper.getRemindTaskDatas(iPage,remindTaskListVo);
        page.setAll((int) iPage.getTotal());
        for (RemindTaskListVo remindTaskListDto : datas) {
            if (GeneralTool.isNotEmpty(remindTaskListDto.getRemindMethod())){
                StringJoiner stringJoiner = new StringJoiner(" ");
                String[] remindMethodString = remindTaskListDto.getRemindMethod().split(",");
                for (String name : remindMethodString) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.REMINDER_TYPE));
                }
                remindTaskListDto.setRemindMethodString(stringJoiner.toString());
            }
            if (GeneralTool.isNotEmpty(remindTaskListDto.getAdvanceDays())){
                remindTaskListDto.setAdvanceReminderString("提前"+remindTaskListDto.getAdvanceDays().replace("-","")+"天提醒");
            } else if (GeneralTool.isNotEmpty(remindTaskListDto.getAdvanceHours())) {
                remindTaskListDto.setAdvanceReminderString("提前"+remindTaskListDto.getAdvanceHours().replace("-","")+"小时提醒");
            }
        }
        return datas;
    }


    /**
     * @Description: 校验结束时间是否大于等于开始时间
     * @Author: Jerry
     * @Date:14:59 2021/11/15
     */
    private void validateTime(Date startTime, Date endTime) {
        if (GeneralTool.isNotEmpty(endTime)) {
            if (startTime.after(endTime)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("end_time_need_greater_start_time"));
            }
        }
    }


    /**
     * @Description: 校验手机号或者邮箱不能为空
     * @Author: Jerry
     * @Date:17:03 2021/11/15
     */
    private void validateEmailAndMobile(Integer remindMethod, Long fkStaffId) {
        if (ProjectExtraEnum.REMIND_METHOD_EMAIL.key.equals(remindMethod)) {
            Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(fkStaffId);
            StaffVo staffVo = null;
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                staffVo = result.getData();
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
            }
            String email = staffVo.getEmail();
            if (GeneralTool.isEmpty(email)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("staff_email_null"));
            }
        } else if (ProjectExtraEnum.REMIND_METHOD_MESSAGE.key.equals(remindMethod)) {
            Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(fkStaffId);
            StaffVo staffVo = null;
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                staffVo = result.getData();
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            String mobile = staffVo.getMobile();
            String mobileAreaCode = staffVo.getMobileAreaCode();
            if (GeneralTool.isEmpty(mobile) || GeneralTool.isEmpty(mobileAreaCode)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("staff_mobile_or_mobileArea_null"));
            }
        }
    }


    /**
     * @Description: 新增任务
     * @Author: Jerry
     * @Date:15:11 2021/11/16
     */
    @Transactional(rollbackFor = Exception.class)
    public void addRemindTaskQueue(RemindTask remindTask) {
        //获取任务数
        List<Date> datas = getListDates(remindTask);
        RemindTaskQueue remindTaskQueue = null;
        Long fkRemindTaskId = remindTask.getId();
        String remindMethods = remindTask.getRemindMethod();
        String[] remindMethodList = remindMethods.split(",");
        //新增任务到提醒任务执行队列表
        for (Date optTime : datas) {
            //可能会有多个提醒
            for (String remindMethod : remindMethodList) {
                remindTaskQueue = new RemindTaskQueue();
                remindTaskQueue.setRemindMethod(remindMethod);
                remindTaskQueue.setTryTimes(0);
                remindTaskQueue.setFkRemindTaskId(fkRemindTaskId);
                remindTaskQueue.setOptTime(optTime);
                utilService.updateUserInfoToEntity(remindTaskQueue);
                remindTaskQueueService.add(remindTaskQueue);
            }
        }
    }


    /**
     * @Description: 拆分新增任务
     * @Author: Jerry
     * @Date:15:11 2021/11/16
     */
    @Transactional(rollbackFor = Exception.class)
    public void addRemindTaskQueueSplit(RemindTask remindTask, RemindTaskUpdateDto remindTaskUpdateVo, String remindMethods) {
        //获取任务数
        List<Date> datas = getListDates(remindTask);
        RemindTaskQueue remindTaskQueue = null;
        String[] remindMethodList = remindMethods.split(",");
        RemindTask newRemindTask = null;
        //新增任务到提醒任务执行队列表
        for (Date optTime : datas) {
            newRemindTask = new RemindTask();
            BeanUtils.copyProperties(remindTaskUpdateVo, newRemindTask);
            newRemindTask.setId(null);
            //清空间隔日期
            newRemindTask.setAdvanceDays(null);
            newRemindTask.setAdvanceHours(null);
            newRemindTask.setLoopIntervalDays(null);
            newRemindTask.setLoopWeekDays(null);
            newRemindTask.setLoopMonthDays(null);
            //一条任务对应一条执行任务
            String taskGuid = UUID.randomUUID().toString();
            newRemindTask.setTaskGuid(taskGuid);
            newRemindTask.setFkStaffId(GetAuthInfo.getStaffId());
            newRemindTask.setStartTime(optTime);
            //结束时间为开始时间的23:59:59
            Date endTime = DateUtil.getMaxTimeByDay(optTime);
            newRemindTask.setEndTime(endTime);
            newRemindTask.setRemindMethod(remindMethods);
            if (GeneralTool.isEmpty(remindTaskUpdateVo.getFkDbName())) {
                newRemindTask.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
            }
            if (GeneralTool.isEmpty(remindTaskUpdateVo.getFkTableName())) {
                newRemindTask.setFkTableName(TableEnum.REMIND_TASK.key);
            }
            utilService.updateUserInfoToEntity(newRemindTask);
            remindTaskMapper.insert(newRemindTask);
            //可能会有多个提醒
            for (String remindMethod : remindMethodList) {
                remindTaskQueue = new RemindTaskQueue();
                remindTaskQueue.setRemindMethod(remindMethod);
                remindTaskQueue.setTryTimes(0);
                remindTaskQueue.setFkRemindTaskId(newRemindTask.getId());
                remindTaskQueue.setOptTime(optTime);
                utilService.updateUserInfoToEntity(remindTaskQueue);
                remindTaskQueueService.add(remindTaskQueue);
            }
        }
    }




    /**
     * @Description: 批量新增任务
     * @Author: Jerry
     * @Date:18:31 2021/11/25
     */
    @Transactional(rollbackFor = Exception.class)
    public void addBatchRemindTaskQueue(RemindTask remindTask) {
        List<Date> datas = new ArrayList<>();
//        Date startTime = new Date();
//        Calendar instance = Calendar.getInstance();
//        instance.setTime(startTime);
//        instance.add(instance.MINUTE,3);
        Date startTime = remindTask.getStartTime();
        Date endTime = remindTask.getEndTime();
        if (GeneralTool.isNotEmpty(startTime)) {
            datas.add(startTime);
        }
        if (GeneralTool.isNotEmpty(endTime)) {
            datas.add(endTime);
        }
        RemindTaskQueue remindTaskQueue = null;
        Long fkRemindTaskId = remindTask.getId();
        String remindMethods = remindTask.getRemindMethod();
        String[] remindMethodList = remindMethods.split(",");
        //新增任务到提醒任务执行队列表
        for (Date optTime : datas) {
            //可能会有多个提醒
            for (String remindMethod : remindMethodList) {
                remindTaskQueue = new RemindTaskQueue();
                remindTaskQueue.setRemindMethod(remindMethod);
                remindTaskQueue.setTryTimes(0);
                remindTaskQueue.setFkRemindTaskId(fkRemindTaskId);
                remindTaskQueue.setOptTime(optTime);
                if (GeneralTool.isNotEmpty(SecureUtil.getStaffInfo())) {
                    utilService.updateUserInfoToEntity(remindTaskQueue);
                } else {
                    remindTaskQueue.setGmtCreateUser("admin");
                    remindTaskQueue.setGmtCreate(new Date());
                }
                remindTaskQueueService.add(remindTaskQueue);
            }
        }
    }

    /**
     * @Description: 数组日期转成字符串日期
     * @Author: Jerry
     * @Date:10:29 2021/11/24
     */
    private void dealListDayToString(RemindTask remindTask, RemindTaskUpdateDto remindTaskUpdateVo) {
        String[] loopWeekDays = remindTaskUpdateVo.getLoopWeekDaysList();
        String[] loopMonthDays = remindTaskUpdateVo.getLoopMonthDaysList();
        if (GeneralTool.isNotEmpty(loopWeekDays)) {
            StringJoiner sj = new StringJoiner(",");
            for (String loopWeekDay : loopWeekDays) {
                sj.add(loopWeekDay);
            }
            remindTask.setLoopWeekDays(sj.toString());
        } else {
            remindTask.setLoopWeekDays(null);
        }
        if (GeneralTool.isNotEmpty(loopMonthDays)) {
            StringJoiner sj = new StringJoiner(",");
            for (String loopMonthDay : loopMonthDays) {
                sj.add(loopMonthDay);
            }
            remindTask.setLoopMonthDays(sj.toString());
        } else {
            remindTask.setLoopMonthDays(null);
        }
    }


    /**
     * @Description: 字符串日期转数组日期
     * @Author: Jerry
     * @Date:10:37 2021/11/24
     */
    private void dealStringToListDay(RemindTaskVo remindTaskDto) {
        String loopWeekDays = remindTaskDto.getLoopWeekDays();
        String loopMonthDays = remindTaskDto.getLoopMonthDays();
        if (GeneralTool.isNotEmpty(loopWeekDays)) {
            String[] loopWeekDaysList = loopWeekDays.split(",");
            List<String> list = new ArrayList<>();
            for (String s : loopWeekDaysList) {
                list.add(s);
            }
            remindTaskDto.setLoopWeekDaysList(list);
        } else {
            remindTaskDto.setLoopWeekDaysList(new ArrayList<>());
        }
        if (GeneralTool.isNotEmpty(loopMonthDays)) {
            String[] loopMonthDaysList = loopMonthDays.split(",");
            List<String> list = new ArrayList<>();
            for (String s : loopMonthDaysList) {
                list.add(s);
            }
            remindTaskDto.setLoopMonthDaysList(list);
        } else {
            remindTaskDto.setLoopMonthDaysList(new ArrayList<>());
        }
    }


    /**
     * @Description: 判断是否是周期
     * @Author: Jerry
     * @Date:11:07 2021/11/24
     */
    private void isCycle(RemindTaskVo remindTaskDto) {
        boolean isCycle = false;
        if (GeneralTool.isNotEmpty(remindTaskDto.getLoopIntervalDays()) || GeneralTool.isNotEmpty(remindTaskDto.getLoopWeekDaysList())
                || GeneralTool.isNotEmpty(remindTaskDto.getLoopMonthDaysList())) {
            isCycle = true;
        }
        remindTaskDto.setCycle(isCycle);
    }


    /**
     * @Description: 将提醒方法字符串转成数组
     * @Author: Jerry
     * @Date:14:25 2021/11/25
     */
    private void remindMethodToRemindMethodList(RemindTaskVo remindTaskDto) {
        String remindMethod = remindTaskDto.getRemindMethod();
        if (GeneralTool.isNotEmpty(remindMethod)) {
            String[] split = remindMethod.split(",");
            List<String> remindMethodList = new ArrayList<String>();
            for (String s : split) {
                remindMethodList.add(s);
            }
            remindTaskDto.setRemindMethodList(remindMethodList);
            if (remindMethodList.contains("0")) {
                remindTaskDto.setRemind(false);
            } else {
                remindTaskDto.setRemind(true);
            }
        }
    }


    /**
     * @Description: 提醒方法数组转字符串
     * @Author: Jerry
     * @Date:14:29 2021/11/25
     */
    private String remindMethodToRemindMethodList(RemindTaskUpdateDto remindTaskUpdateVo) {
        String[] remindMethod = remindTaskUpdateVo.getRemindMethod();
        StringJoiner sj = new StringJoiner(",");
        for (String s : remindMethod) {
            sj.add(s);
        }
        return sj.toString();
    }


    /**
     * @Description: 获取执行时间
     * @Author: Jerry
     * @Date:10:13 2021/11/29
     */
    private List<Date> getListDates(RemindTask remindTask) {
        List<Date> datas = new ArrayList<>();
        Date startTime = remindTask.getStartTime();
        Date endTime = remindTask.getEndTime();
        if (GeneralTool.isNotEmpty(remindTask.getAdvanceDays())) {
            //提前天数提醒
            String advanceDays = remindTask.getAdvanceDays();
            String[] advanceDaysList = advanceDays.split(",");
            for (String advanceDay : advanceDaysList) {
                Date advanceDateByDay = GetDateUtil.getAdvanceDateByDay(startTime, Long.valueOf(advanceDay));
                datas.add(advanceDateByDay);
            }
        } else if (GeneralTool.isNotEmpty(remindTask.getAdvanceHours())) {
            //提前小时提醒
            String advanceHours = remindTask.getAdvanceHours();
            String[] advanceHoursList = advanceHours.split(",");
            for (String advanceHour : advanceHoursList) {
                Date advanceDateByStrHour = GetDateUtil.getAdvanceDateByStrHour(startTime, advanceHour);
                datas.add(advanceDateByStrHour);
            }
        } else if (GeneralTool.isNotEmpty(remindTask.getLoopIntervalDays())) {
            //循环间隔日提醒
            Integer loopIntervalDays = remindTask.getLoopIntervalDays();
            List<Date> datesBetweenStartAndEndByDay = GetDateUtil.getDatesBetweenStartAndEndByDay(startTime, endTime, loopIntervalDays, new Date());
            datas.addAll(datesBetweenStartAndEndByDay);
        } else if (GeneralTool.isNotEmpty(remindTask.getLoopWeekDays())) {
            //循环周日期提醒
            String loopWeekDays = remindTask.getLoopWeekDays();
            String[] loopWeekDaysList = loopWeekDays.split(",");
            for (String loopWeekDay : loopWeekDaysList) {
                List<Date> datesBetweenStartAndEndByWeek = GetDateUtil.getDatesBetweenStartAndEndByWeek(startTime, endTime, Integer.valueOf(loopWeekDay), new Date());
                datas.addAll(datesBetweenStartAndEndByWeek);
            }
        } else if (GeneralTool.isNotEmpty(remindTask.getLoopMonthDays())) {
            //循环月日期提醒
            String loopMonthDays = remindTask.getLoopMonthDays();
            String[] loopMonthDaysList = loopMonthDays.split(",");
            for (String loopMonthDay : loopMonthDaysList) {
                List<Date> datesBetweenStartAndEndByMonth = GetDateUtil.getDatesBetweenStartAndEndByMonth(startTime, endTime, Integer.valueOf(loopMonthDay), new Date());
                datas.addAll(datesBetweenStartAndEndByMonth);
            }
        } else {
            //都为空，则直接取开始时间
            datas.add(startTime);
        }

        //任务总数量
        int dataSize = datas.size();
        //获取最大任务数限制，如果超过这个任务数则返回提示
        String reMinderTaskMax = "";
        Result<String> stringResult = permissionCenterClient.getConfigValueByConfigKey("REMINDER_TASK_SPLIT_LIMIT");
        if (stringResult.isSuccess() && GeneralTool.isNotEmpty(stringResult.getData())) {
            reMinderTaskMax = stringResult.getData();
        }
        if (GeneralTool.isNotEmpty(reMinderTaskMax) && Integer.valueOf(reMinderTaskMax) < dataSize) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("remind_task_max") + ":" + reMinderTaskMax);
        }
        return datas;
    }
}

#服务器端口
server:
  port: 1198


#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: platformdb
      datasource:
        platformdb:
          url: ${get.datasource.test.platformdb.url}
          username: ${get.datasource.test.platformdb.username}
          password: ${get.datasource.test.platformdb.password}
        registrationdb:
          url: ${get.datasource.test.registrationdb.url}
          username: ${get.datasource.test.registrationdb.username}
          password: ${get.datasource.test.registrationdb.password}
        appmsodb:
          url: ${get.datasource.test.appmsodb.url}
          username: ${get.datasource.test.appmsodb.username}
          password: ${get.datasource.test.appmsodb.password}
        appmsologdb:
          url: ${get.datasource.test.appmsologdb.url}
          username: ${get.datasource.test.appmsologdb.username}
          password: ${get.datasource.test.appmsologdb.password}
        aisfiledb:
          url: ${get.datasource.test.aisfiledb.url}
          username: ${get.datasource.test.aisfiledb.username}
          password: ${get.datasource.test.aisfiledb.password}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.InstitutionCenterMapper">


    <select id="getRegionList" resultType="com.get.partnercenter.vo.ReginVo">
        select id       as regionId,
               name     as regionName,
               name_chn as regionNameChn
        from u_area_region
        where fk_area_country_id = 3
        order by view_order desc
    </select>

    <select id="getCountryNameByIds" resultType="java.lang.String">
        SELECT GROUP_CONCAT(u.name_chn) AS countyName from u_area_country u
        where u.id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getRegionNames" resultType="com.get.partnercenter.vo.AgentAccountVo">
        SELECT GROUP_CONCAT(DISTINCT uar.name_chn ) AS area_region_name_chn,
               GROUP_CONCAT(DISTINCT uar.NAME )     AS area_region_name
        FROM u_area_region uar
        WHERE FIND_IN_SET(uar.id, #{areaRegionId}) > 0
    </select>
</mapper>

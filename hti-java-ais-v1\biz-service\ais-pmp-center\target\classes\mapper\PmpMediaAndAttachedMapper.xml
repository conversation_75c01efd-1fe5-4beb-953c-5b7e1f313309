<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.PmpMediaAndAttachedMapper">
    <update id="updateFileName">
        UPDATE ais_file_center.m_file_pmp
        SET file_name = CONCAT(#{fileName}, file_type_orc)
        WHERE file_guid = #{fileGuid}
    </update>

    <select id="selectMediaList" resultType="com.get.pmpcenter.vo.common.MediaVo">
        select m.id,
        m.fk_file_guid as fileGuid,
        m.link,
        m.remark,
        m.gmt_create,
        f.file_type_orc,
        f.file_name_orc,
        f.file_name,
        f.file_path,
        f.file_key,
        m.fk_table_name as table_name,
        m.fk_table_id as table_id
        from s_media_and_attached m
        left join ais_file_center.m_file_pmp f on m.fk_file_guid = f.file_guid
        where m.fk_table_name = #{tableName}
        and m.fk_table_id in
        <foreach item="tableId" collection="tableIds" open="(" separator="," close=")">
            #{tableId}
        </foreach>
        order by m.id desc
    </select>

    <select id="selectMedia" resultType="com.get.pmpcenter.vo.common.MediaVo">
        select m.id,
               m.fk_file_guid  as fileGuid,
               m.link,
               m.remark,
               m.gmt_create,
               f.file_type_orc,
               f.file_name_orc,
               f.file_name,
               f.file_path,
               f.file_key,
               m.fk_table_name as tableName,
               m.fk_table_id   as tableId
        from s_media_and_attached m
                 inner join ais_file_center.m_file_pmp f on m.fk_file_guid = f.file_guid
        where m.id = #{id}
    </select>
</mapper>
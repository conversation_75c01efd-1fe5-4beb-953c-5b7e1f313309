<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.resumecenter.dao.ResumeIntentionMapper">
  <resultMap id="BaseResultMap" type="com.get.resumecenter.entity.ResumeIntention">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_resume_id" jdbcType="BIGINT" property="fkResumeId" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="salary" jdbcType="DECIMAL" property="salary" />
    <result column="work_place" jdbcType="VARCHAR" property="workPlace" />
    <result column="take_office" jdbcType="TIMESTAMP" property="takeOffice" />
    <result column="self_evaluation" jdbcType="VARCHAR" property="selfEvaluation" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_resume_id, position, salary, work_place, take_office, self_evaluation, gmt_create, 
    gmt_create_user, gmt_modified, gmt_modified_user
  </sql>

  <insert id="insertSelective" parameterType="com.get.resumecenter.entity.ResumeIntention" keyProperty="id" useGeneratedKeys="true">
    insert into m_resume_intention
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkResumeId != null">
        fk_resume_id,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="salary != null">
        salary,
      </if>
      <if test="workPlace != null">
        work_place,
      </if>
      <if test="takeOffice != null">
        take_office,
      </if>
      <if test="selfEvaluation != null">
        self_evaluation,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkResumeId != null">
        #{fkResumeId,jdbcType=BIGINT},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="salary != null">
        #{salary,jdbcType=DECIMAL},
      </if>
      <if test="workPlace != null">
        #{workPlace,jdbcType=VARCHAR},
      </if>
      <if test="takeOffice != null">
        #{takeOffice,jdbcType=TIMESTAMP},
      </if>
      <if test="selfEvaluation != null">
        #{selfEvaluation,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
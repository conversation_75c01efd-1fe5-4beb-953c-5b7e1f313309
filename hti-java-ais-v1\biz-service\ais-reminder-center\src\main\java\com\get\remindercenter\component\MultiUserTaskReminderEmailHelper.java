package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.officecenter.vo.CustomTaskVo;
import com.get.officecenter.vo.TaskItemVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.MultiUserTaskReminderEmailDto;
import com.get.remindercenter.dto.StudyPlanSameSchoolCourseReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component("multiUserTaskReminderEmailHelper")
@Slf4j
public class MultiUserTaskReminderEmailHelper extends EmailAbstractHelper{

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;


    @Resource
    private IOfficeCenterClient  officeCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;



    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        //是否发送邮件
        EmailSenderQueue emailSenderQueueNew = emailSenderQueueMapper.selectById(emailSenderQueue.getId());
        if (emailSenderQueueNew.getOperationStatus() != 1) {
            log.info("邮件已到MQ，无需重复发送");
            return;
        }

        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            MultiUserTaskReminderEmailDto multiUserTaskReminderEmailDto = assembleEmailData(emailSenderQueue);
            String template = setEmailTemplate(multiUserTaskReminderEmailDto);
            if (GeneralTool.isEmpty(template)) {
                log.error("邮箱模板内容为空，需要配置邮箱模板");
                throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
            }
            StringJoiner emailsCombined = new StringJoiner(", ");
            //根据发送人id获取发送人邮箱
            Map<Long, Staff> data = permissionCenterClient.getStaffMapByStaffIds(multiUserTaskReminderEmailDto.getStaffEmailSet()).getData();
                for (Long id : multiUserTaskReminderEmailDto.getStaffEmailSet()) {
                    try {
                        EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                        emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
                        emailSystemMQMessageDto.setTitle(multiUserTaskReminderEmailDto.getEmailTitle());
                        emailSystemMQMessageDto.setContent(template);
                        emailSystemMQMessageDto.setToEmail(data.get(id).getEmail());
                        //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                        //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                        remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                        if (GeneralTool.isNotEmpty(data.get(id).getEmail())) {
                            emailsCombined.add(data.get(id).getEmail());
                        }
                    } catch (Exception e) {
                        // 记录发送失败的邮箱
                        String failedEmail = data.get(id) != null && data.get(id).getEmail() != null ? data.get(id).getEmail() : "staffId:" + id;
                        failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                        log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}", id, failedEmail, e.getMessage());
                    }
                }

            emailSenderQueue.setEmailTo(emailsCombined.toString());
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        }catch (Exception e){
            log.error("multiUserTaskReminderEmailHelper error:{}", e);
            failedEmails.add("邮件发送异常"+e.getMessage());
            emailSenderQueue.setErrorMessage(failedEmails.toString());
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    @Override
    public MultiUserTaskReminderEmailDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        MultiUserTaskReminderEmailDto reminderDto = new MultiUserTaskReminderEmailDto();
        CustomTaskVo taskVo = null;
        List<Long> taskItems =null;
        List<Long> taskItemsUnfinished = null;
        TaskItem taskItem = null;
        if (GeneralTool.isNotEmpty(emailSenderQueue.getFkEmailTypeKey())&&emailSenderQueue.getFkEmailTypeKey().equals(EmailTemplateEnum.MULTI_USER_TASK_REMINDER.getEmailTemplateKey())) { //多人任务
            //获取任务内容
             taskVo = officeCenterClient.getTask(emailSenderQueue.getFkTableId()).getData();
            //获取任务下的子任务的接收人
            taskItems = officeCenterClient.getTaskItemIds(emailSenderQueue.getFkTableId()).getData();
            //获取任务下的未完成的接收人id
             taskItemsUnfinished = officeCenterClient.getUnfinishedTaskItemReceiver(emailSenderQueue.getFkTableId()).getData();
        }else if(GeneralTool.isNotEmpty(emailSenderQueue.getFkEmailTypeKey())&&emailSenderQueue.getFkEmailTypeKey().equals(EmailTemplateEnum.NEW_MULTI_USER_TASK_REMINDER.getEmailTemplateKey())){
            //taskItem = officeCenterClient.getTaskItem(emailSenderQueue.getFkTableId()).getData();
            taskVo = officeCenterClient.getTask(emailSenderQueue.getFkTableId()).getData();
        }

        //根据任务的委派人获取用户信息
        StaffVo staffVo = new StaffVo();
        Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(taskVo.getFkStaffIdFrom());
        if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
            staffVo = staffDtoResult.getData();
        }
        //根据创建人的公司区分中英文
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
        //设置标题
        String title1 = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if(!versionValue2.equals("en")){
            if (GeneralTool.isNotEmpty(taskVo.getFkStaffIdTo())&&taskVo.getFkStaffIdTo()==-1) {
                title1 = "多人任务提醒："+staffVo.getFullName()+"委派多人任务，【截止时间："+sdf.format(taskVo.getTaskDeadline())+"】";
            }else {
                title1 = "单人任务提醒："+staffVo.getFullName()+"委派单人任务，【截止时间："+sdf.format(taskVo.getTaskDeadline())+"】";
            }
        }else {

            if (GeneralTool.isNotEmpty(taskVo.getFkStaffIdTo())&&taskVo.getFkStaffIdTo()==-1) {
                title1  = "Multi Person Task Reminder："+staffVo.getName()+"Delegate multi person tasks,【Deadline："+sdf.format(taskVo.getTaskDeadline())+"】";
            }else {
                title1  = "Single Task Reminder："+staffVo.getName()+"Delegate single person tasks,【Deadline："+sdf.format(taskVo.getTaskDeadline())+"】";
            }
        }
        //获取需要发送的人，并且去重
        Set<Long> staffIdList = new HashSet<>();
        if(GeneralTool.isNotEmpty(emailSenderQueue.getFkEmailTypeKey())&&emailSenderQueue.getFkEmailTypeKey().equals(EmailTemplateEnum.MULTI_USER_TASK_REMINDER.getEmailTemplateKey())){
            //任务截止到期时候发送
            if(GeneralTool.isNotEmpty(taskVo.getFkStaffIdTo())&&taskVo.getFkStaffIdTo()==-1){
                if (GeneralTool.isNotEmpty(taskVo.getTaskDeadline())&&taskVo.getTaskDeadline().equals(reminderDto.getOperationTime())) {
                    //发送未完成任务的发送人
                    staffIdList = taskItemsUnfinished.stream().collect(Collectors.toSet());
                }else { //当前发
                    staffIdList =  taskItems.stream().collect(Collectors.toSet());
                }
            }else {
                staffIdList.add(taskVo.getFkStaffIdTo());
            }
        }else if(GeneralTool.isNotEmpty(emailSenderQueue.getFkEmailTypeKey())&&emailSenderQueue.getFkEmailTypeKey().equals(EmailTemplateEnum.NEW_MULTI_USER_TASK_REMINDER.getEmailTemplateKey())){
            String id = emailSenderQueue.getEmailParameter();
            staffIdList.add(Long.valueOf(id));
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        reminderDto.setStaffEmailSet(staffIdList);
        reminderDto.setLanguageCode(versionValue2);
        reminderDto.setEmailTitle(title1);
        Map<String, String> map = new HashMap<>();
        map.put("taskDescription",taskVo.getTaskDescription());
        map.put("taskDeadline",sdf.format(taskVo.getTaskDeadline()));
        map.put("createUser",taskVo.getGmtCreateUser());
        map.put("createTime",format.format(taskVo.getGmtCreate()));
        reminderDto.setMap(map);
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
        return reminderDto;
    }

    /**
     * 设置模板
     * @param reminderDto
     * @return
     */
    private String setEmailTemplate(MultiUserTaskReminderEmailDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.MULTI_USER_TASK_REMINDER.getEmailTemplateKey()));
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }
}

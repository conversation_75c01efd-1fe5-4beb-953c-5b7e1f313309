<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanTerritoryMapper">

    <select id="selectPlansByTerritoryId" resultType="java.lang.Long">
        SELECT DISTINCT p.id
        FROM m_institution_provider_commission_plan p
                 LEFT JOIN r_institution_provider_commission_plan_territory t
                           ON p.id = t.fk_institution_provider_commission_plan_id
        WHERE (
                  -- 规则 1：无记录，全球适用
                  NOT EXISTS (SELECT 1
                              FROM r_institution_provider_commission_plan_territory t1
                              WHERE t1.fk_institution_provider_commission_plan_id = p.id)
                      OR
                      -- 规则 2：存在 is_include = 3 或 4，为全球适用
                  EXISTS (SELECT 1
                          FROM r_institution_provider_commission_plan_territory t2
                          WHERE t2.fk_institution_provider_commission_plan_id = p.id
                            AND t2.is_include IN (3, 4))
                      OR
                      -- 规则 3：is_include 为 1 或 2，且国家ID匹配
                  EXISTS (SELECT 1
                          FROM r_institution_provider_commission_plan_territory t3
                          WHERE t3.fk_institution_provider_commission_plan_id = p.id
                            AND t3.is_include IN (1, 2)
                            AND t3.fk_area_country_id = #{countryId})
                      OR
                      -- 规则 4：is_include = -1，但该国家不在排除列表中
                  EXISTS (SELECT 1
                          FROM r_institution_provider_commission_plan_territory t4
                          WHERE t4.fk_institution_provider_commission_plan_id = p.id
                            AND t4.is_include = -1)
                      AND NOT EXISTS (SELECT 1
                                      FROM r_institution_provider_commission_plan_territory t5
                                      WHERE t5.fk_institution_provider_commission_plan_id = p.id
                                        AND t5.is_include = -1
                                        AND t5.fk_area_country_id = #{countryId})
                  )
    </select>
</mapper>
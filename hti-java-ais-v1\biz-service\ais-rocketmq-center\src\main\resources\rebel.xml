<?xml version="1.0" encoding="UTF-8"?>

<!--
  This is the JRebel configuration file. It maps the running application to your IDE workspace, enabling JRebel reloading for this project.
  Refer to https://manuals.jrebel.com/jrebel/standalone/config.html for more information.
-->
<application generated-by="intellij" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.zeroturnaround.com" xsi:schemaLocation="http://www.zeroturnaround.com http://update.zeroturnaround.com/jrebel/rebel-2_3.xsd">

	<id>ais-rocketmq-center</id>

	<classpath>
		<dir name="E:/ideaworkspace-work-1/hti-java-ais-v1/biz-service/ais-rocketmq-center/target/classes">
		</dir>
	</classpath>

</application>

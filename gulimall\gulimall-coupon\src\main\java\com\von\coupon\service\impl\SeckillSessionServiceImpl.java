package com.von.coupon.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.von.common.utils.PageUtils;
import com.von.common.utils.Query;
import com.von.common.utils.TimeUtils;
import com.von.coupon.dao.SeckillSessionDao;
import com.von.coupon.entity.SeckillSessionEntity;
import com.von.coupon.entity.SeckillSkuRelationEntity;
import com.von.coupon.service.SeckillSessionService;
import com.von.coupon.service.SeckillSkuRelationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("seckillSessionService")
@RequiredArgsConstructor
public class SeckillSessionServiceImpl extends ServiceImpl<SeckillSessionDao, SeckillSessionEntity> implements SeckillSessionService {

    private final SeckillSkuRelationService seckillSkuRelationService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<SeckillSessionEntity> page = this.page(
                new Query<SeckillSessionEntity>().getPage(params),
                new QueryWrapper<SeckillSessionEntity>()
        );

        return new PageUtils(page);
    }

    /**
     * 获取最新3天的活动场次和 sku数据
     *
     * @return
     */
    @Override
    public List<SeckillSessionEntity> getLatest3DaySessionAndSku() {
        // 获取最新3天的活动场次
        List<SeckillSessionEntity> seckillSessionEntityList = this.getLatest3DaySession();
        if (CollectionUtil.isEmpty(seckillSessionEntityList)) {
            return null;
        }

        // 根据场次获取关联的sku数据Map
        Map<Long, List<SeckillSkuRelationEntity>> seckillSkuRelationMap = this.getSeckillSkuRelationMapBySeckillSessions(seckillSessionEntityList);
        if (CollectionUtil.isEmpty(seckillSkuRelationMap)) {
            return null;
        }

        // 设置关联sku数据
        for (SeckillSessionEntity seckillSessionEntity : seckillSessionEntityList) {
            Long seckillSessionId = seckillSessionEntity.getId();
            seckillSessionEntity.setRelationSkus(seckillSkuRelationMap.get(seckillSessionId));
        }

        return seckillSessionEntityList;
    }

    private List<SeckillSessionEntity> getLatest3DaySession() {
        QueryWrapper<SeckillSessionEntity> seckillSessionEntityQueryWrapper = new QueryWrapper<SeckillSessionEntity>().between("start_time", TimeUtils.getNowBeginTime().toString(), TimeUtils.get2DayEndTime().toString());
        List<SeckillSessionEntity> seckillSessionEntityList = this.list(seckillSessionEntityQueryWrapper);
        return seckillSessionEntityList;
    }

    private Map<Long, List<SeckillSkuRelationEntity>> getSeckillSkuRelationMapBySeckillSessions(List<SeckillSessionEntity> seckillSessionEntityList) {
        // 根据id批量查询关联sku数据
        List<Long> seckillSessionIds = seckillSessionEntityList.stream().map(SeckillSessionEntity::getId).collect(Collectors.toList());
        QueryWrapper<SeckillSkuRelationEntity> seckillSkuRelationEntityQueryWrapper = new QueryWrapper<SeckillSkuRelationEntity>().in("promotion_session_id", seckillSessionIds);
        List<SeckillSkuRelationEntity> seckillSkuRelationEntities = this.seckillSkuRelationService.list(seckillSkuRelationEntityQueryWrapper);
        if (CollectionUtil.isEmpty(seckillSkuRelationEntities)) {
            return null;
        }

        // 构造返回结果
        Map<Long, List<SeckillSkuRelationEntity>> seckillSkuRelationMap = seckillSkuRelationEntities.stream()
                .collect(Collectors.groupingBy(SeckillSkuRelationEntity::getPromotionSessionId));
        return seckillSkuRelationMap;
    }


}
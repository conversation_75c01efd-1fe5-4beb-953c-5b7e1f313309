package com.get.aisplatformcenter.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.aisplatformcenter.service.ULiveTypeService;
import com.get.aisplatformcenterap.entity.ULiveTypeEntity;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "后台管理-直播类型管理")
@RestController
@RequestMapping("platform/ulivetype")
public class ULiveTypeController {
    @Autowired
    private ULiveTypeService uliveTypeService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/直播类型管理/列表查询")
    @PostMapping("searchList")
    public ResponseBo<ULiveTypeEntity> searchList(@RequestBody ULiveTypeEntity dto){
        List<ULiveTypeEntity> datas=uliveTypeService.list(new LambdaQueryWrapper<ULiveTypeEntity>());
        return new ResponseBo(datas);
    }

}

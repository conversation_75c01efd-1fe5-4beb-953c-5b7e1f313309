package com.von.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @Date 2024/11/27 20:28
 * @Version 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "thread")
public class ThreadPoolConfigProperties {

   private Integer coreSize;

   private Integer maxSize;

   private Integer keepAliveTime;

}

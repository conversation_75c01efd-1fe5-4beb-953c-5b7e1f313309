#服务器端口
server:
  port: 1198


#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: platformdb
      datasource:
        platformdb:
          url: ${get.datasource.dev.platformdb.url}
          username: ${get.datasource.dev.platformdb.username}
          password: ${get.datasource.dev.platformdb.password}
        registrationdb:
          url: ${get.datasource.dev.registrationdb.url}
          username: ${get.datasource.dev.registrationdb.username}
          password: ${get.datasource.dev.registrationdb.password}
        appmsodb:
          url: ${get.datasource.dev.appmsodb.url}
          username: ${get.datasource.dev.appmsodb.username}
          password: ${get.datasource.dev.appmsodb.password}
        appmsologdb:
          url: ${get.datasource.dev.appmsologdb.url}
          username: ${get.datasource.dev.appmsologdb.username}
          password: ${get.datasource.dev.appmsologdb.password}
        aisfiledb:
          url: ${get.datasource.dev.aisfiledb.url}
          username: ${get.datasource.dev.aisfiledb.username}
          password: ${get.datasource.dev.aisfiledb.password}
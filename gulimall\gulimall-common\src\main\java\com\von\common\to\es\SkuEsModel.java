package com.von.common.to.es;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SkuEsModel implements Serializable {

    private static final long serialVersionUID = 1L;

	private Long skuId;

	private Long spuId;

	private String skuTitle;

	private BigDecimal skuPrice;

	private String skuImg;

	private Long saleCount;

	private Boolean hasStock;

	private Long hotScore;

	private Long brandId;

	private Long catalogId;

	private String brandName;

	private String brandImg;

	private String catalogName;

    // TODO 按照视频教程写的, 不规范
	private List<Attrs> attrs;

    /**
     *  检索属性
     */
    @Data
    public static class Attrs implements Serializable{

        private static final long serialVersionUID = 1L;

        private Long attrId;

        private String attrName;

        private String attrValue;

    }

}

package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.ResumeTypeVo;
import com.get.resumecenter.entity.ResumeType;
import com.get.resumecenter.service.IResumeTypeService;
import com.get.resumecenter.dto.ResumeTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/31
 * @TIME: 10:47
 * @Description:
 **/
@Api(tags = "简历类型管理")
@RestController
@RequestMapping("resume/resumetype")
public class ResumeTypeController {
    @Resource
    IResumeTypeService resumeTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.resumecenter.vo.ResumeTypeVo>
     * @Description: 列表数据
     * @Param [resumeTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.LIST, description = "人才中心/简历类型管理/查询简历类型")
    @PostMapping("datas")
    public ResponseBo<ResumeTypeVo> datas(@RequestBody ResumeTypeDto resumeTypeDto) {
        List<ResumeTypeVo> datas = resumeTypeService.datas(resumeTypeDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/简历类型管理/简历类型详情")
    @GetMapping("/{id}")
    public ResponseBo<ResumeTypeVo> detail(@PathVariable("id") Long id) {
        ResumeTypeVo data = resumeTypeService.findResumeTypeById(id);
        ResumeTypeVo resumeTypeVo = BeanCopyUtils.objClone(data, ResumeTypeVo::new);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", resumeTypeVo);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param resumeTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历类型管理/新增简历类型")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ResumeTypeDto.Add.class) ResumeTypeDto resumeTypeDto) {
        return SaveResponseBo.ok(this.resumeTypeService.addResumeType(resumeTypeDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "人才中心/简历类型管理/删除简历类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.resumeTypeService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param resumeTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/简历类型管理/更新简历类型")
    @PostMapping("update")
    public ResponseBo<ResumeTypeVo> update(@RequestBody @Validated(ResumeTypeDto.Update.class) ResumeTypeDto resumeTypeDto) {
        return UpdateResponseBo.ok(resumeTypeService.updateResumeType(resumeTypeDto));
    }


    /**
     * 批量新增信息
     *
     * @param resumeTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历类型管理/批量保存简历类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(ResumeTypeDto.Add.class) ValidList<ResumeTypeDto> resumeTypeDtos) {
        resumeTypeService.batchAdd(resumeTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "简历类型下拉", notes = "")
    @GetMapping("getResumeTypeSelect")
    public ResponseBo<BaseSelectEntity> getResumeTypeSelect() {
        return new ListResponseBo<>(resumeTypeService.getResumeTypeSelect());
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [studentOfferItemStepVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/技能类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ResumeTypeDto> resumeTypeDtos) {
        resumeTypeService.movingOrder(resumeTypeDtos);
        return ResponseBo.ok();
    }

}

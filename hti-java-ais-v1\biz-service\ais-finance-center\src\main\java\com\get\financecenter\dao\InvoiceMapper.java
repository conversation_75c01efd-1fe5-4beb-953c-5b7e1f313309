package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.vo.InvoiceReceivablePlanVo;
import com.get.financecenter.vo.InvoiceSelectVo;
import com.get.financecenter.entity.Invoice;
import com.get.financecenter.dto.query.InvoiceQueryDto;
import com.get.salecenter.vo.ReceivablePlanVo;
import com.get.salecenter.dto.InvoiceReceiptFormDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/16 17:05
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface InvoiceMapper extends BaseMapper<Invoice> {

    /**
     * 根据发票ID获取应收计划ids
     *
     *
     * @param pages
     * @param fkInvoiceId
     * @return
     */
    List<Long> getReceivablePlanIds(IPage<Long> pages, @Param("fkInvoiceId") Long fkInvoiceId);


    List<Invoice> getInvoiceList(IPage<Invoice> pages, @Param("invoiceDto") InvoiceQueryDto invoiceVo);

    List<ReceivablePlanVo> getReceivablePlanByInvoice(IPage<Long> pages, @Param("fkInvoiceId") Long fkInvoiceId);
    /**
     * 根据应收计划Ids获取发票
     *
     * @param planIds
     * @return
     */
    List<InvoiceReceivablePlanVo> getFkInvoiceNum(@Param("planIds") Set<Long> planIds);


    /**
     * 根据提供商id获取对应发票下拉接口
     *
     * @param fkTypeTargetId
     * @param fkTypeKey
     * @return
     */
    List<InvoiceSelectVo> getInvoiceSelectByTargetId(@Param("fkTypeTargetId") Long fkTypeTargetId, @Param("fkTypeKey") String fkTypeKey, @Param("fkReceiptFormId") Long fkReceiptFormId);


    /**
     * 发票下拉接口
     * @return
     */
    List<InvoiceSelectVo> getInvoiceSelect(@Param("invoiceReceiptFormDto") InvoiceReceiptFormDto invoiceReceiptFormDto);

    List<InvoiceSelectVo> getAllInvoice(@Param("invoiceIds") Set<Long> invoiceIds, @Param("fkCompanyId")Long fkCompanyId);

    /**
     * 根据传入的发票id、获取发票总金额
     */
    BigDecimal getInvoiceTotalAmountByIds(@Param("list")Set<Long> list);

    Integer getMaxPoNum(@Param("num")String num);

    List<Long> getInvoiceIdsByReceivablePlanId(@Param("receivablePlanId")Long receivablePlanId);

    /**
     * 获取最大的当月数量累计
     * @param vouchMonth
     * @return
     */
    int getMaxVouchQty(@Param("vouchMonth") String vouchMonth);

}
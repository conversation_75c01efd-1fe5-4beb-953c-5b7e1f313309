package com.von.ware.listener;

import com.rabbitmq.client.Channel;
import com.von.common.constant.RabbitInfo;
import com.von.common.to.mq.OrderTo;
import com.von.common.to.mq.StockLockedTo;
import com.von.ware.service.WareSkuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @Date 2024/12/29 22:58
 * @Version 1.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@RabbitListener(queues = RabbitInfo.Stock.releaseQueue)
public class StockReleaseListener {

    private final WareSkuService wareSkuService;

    /**
     * MQ库存自动解锁
     *
     * @param stockLockedTo
     * @param message
     */
    @RabbitHandler
    public void handleStockLockedRelease(StockLockedTo stockLockedTo, Message message, Channel channel) throws IOException {
        log.info("收到解锁库存消息");
        try {
            this.wareSkuService.unlockStock(stockLockedTo);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
        }
    }

    @RabbitHandler
    public void handleOrderCLoseRelease(OrderTo orderTo, Message message, Channel channel) throws IOException {
        log.info("订单关闭, 解锁库存");
        try {
            this.wareSkuService.unlockStock(orderTo);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
        }
    }



}

package com.get.pmpcenter.strategy;

import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/4/27
 * @Version 1.0
 * @apiNote:工作台PMP审批策略接口
 */
public interface WorkbenchApprovalStrategy {

    /**
     * 获取待审批列表
     * @param workbenchApprovalDto
     * @return
     */
    List<WorkbenchApprovalVo> getApprovalList(PmpWorkbenchApprovalDto workbenchApprovalDto);

    /**
     * 获取审批类型
     * @return
     */
    String getApprovalType();
}

package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.financecenter.dao.CurrencyTypeMapper;
import com.get.financecenter.dao.TravelClaimFeeTypeMapper;
import com.get.financecenter.dao.TravelClaimFormItemMapper;
import com.get.financecenter.entity.TravelClaimFeeType;
import com.get.financecenter.entity.TravelClaimFormItem;
import com.get.financecenter.service.TravelClaimFormItemService;
import com.get.financecenter.vo.TravelClaimFormItemVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class TravelClaimFormItemServiceImpl extends BaseServiceImpl<TravelClaimFormItemMapper, TravelClaimFormItem> implements TravelClaimFormItemService {
    @Resource
    private TravelClaimFormItemMapper travelClaimFormItemMapper;
    @Resource
    private TravelClaimFeeTypeMapper travelClaimFeeTypeMapper;
    @Resource
    private CurrencyTypeMapper currencyTypeMapper;

    @Override
    public List<TravelClaimFormItemVo> getDtoByTravelClaimFormId(Long id) {
        List<TravelClaimFormItem> expenseClaimFormItems = this.travelClaimFormItemMapper.selectList(Wrappers.<TravelClaimFormItem>query().lambda().eq(TravelClaimFormItem::getFkTravelClaimFormId, id));

        // 2021/9/16 费用类型名称改为获取Map 一次性sql查询
        List<TravelClaimFormItemVo> convertDatas = new ArrayList<>();
        for (TravelClaimFormItem travelClaimFormItem : expenseClaimFormItems) {
            TravelClaimFormItemVo travelClaimFormItemVo = BeanCopyUtils.objClone(travelClaimFormItem, TravelClaimFormItemVo::new);
            //费用类型名称
            TravelClaimFeeType travelClaimFeeType = travelClaimFeeTypeMapper.selectById(travelClaimFormItem.getFkTravelClaimFeeTypeId());
            travelClaimFormItemVo.setExpenseClaimFeeTypeName(travelClaimFeeType.getTypeName());

            String currencyName = currencyTypeMapper.getCurrencyNameByNum(travelClaimFormItemVo.getFkCurrencyTypeNum());
            travelClaimFormItemVo.setFkCurrencyTypeName(travelClaimFormItemVo.getFkCurrencyTypeNum() + "(" + currencyName + ")");
            convertDatas.add(travelClaimFormItemVo);
        }
        return convertDatas;
    }
}

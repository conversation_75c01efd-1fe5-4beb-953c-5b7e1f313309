package com.get.remindercenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.dao.DefaultSendEmailMapper;
import com.get.remindercenter.entity.DefaultSendEmail;
import com.get.remindercenter.service.DefaultSendEmailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Wrapper;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class DefaultSendEmailServiceImpl implements DefaultSendEmailService {

    @Resource
    private DefaultSendEmailMapper defaultSendEmailMapper;

    /**
     * 获取必发邮箱
     * @return
     */
    @Override
    public List<String> getDefaultSendEmail() {
        List<DefaultSendEmail> defaultSendEmails = defaultSendEmailMapper.selectList(Wrappers.lambdaQuery());
        if (GeneralTool.isNotEmpty(defaultSendEmails)) {
            return defaultSendEmails.stream().map(DefaultSendEmail::getEmail).collect(Collectors.toList());
        }
        return null;
    }

}

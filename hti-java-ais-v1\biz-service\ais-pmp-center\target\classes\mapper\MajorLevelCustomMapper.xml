<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.MajorLevelCustomMapper">

    <select id="selectByIds" resultType="com.get.pmpcenter.entity.MajorLevelCustom">
        SELECT c.* ,
        c.fk_major_level_custom_id_parent AS parentLevelId,
        p.custom_name AS parentLevelName,
        p.custom_name_chn AS parentLevelNameChn
        FROM u_major_level_custom c
        LEFT JOIN u_major_level_custom p ON c.fk_major_level_custom_id_parent = p.id
        WHERE c.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by c.view_order desc,c.gmt_create desc
    </select>
</mapper>
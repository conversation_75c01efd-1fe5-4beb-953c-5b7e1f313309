package com.get.pmpcenter.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.pmpcenter.entity.AgentCommissionMajorLevelCustom;
import com.get.pmpcenter.entity.InstitutionProviderCommissionMajorLevelCustom;
import com.get.pmpcenter.entity.MajorLevelCustom;
import com.get.pmpcenter.mapper.AgentCommissionMajorLevelCustomMapper;
import com.get.pmpcenter.mapper.MajorLevelCustomMapper;
import com.get.pmpcenter.service.AgentCommissionMajorLevelCustomService;
import com.get.pmpcenter.vo.common.CommissionMajorLevelVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionMajorLevelCustomServiceImpl extends ServiceImpl<AgentCommissionMajorLevelCustomMapper, AgentCommissionMajorLevelCustom> implements AgentCommissionMajorLevelCustomService {

    @Autowired
    private AgentCommissionMajorLevelCustomMapper agentCommissionMajorLevelCustomMapper;
    @Autowired
    private MajorLevelCustomMapper customMapper;

    @Override
    public List<CommissionMajorLevelVo> getCommissionMajorLevelList(List<Long> commissionIds) {
        List<CommissionMajorLevelVo> list = new ArrayList<>();
        commissionIds.stream().forEach(commissionId -> {
            List<AgentCommissionMajorLevelCustom> levelCustoms = agentCommissionMajorLevelCustomMapper.selectList(new LambdaQueryWrapper<AgentCommissionMajorLevelCustom>()
                    .eq(AgentCommissionMajorLevelCustom::getFkAgentCommissionId, commissionId));
            if (CollectionUtils.isEmpty(levelCustoms)) {
                return;
            }
            List<MajorLevelCustom> majorLevelCustoms = customMapper.selectByIds(levelCustoms.stream()
                    .map(AgentCommissionMajorLevelCustom::getFkMajorLevelCustomId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(majorLevelCustoms)) {
                List<CommissionMajorLevelVo> levelVoList = majorLevelCustoms.stream().map(majorLevelCustom -> {
                    CommissionMajorLevelVo vo = new CommissionMajorLevelVo();
                    vo.setCustomName(majorLevelCustom.getCustomName());
                    vo.setCustomNameChn(majorLevelCustom.getCustomNameChn());
                    vo.setLevelId(majorLevelCustom.getId());
                    vo.setViewOrder(majorLevelCustom.getViewOrder());
                    vo.setParentLevelId(majorLevelCustom.getParentLevelId());
                    vo.setParentLevelName(majorLevelCustom.getParentLevelName());
                    vo.setParentLevelNameChn(majorLevelCustom.getParentLevelNameChn());
                    vo.setCommissionId(commissionId);
                    return vo;
                }).collect(Collectors.toList());
                list.addAll(levelVoList);
            }
        });
        return list;
    }
}

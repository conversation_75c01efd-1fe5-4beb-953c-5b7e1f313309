package com.von.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * 错误状态美剧
 *
 * <AUTHOR>
 * @Date 2024/10/16 16:07
 * @Version 1.0
 */

@Getter
@ToString
@AllArgsConstructor
public enum BizCodeEnum {

    UNKNOW_EXCEPTION(10000, "系统未知异常"),

    VALID_EXCEPTION(10001, "参数格式校验失败"),

    TO_MANY_REQUEST(10011, "请求流量过大"),

    SMS_CODE_EXCEPTION(10002, "验证码获取频率太高, 稍后再试"),

    PRODUCT_UP_EXCEPTION(11000, "商品上架异常"),

    USER_EXIST_EXCEPTION(15001, "用户已存在"),

    PHONE_EXIST_EXCEPTION(15002, "手机已存在"),

    NO_STOCK_EXCEPTION(21000, "商品库存不足"),

    LOGINACCT_PASSWORD_INVAILD_EXCEPTION(15003, "账号或密码错误")

    ;

    private Integer code;
    private String msg;
    private static final Map<Integer, BizCodeEnum> BIZ_CODE_ENUM_MAP = new HashMap<>();

    static {
        for (BizCodeEnum bizCodeEnum : BizCodeEnum.values()) {
            BIZ_CODE_ENUM_MAP.put(bizCodeEnum.getCode(), bizCodeEnum);
        }
    }

    public static BizCodeEnum valueOf(Integer code) {
        return BIZ_CODE_ENUM_MAP.get(code);
    }

}

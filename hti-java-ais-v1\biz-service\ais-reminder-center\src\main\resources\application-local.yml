#服务器端口
server:
  port: 8093

#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: reminderdb
      datasource:
        reminderdb:
          url: ${get.datasource.test.reminderdb.url}
          username: ${get.datasource.test.reminderdb.username}
          password: ${get.datasource.test.reminderdb.password}
        reminderdb-doris:
          url: ${get.datasource.test.reminderdb-doris.url}
          username: ${get.datasource.test.reminderdb-doris.username}
          password: ${get.datasource.test.reminderdb-doris.password}


  #  mail:
  #    port: 465
  #    host: smtp.exmail.qq.com
  #    username: <EMAIL>
  #    password: GZEtLhkf6WzpcPd9
  #    default-encoding: utf-8
  #    protocol: smtp
  #    properties:
  #      mail:
  #        username: <EMAIL>
  #        smtp:
  #          auth: true
  #          starttls:
  #            enable: true
  #            required: true
  #          ssl:
  #            enable: true
  #            socketFactory:
  #              port: 465
  #              class: javax.net.ssl.SSLSocketFactory
  #    financeMail: <EMAIL>

  mail:
    port: 465
    host: smtp.exmail.qq.com
    username: <EMAIL>
    password: FFpnYPwmAbPo5Xoa
    default-encoding: utf-8
    protocol: smtp
    properties:
      mail:
        username: <EMAIL>
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: true
            socketFactory:
              port: 465
              class: javax.net.ssl.SSLSocketFactory
    financeMail: <EMAIL>



# true 开启 、false 关闭
emailSwitch: ${emailSwitch}
accessKeyId: XXXXX
accessKeySecret: XXXXX

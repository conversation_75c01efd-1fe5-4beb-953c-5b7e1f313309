package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.pmpcenter.dto.common.IdListDto;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.dto.institution.*;
import com.get.pmpcenter.entity.*;
import com.get.pmpcenter.enums.*;
import com.get.pmpcenter.event.publisher.CommissionChangedEventPublisher;
import com.get.pmpcenter.mapper.*;
import com.get.pmpcenter.service.*;
import com.get.pmpcenter.utils.ContractNumberGeneratorUtil;
import com.get.pmpcenter.utils.GeneratorLogsUtil;
import com.get.pmpcenter.vo.common.CountryVo;
import com.get.pmpcenter.vo.common.MediaVo;
import com.get.pmpcenter.vo.common.ProviderInstitutionVo;
import com.get.pmpcenter.vo.common.StaffVo;
import com.get.pmpcenter.vo.institution.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class InstitutionProviderContractServiceImpl extends ServiceImpl<InstitutionProviderContractMapper, InstitutionProviderContract> implements InstitutionProviderContractService {

    @Autowired
    private InstitutionProviderContractMapper institutionProviderContractMapper;
    @Autowired
    private PmpMediaAndAttachedService mediaAndAttachedService;
    @Autowired
    private InstitutionProviderCommissionPlanService commissionPlanService;
    @Autowired
    private PmpContractTypeMapper contractTypeMapper;
    @Autowired
    private IInstitutionCenterClient institutionCenterClient;
    @Autowired
    private InstitutionProviderCommissionService commissionService;
    @Autowired
    private CommissionChangedEventPublisher commissionChangedEventPublisher;
    @Autowired
    private InstitutionProviderCommissionPlanMapper commissionPlanMapper;
    @Autowired
    private LogOperationMapper logOperationMapper;
    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;
    @Autowired
    private InstitutionProviderCommissionPlanInstitutionService planInstitutionService;
    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private InstitutionProviderCommissionPlanApprovalMapper planApprovalMapper;
    @Autowired
    private PmpMediaAndAttachedService pmpMediaAndAttachedService;
    @Autowired
    private InstitutionProviderCommissionPlanApprovalMapper commissionPlanApprovalMapper;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveProviderContract(SaveProviderContractDto saveProviderContractDto, Boolean updateProvider) {
        UserInfo user = SecureUtil.getUser();
        checkProviderContract(saveProviderContractDto);
        if (Objects.nonNull(saveProviderContractDto.getId()) && saveProviderContractDto.getId() > 0) {
            InstitutionProviderContract currentProviderContract = institutionProviderContractMapper.selectById(saveProviderContractDto.getId());
            if (Objects.isNull(currentProviderContract)) {
                log.error("更新合同失败,供应商合同不存在,参数:{}", JSONObject.toJSONString(saveProviderContractDto));
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PROVIDER_CONTRACT_NOT_FOUND", "供应商合同不存在"));
            }
            Long originalProviderId = currentProviderContract.getFkInstitutionProviderId();
            BeanCopyUtils.copyProperties(saveProviderContractDto, currentProviderContract);
            currentProviderContract.setFkInstitutionProviderId(saveProviderContractDto.getInstitutionProviderId());
            currentProviderContract.setFkContractPartyId(saveProviderContractDto.getContractPartyId());
            currentProviderContract.setFkContractTypeId(saveProviderContractDto.getContractTypeId());
            currentProviderContract.setGmtModified(new Date());
            currentProviderContract.setGmtModifiedUser(user.getLoginId());
            institutionProviderContractMapper.updateById(currentProviderContract);
            pmpMediaAndAttachedService.saveMedia(MediaTableEnum.PROVIDER_CONTRACT.getCode(),
                    saveProviderContractDto.getId(), saveProviderContractDto.getMediaList());
            //同步方案时间
            if (Objects.nonNull(saveProviderContractDto.getUpdatePlanTime()) && saveProviderContractDto.getUpdatePlanTime().equals(1)) {
                commissionPlanService.update(new LambdaUpdateWrapper<InstitutionProviderCommissionPlan>()
                        .set(InstitutionProviderCommissionPlan::getStartTime, currentProviderContract.getStartTime())
                        .set(InstitutionProviderCommissionPlan::getIsTimeless, currentProviderContract.getIsTimeless())
                        .set(InstitutionProviderCommissionPlan::getEndTime, currentProviderContract.getEndTime())
                        .set(InstitutionProviderCommissionPlan::getGmtModifiedUser, SecureUtil.getLoginId())
                        .set(InstitutionProviderCommissionPlan::getGmtModified, new Date())
                        .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, currentProviderContract.getId()));
            }
            //提供商改变,更新佣金方案
            if (!originalProviderId.equals(saveProviderContractDto.getInstitutionProviderId())) {
                commissionPlanService.changeContractProvider(currentProviderContract.getId(), saveProviderContractDto.getInstitutionProviderId());
            }

            return saveProviderContractDto.getId();
        }
        InstitutionProviderContract providerContract = new InstitutionProviderContract();
        BeanCopyUtils.copyProperties(saveProviderContractDto, providerContract);
        providerContract.setFkInstitutionProviderId(saveProviderContractDto.getInstitutionProviderId());
        providerContract.setFkContractPartyId(saveProviderContractDto.getContractPartyId());
        providerContract.setFkContractTypeId(saveProviderContractDto.getContractTypeId());
        providerContract.setGmtCreate(new Date());
        providerContract.setGmtCreateUser(user.getLoginId());
        providerContract.setGmtModified(new Date());
        providerContract.setGmtModifiedUser(user.getLoginId());
        providerContract.setIsLocked(0);
        providerContract.setApprovalStatus(ApprovalStatusEnum.UN_COMMITTED.getCode());
        institutionProviderContractMapper.insert(providerContract);
        String contractNumber = ContractNumberGeneratorUtil.generateContractNumber(providerContract.getGmtCreate(), providerContract.getId());
        providerContract.setContractNum(contractNumber);
        institutionProviderContractMapper.updateById(providerContract);
        pmpMediaAndAttachedService.saveMedia(MediaTableEnum.PROVIDER_CONTRACT.getCode(),
                providerContract.getId(), saveProviderContractDto.getMediaList());
        return providerContract.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enableProviderContract(EnableProviderContractDto enableProviderContractDto) {
        UserInfo user = SecureUtil.getUser();
        List<InstitutionProviderContract> contractList = institutionProviderContractMapper.selectList(new LambdaQueryWrapper<InstitutionProviderContract>()
                .in(InstitutionProviderContract::getId, enableProviderContractDto.getIdList()));
        if (CollectionUtils.isNotEmpty(contractList)) {
            contractList.forEach(contract -> {
                contract.setIsActive(enableProviderContractDto.getIsActive());
                contract.setGmtModified(new Date());
                contract.setGmtModifiedUser(user.getLoginId());
            });
            this.updateBatchById(contractList);

//            if (enableProviderContractDto.getIsActive().equals(0)) {
//                //失效学校佣金方案
//                List<InstitutionProviderCommissionPlan> commissionPlanList = commissionPlanService.list(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
//                        .in(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, contractList.stream().map(InstitutionProviderContract::getId).collect(Collectors.toList()))
//                        .eq(InstitutionProviderCommissionPlan::getIsActive, 1));
//                if (CollectionUtils.isNotEmpty(commissionPlanList)) {
//                    commissionPlanList.forEach(plan -> {
//                        plan.setIsActive(0);
//                        plan.setGmtModified(new Date());
//                    });
//                    commissionPlanService.updateBatchById(commissionPlanList);
//                    List<Long> planIds = commissionPlanList.stream().map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
//                    //失效代理商佣金方案
//                    agentCommissionPlanService.ineffectiveAgentCommissionPlan(planIds);
//                }
//            }

        }
    }

    @Override
    public List<ProviderContractVo> providerContractPage(ProviderContractPageDto params, Page page) {
        //自己创建的合同
        List<Long> selfContractIds = institutionProviderContractMapper.selectList(new LambdaQueryWrapper<InstitutionProviderContract>()
                        .eq(InstitutionProviderContract::getGmtCreateUser, SecureUtil.getLoginId()))
                .stream().map(InstitutionProviderContract::getId).collect(Collectors.toList());
        List<Long> userPermissionPlanIds = commissionPlanService.getUserPermissionPlanIds(null);
//        if (CollectionUtils.isNotEmpty(userPermissionPlanIds)) {
//            //根据可以看到的方案查询合同
//            List<Long> planContractIds = commissionPlanService.list(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
//                            .in(InstitutionProviderCommissionPlan::getId, userPermissionPlanIds))
//                    .stream().map(plan -> plan.getFkInstitutionProviderContractId())
//                    .distinct().collect(Collectors.toList());
//            selfContractIds.addAll(planContractIds);
//        }
        //查询当前用户国家线可以看到的合同
        List<Long> countryIds = SecureUtil.getCountryIds();
        if (CollectionUtils.isEmpty(countryIds)) {
            page.setTotalPage(0);
            page.setTotalResult(0);
            log.error("登录用户无countryIds:{}", JSONObject.toJSONString(SecureUtil.getCountryIds()));
            return Collections.EMPTY_LIST;
        }
        List<Long> countryContractIds = institutionProviderContractMapper.selectContractIdsByCountryIds(countryIds);
        if (CollectionUtils.isNotEmpty(countryContractIds)) {
            selfContractIds.addAll(countryContractIds);
        }
        if (CollectionUtils.isEmpty(selfContractIds)) {
            page.setTotalPage(0);
            page.setTotalResult(0);
            log.error("登录用户无合同,登录人:{},分公司:{},国家:{}", SecureUtil.getLoginId(), SecureUtil.getCompanyIds(), countryIds);
            return Collections.EMPTY_LIST;
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (CollectionUtils.isEmpty(companyIds)) {
            log.error("登录用户无companyIds:{}", JSONObject.toJSONString(SecureUtil.getUser()));
            return Collections.EMPTY_LIST;
        }
        IPage<ProviderContractVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<ProviderContractVo> list = institutionProviderContractMapper.getProviderContractPage(pages, params, companyIds, selfContractIds);
        if (CollectionUtils.isEmpty(list)) {
            page.setTotalPage(0);
            page.setTotalResult(0);
            return Collections.EMPTY_LIST;
        }
        List<Long> providerIds = list.stream().map(ProviderContractVo::getFkInstitutionProviderId).distinct().collect(Collectors.toList());
        Map<Long, List<Long>> providerInstitutionMap = institutionCenterMapper.getInstitutionListByProviderIds(providerIds, companyIds)
                .stream().collect(Collectors.groupingBy(
                        ProviderInstitutionVo::getProviderId,
                        Collectors.mapping(ProviderInstitutionVo::getInstitutionId, Collectors.toList())));
        List<InstitutionProviderCommissionPlan> contractPlans = commissionPlanService.list(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                .in(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, list.stream().map(ProviderContractVo::getId).collect(Collectors.toList()))
                .in(CollectionUtils.isNotEmpty(userPermissionPlanIds), InstitutionProviderCommissionPlan::getId, userPermissionPlanIds));
        List<String> planCreateUserList = contractPlans.stream().map(InstitutionProviderCommissionPlan::getGmtCreateUser).distinct().collect(Collectors.toList());
        Map<Long, List<String>> contractIdToCreatorsMap = contractPlans.stream()
                .collect(Collectors.groupingBy(
                        InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId,
                        Collectors.mapping(
                                InstitutionProviderCommissionPlan::getGmtCreateUser,
                                Collectors.toList()
                        )));
        //创建人-合同加方案
        List<String> allCreateUsers = list.stream()
                .map(ProviderContractVo::getGmtCreateUser)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        allCreateUsers.addAll(planCreateUserList);
        Map<String, String> loginIdToUsernameMap = permissionCenterMapper.getStaffListByLoginIds(allCreateUsers)
                .stream()
                .collect(Collectors.toMap(StaffVo::getLoginId, StaffVo::getName, (a, b) -> a));
        list.stream().forEach(contract -> {
            //学校数量
            contract.setInstitutionCount(0);
            //查询合同下的方案审核统计数据
            if (CollectionUtils.isNotEmpty(userPermissionPlanIds)) {
                List<InstitutionProviderCommissionPlan> planList = commissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                        .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, contract.getId())
                        .in(InstitutionProviderCommissionPlan::getId, userPermissionPlanIds));
                Map<Integer, Long> statusCountMap = planList.stream()
                        .collect(Collectors.groupingBy(InstitutionProviderCommissionPlan::getApprovalStatus, Collectors.counting()));
                Map<Integer, Long> activePlanStatusCountMap = planList.stream()
                        .filter(plan -> plan.getIsActive().equals(1))
                        .collect(Collectors.groupingBy(InstitutionProviderCommissionPlan::getApprovalStatus, Collectors.counting()));
                // 设置各种状态的数量
                contract.setPassPlanCount(statusCountMap.getOrDefault(2, 0L).intValue());
                contract.setExaminingPlanCount(statusCountMap.getOrDefault(1, 0L).intValue());
                contract.setPendingPlanCount(statusCountMap.getOrDefault(0, 0L).intValue());
                contract.setRejectPlanCount(statusCountMap.getOrDefault(3, 0L).intValue());

                // 默认可以续约
                contract.setCanRenewal(Boolean.TRUE);
                // 检查需要判断的状态（审批中、待提交、驳回）-续签不考虑已下架的状态
                List<Integer> watchStatus = Arrays.asList(0, 1, 3);
                boolean canRenew = watchStatus.stream().noneMatch(status ->
                        statusCountMap.getOrDefault(status, 0L) > 0 &&
                                activePlanStatusCountMap.getOrDefault(status, 0L) > 0);
                contract.setCanRenewal(canRenew);

                List<Long> planIds = planList.stream().map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(planIds)) {
                    //当前的合同全部方案学校数量
                    List<Long> currentContractInstitutionIds = planInstitutionService.getBaseMapper().selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                                    .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, planIds))
                            .stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId).collect(Collectors.toList());
                    if (Objects.nonNull(providerInstitutionMap.get(contract.getFkInstitutionProviderId()))) {
                        //当前提供商的全部学校列表
                        List<Long> providerInstitutionIds = providerInstitutionMap.get(contract.getFkInstitutionProviderId());
                        int total = (int) currentContractInstitutionIds.stream()
                                .filter(providerInstitutionIds::contains).distinct().count();
                        contract.setInstitutionCount(total);
                    }
                    //填充国家信息
                    if (CollectionUtils.isNotEmpty(currentContractInstitutionIds)) {
                        List<String> countryNamesList = institutionCenterMapper.getCountryListByInstitutionIds(currentContractInstitutionIds)
                                .stream().map(CountryVo::getName).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(countryNamesList)) {
                            contract.setCountryNames(String.join(",", countryNamesList));
                        }
                    }
                }
            }
            //填充创建人信息
            List<String> loginIds = new ArrayList<>(Arrays.asList(contract.getGmtCreateUser()));
            List<String> planCreatorLoginIds = contractIdToCreatorsMap.getOrDefault(contract.getId(), Collections.emptyList());
            if (CollectionUtils.isNotEmpty(planCreatorLoginIds)) {
                List<String> distinctLoginIds = planCreatorLoginIds.stream().distinct().collect(Collectors.toList());
                loginIds.addAll(distinctLoginIds);
            }
            loginIds = loginIds.stream().distinct().collect(Collectors.toList());
            List<String> usernames = loginIds.stream()
                    .distinct()
                    .map(loginIdToUsernameMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            contract.setCreateUserNames(String.join(",", usernames));
            contract.setCreateUserLoginIds(String.join(",", loginIds));
        });
        int totalCount = (int) pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return list;
    }

    @Override
    public ProviderContractDetailVo providerContractDetail(Long id) {
        ProviderContractDetailVo detail = new ProviderContractDetailVo();
        ProviderContractVo contractVo = institutionProviderContractMapper.getProviderContractById(id);
        detail.setContractInfo(contractVo);
        //合同附件列表
        List<MediaVo> contractMediaList = mediaAndAttachedService.getMediaList(MediaTableEnum.PROVIDER_CONTRACT.getCode(), Arrays.asList(id));
        detail.setContracMediaList(contractMediaList);
        //佣金计划列表
        ProviderCommissionPlanListVo commissionPlan = commissionPlanService.getProviderCommissionPlanList(id);
        detail.setCommissionPlanList(commissionPlan.getCommissionPlanList());
        detail.setCommissionPlanMediaList(commissionPlan.getCommissionPlanMediaList());
        //学校列表
        ProviderCommissionListVo planInstitutionList = planInstitutionService.getPlanInstitutionList(1, id);
        //查询学校的佣金方案数量
//        List<InstitutionVo> institutionList = planInstitutionList.getInstitutionList();
//        if (CollectionUtils.isNotEmpty(institutionList)) {
//            List<Long> institutionIds = institutionList.stream().map(InstitutionVo::getInstitutionId).distinct().collect(Collectors.toList());
//            Map<Long, Integer> planCountMap = commissionPlanService.institutionPlanCount(institutionIds);
//            institutionList.stream().forEach(item -> {
//                item.setPlanCount(planCountMap.getOrDefault(item.getInstitutionId(), 0));
//            });
//        }
        detail.setInstitutionIds(planInstitutionList.getInstitutionIds());
        detail.setInstitutionList(planInstitutionList.getInstitutionList());

        //查询方案锁定和审核权限
        detail.getCommissionPlanList().stream().forEach(plan -> {
            ProviderCommissionPlanVo planPermission = checkPlanPermission(plan);
            plan.setLockPermission(planPermission.getLockPermission());
            plan.setApprovalPermission(planPermission.getApprovalPermission());
            plan.setPendingApprovalType(planPermission.getPendingApprovalType());
        });
        //排序：
        // 1:已驳回(用户会优先处理)
        //2:未提交(用户未完成的工作)
        //3:待审批(已完成待确认)
        //4:已通过(已完成的工作)
        //5:未创建(代理佣金)
        //6:再按创建时间最新排最前。
        List<ProviderCommissionPlanVo> sortedList = detail.getCommissionPlanList().stream()
                .sorted(Comparator
                        .comparingInt((ProviderCommissionPlanVo vo) -> {
                            // 自定义 approvalStatus 排序规则
                            switch (vo.getApprovalStatus() == null ? -1 : vo.getApprovalStatus()) {
                                case 3:
                                    return 0;
                                case 0:
                                    return 1;
                                case 1:
                                    return 2;
                                case 2:
                                    return 3;
                                default:
                                    return 4;
                            }
                        })
                        .thenComparing(ProviderCommissionPlanVo::getGmtCreate, Comparator.nullsLast(Comparator.reverseOrder()))
                )
                .collect(Collectors.toList());

        //查询是否有批量审核的数据
        if (CollectionUtils.isNotEmpty(sortedList)) {
            List<BatchApprovalVo> batchApprovalList = commissionPlanService.getBatchApprovalList(id);
            detail.setHasBatchApprovalList(CollectionUtils.isNotEmpty(batchApprovalList));
        }
        detail.setCommissionPlanList(sortedList);
        return detail;
    }

    @Override
    public InstitutionProviderContract getProviderContractBaseInfo(Long id) {
        InstitutionProviderContract contract = institutionProviderContractMapper.selectById(id);
        if (Objects.nonNull(contract)) {
            List<MediaVo> mediaList = pmpMediaAndAttachedService.getMediaList(MediaTableEnum.PROVIDER_CONTRACT.getCode(), Arrays.asList(id));
            contract.setMediaList(mediaList);
        }
        return contract;
    }

    @Override
    public List<ContractTypeVo> getContractTypeList() {
        return contractTypeMapper.selectList(new LambdaQueryWrapper<PmpContractType>()
                        .orderByDesc(PmpContractType::getViewOrder))
                .stream().map(contractType -> {
                    ContractTypeVo vo = new ContractTypeVo();
                    vo.setContractTypeId(contractType.getId());
                    vo.setTypeName(contractType.getTypeName());
                    return vo;
                }).collect(Collectors.toList());
    }


    @Override
    public ContractDetailVo renewalContractDetail(Long id) {
        InstitutionProviderContract contract = institutionProviderContractMapper.selectById(id);
        if (Objects.isNull(contract)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PROVIDER_CONTRACT_NOT_FOUND", "供应商合同不存在"));
        }
        ContractDetailVo vo = new ContractDetailVo();
        vo.setRenewalContractId(contract.getId());
        SaveProviderContractDto contractDetail = vo.getContract();
        contractDetail.setInstitutionProviderId(contract.getFkInstitutionProviderId());
        contractDetail.setContractPartyId(contract.getFkContractPartyId());
        contractDetail.setContractTypeId(contract.getFkContractTypeId());
        contractDetail.setContractTitle(contract.getContractTitle());
        contractDetail.setRemark(contract.getRemark());
        contractDetail.setIsActive(contract.getIsActive());
        contractDetail.setIsTimeless(0);
        contractDetail.setStartTime(contract.getStartTime());
        if (Objects.nonNull(contract.getEndTime())) {
            //默认以旧合同结束日期的后一天作为新合同的开始时间，增加两年作为结束时间；
            contractDetail.setStartTime(DateUtil.plusDays(contract.getEndTime(), 1));
            contractDetail.setEndTime(DateUtil.plusYears(contractDetail.getStartTime(), 2));
        }
        vo.setContract(contractDetail);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long renewalContract(ContractDetailVo contractDetail) {
        //校验时间
        InstitutionProviderContract oldContract = institutionProviderContractMapper.selectById(contractDetail.getRenewalContractId());
        if (Objects.isNull(oldContract)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_ORIGINAL_CONTRACT_NOT_FOUND", "原合同不存在"));
        }
        boolean overlapping = isOverlapping(oldContract.getStartTime(), oldContract.getEndTime(), contractDetail.getContract().getStartTime(), contractDetail.getContract().getEndTime());
        if (overlapping) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_NEW_CONTRACT_DATE_OVERLAP", "新合同时间范围与原合同时间范围重叠"));
        }
        List<LogDto> logs = new ArrayList();
        //保存合同
        SaveProviderContractDto contract = contractDetail.getContract();
        if (CollectionUtils.isEmpty(contract.getMediaList())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_CONTRACT_FILE_MISSING", "合同附件暂未上传,请先上传合同附件"));
        }
        contract.setId(null);
        Long contractId = saveProviderContract(contract, Boolean.TRUE);

        //保存佣金方案加明细-下架的方案不用复制(0708)
        //获取原合同佣金方案
        ProviderCommissionPlanListVo providerCommissionPlanList = commissionPlanService.getProviderCommissionPlanList(contractDetail.getRenewalContractId());
        if (CollectionUtils.isNotEmpty(providerCommissionPlanList.getCommissionPlanList())) {
            List<SaveProviderCommissionPlanDetailDto> planDetailList = providerCommissionPlanList.getCommissionPlanList().stream()
                    .filter(plan -> plan.getIsActive().equals(1))
                    .sorted(Comparator.comparing(ProviderCommissionPlanVo::getId))
                    .map(p -> {
                        //合同内的方案有效时间默认同步新合同的有效时间
                        ProviderCommissionPlanDetailVo detail = commissionPlanService.getProviderCommissionPlanDetail(p.getId());
                        SaveProviderCommissionPlanDetailDto planDetail = new SaveProviderCommissionPlanDetailDto();
                        planDetail.setInstitutionProviderId(detail.getFkInstitutionProviderId());
                        planDetail.setName(detail.getName());
                        planDetail.setIsTimeless(contract.getIsTimeless());
                        planDetail.setIsActive(detail.getIsActive());
                        planDetail.setRemark(detail.getRemark());
                        planDetail.setTerritory(detail.getTerritory());
                        planDetail.setTerritoryChn(detail.getTerritoryChn());
                        planDetail.setSummary(detail.getSummary());
                        planDetail.setSummaryChn(detail.getSummaryChn());
                        planDetail.setCourse(detail.getCourse());
                        planDetail.setCourseChn(detail.getCourseChn());
                        planDetail.setTerritoryList(detail.getTerritoryList());
                        planDetail.setStartTime(contract.getStartTime());
                        planDetail.setEndTime(contract.getEndTime());
                        //佣金方案明细
                        SaveProviderCommissionDto providerCommissionDto = new SaveProviderCommissionDto();
                        ProviderCommissionListVo providerCommissionList = commissionService.getProviderCommissionList(p.getId());
                        BeanCopyUtils.copyProperties(providerCommissionList, providerCommissionDto);
                        providerCommissionDto = initProviderCommissionList(providerCommissionDto, null);
                        planDetail.setProviderCommission(providerCommissionDto);
                        return planDetail;
                    }).collect(Collectors.toList());

            //保存佣金方案及佣金明细
            for (SaveProviderCommissionPlanDetailDto plan : planDetailList) {
                plan.setId(null);
                plan.setInstitutionProviderContractId(contractId);
                plan.setMediaList(new ArrayList<>());
                Long planId = commissionPlanService.saveProviderCommissionPlan(plan, Boolean.FALSE);
                SaveProviderCommissionDto providerCommission = plan.getProviderCommission();
                providerCommission.setCommissionPlanId(planId);
                providerCommission = initProviderCommissionList(providerCommission, planId);
                //如果没有学校,就是空方案,不保存明细
                if (CollectionUtils.isNotEmpty(providerCommission.getInstitutionIds())) {
                    commissionService.saveProviderCommission(providerCommission, Boolean.FALSE);
                }
            }
        }
        InstitutionProviderContract newContract = institutionProviderContractMapper.selectById(contractId);
        //原合同显示:时间’，'用户名’续签合同，新合同编号为'合同编号
        logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.RENEW_CONTRACT, LogTableEnum.PROVIDER_CONTRACT,
                oldContract.getId(), LogEventEnum.RENEW_CONTRACT_ORIGINAL, SecureUtil.getLoginId(),
                newContract.getContractNum(), oldContract.getId()));
        //新合同显示:"时间，’用户名’通过'原合同名’续签，创建了此合同记录。
        logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.RENEW_CONTRACT, LogTableEnum.PROVIDER_CONTRACT,
                contractId, LogEventEnum.RENEW_CONTRACT_NEW, SecureUtil.getLoginId(),
                oldContract.getContractTitle(), contractId));
        commissionChangedEventPublisher.publishCommissionChangedEvent(logs);
        return contractId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delContract(IdListDto ids) {
        checkContract(ids.getIds());
        ids.getIds().stream().forEach(id -> {
            List<InstitutionProviderCommissionPlan> planList = commissionPlanMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                    .eq(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, id));
            planList.stream().forEach(plan -> {
                commissionPlanService.deleteProviderCommissionPlan(plan.getId(), Boolean.FALSE);
            });
            //获取学校提供商
            InstitutionProviderContract contract = institutionProviderContractMapper.selectById(id);
            Long InstitutionProviderId = contract.getFkInstitutionProviderId();
            institutionProviderContractMapper.deleteById(id);
            //判断是否有合同
            if (GeneralTool.isNotEmpty(InstitutionProviderId)) {
                LambdaQueryWrapper<InstitutionProviderContract> wrapper = new LambdaQueryWrapper();
                wrapper.eq(InstitutionProviderContract::getFkInstitutionProviderId, InstitutionProviderId);
                List<InstitutionProviderContract> contractList = institutionProviderContractMapper.selectList(wrapper);
                if (GeneralTool.isEmpty(contractList)) {
                    Integer i = institutionCenterClient.updateContractStatus(InstitutionProviderId, 0);
                    log.info("更新供应商合同状态,供应商id:{},状态:{}", InstitutionProviderId, i);
                    if (i < 1) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_UPDATE_CONTRACT_STATUS_FAIL", "更新供应商合同状态失败"));
                    }
                }
            }


        });

        //删除日志
        logOperationMapper.delete(new LambdaQueryWrapper<LogOperation>()
                .in(LogOperation::getFkInstitutionProviderContractId, ids.getIds()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockContract(LockContractDto lockContractDto) {
        InstitutionProviderContract contract = institutionProviderContractMapper.selectById(lockContractDto.getContractId());
        if (Objects.isNull(contract)) {
//            throw new GetServiceException("合同不存在!");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PROVIDER_CONTRACT_NOT_FOUND", "供应商合同不存在"));
        }
        if (!contract.getGmtCreateUser().equals(SecureUtil.getLoginId())) {
            log.error("锁定合同失败,没有权限,当前操作人:{},合同id:{},合同创建人:{}", SecureUtil.getLoginId(), SecureUtil.getLoginId(), contract.getGmtCreateUser());
//            throw new GetServiceException("您没有权限操作!");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_NO_PERMISSION", "您没有权限操作"));
        }
        contract.setIsLocked(lockContractDto.getIsLocked());
        contract.setGmtModified(new Date());
        contract.setGmtCreateUser(SecureUtil.getLoginId());
        institutionProviderContractMapper.updateById(contract);
    }


    @Override
    public List<Long> getExpirePlanIds(Long institutionProviderId) {
        //判断哪些合同是到期的
        List<Long> expirePlanId = new ArrayList<>();
//        List<InstitutionProviderContract> expireContract = institutionProviderContractMapper.selectList(new LambdaQueryWrapper<InstitutionProviderContract>()
//                .eq(InstitutionProviderContract::getIsTimeless, 0)
//                .lt(InstitutionProviderContract::getEndTime, endDate)
//                .eq(Objects.nonNull(institutionProviderId), InstitutionProviderContract::getFkInstitutionProviderId, institutionProviderId)
//                .eq(InstitutionProviderContract::getIsActive, 1));
        //已经生效但是过期的合同
        List<Long> allExpireContractIds = new ArrayList<>();
        List<Long> expireContractId = institutionProviderContractMapper.selectExpireContract(institutionProviderId)
                .stream()
                .map(InstitutionProviderContract::getId)
                .collect(Collectors.toList());
        //已经下架的合同
        List<Long> unActiveContractIds = institutionProviderContractMapper.selectList(new LambdaQueryWrapper<InstitutionProviderContract>()
                        .eq(Objects.nonNull(institutionProviderId),InstitutionProviderContract::getFkInstitutionProviderId, institutionProviderId)
                        .eq(InstitutionProviderContract::getIsActive, 0))
                .stream()
                .map(InstitutionProviderContract::getId)
                .collect(Collectors.toList());
        allExpireContractIds.addAll(unActiveContractIds);
        allExpireContractIds.addAll(expireContractId);
        if (CollectionUtils.isNotEmpty(allExpireContractIds)) {
            //查询佣金方案
            List<InstitutionProviderCommissionPlan> commissionPlanList = commissionPlanService.list(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                    .in(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, allExpireContractIds));
            if (CollectionUtils.isNotEmpty(commissionPlanList)) {
                List<Long> planIds = commissionPlanList.stream().map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
                expirePlanId.addAll(planIds);
            }
        }

        //查询已经下架的合同方案
        List<InstitutionProviderCommissionPlan> unActivePlan = commissionPlanService.list(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                .eq(Objects.nonNull(institutionProviderId), InstitutionProviderCommissionPlan::getFkInstitutionProviderId, institutionProviderId)
                .eq(InstitutionProviderCommissionPlan::getIsActive, 0));
        List<Long> unActivePlanIds = unActivePlan.stream().filter(plan -> !expirePlanId.contains(plan.getId()))
                .map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
        expirePlanId.addAll(unActivePlanIds);
        return expirePlanId;
    }

    @Override
    public ProviderCommissionPlanVo checkPlanPermission(ProviderCommissionPlanVo providerCommissionPlanVo) {
        if (Objects.isNull(providerCommissionPlanVo.getIsLocked())) {
            providerCommissionPlanVo.setIsLocked(0);
        }
        if (Objects.isNull(providerCommissionPlanVo.getApprovalStatus())) {
            providerCommissionPlanVo.setApprovalStatus(ApprovalStatusEnum.UN_COMMITTED.getCode());
        }

        if (SecureUtil.getLoginId().equals(providerCommissionPlanVo.getGmtCreateUser())) {
            //如果创建人和当前用户一致,有锁定权限
            providerCommissionPlanVo.setLockPermission(1);
        } else {
            //默认无锁定权限
            providerCommissionPlanVo.setLockPermission(0);
        }

        if (providerCommissionPlanVo.getIsLocked().equals(1)) {
            //判断锁定权限-默认无权限
            if (SecureUtil.getLoginId().equals(providerCommissionPlanVo.getGmtCreateUser())) {
                providerCommissionPlanVo.setLockPermission(3);
            } else {
                providerCommissionPlanVo.setLockPermission(2);
            }
        }
        //判断审核权限
        if (SecureUtil.getLoginId().equals(providerCommissionPlanVo.getGmtCreateUser())) {
            //如果创建人和当前用户一致,有审核权限
            providerCommissionPlanVo.setApprovalPermission(1);
        } else {
            //默认无审核权限
            providerCommissionPlanVo.setApprovalPermission(0);
        }
        if (providerCommissionPlanVo.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            //查询最新一条的提交审核记录
            InstitutionProviderCommissionPlanApproval planApproval = planApprovalMapper.selectOne(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                    .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                    .apply("FIND_IN_SET({0}, fk_institution_provider_commission_plan_ids)", providerCommissionPlanVo.getId())
                    .orderByDesc(InstitutionProviderCommissionPlanApproval::getGmtCreate)
                    .last("LIMIT 1"));
            if (Objects.nonNull(planApproval)) {
                providerCommissionPlanVo.setApprovalPermission(2);
                providerCommissionPlanVo.setPendingApprovalType(planApproval.getTypeKey());
                if (planApproval.getFkStaffId().equals(SecureUtil.getStaffId())) {
                    providerCommissionPlanVo.setApprovalPermission(3);
                    //审批人-也可以解锁
                    if (providerCommissionPlanVo.getIsLocked().equals(1)) {
                        providerCommissionPlanVo.setLockPermission(3);
                    }
                }
            }
        }
        return providerCommissionPlanVo;
    }

    @Override
    public List<CountryVo> getCountryList() {
        List<Long> userPermissionPlanIds = commissionPlanService.getUserPermissionPlanIds(null);
        if (CollectionUtils.isEmpty(userPermissionPlanIds)) {
            return Collections.EMPTY_LIST;
        }
        List<Long> InstitutionIds = planInstitutionService.getBaseMapper().selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanInstitution>()
                        .in(InstitutionProviderCommissionPlanInstitution::getFkInstitutionProviderCommissionPlanId, userPermissionPlanIds))
                .stream().map(InstitutionProviderCommissionPlanInstitution::getFkInstitutionId)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(InstitutionIds)) {
            return institutionCenterMapper.getCountryListByInstitutionIds(InstitutionIds);
        }
        return Collections.emptyList();
    }

    private void checkContract(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_CONTRACT_ID_REQUIRED", "合同id不能为空"));
        }
        Integer plans = commissionPlanService.getBaseMapper().selectCount(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                .in(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId, ids));
        if (plans > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_EXIST_UNDER_CONTRACT", "合同下存在佣金方案，不能删除"));
        }
    }

    private void checkProviderContract(SaveProviderContractDto providerContractDto) {
        if (providerContractDto.getIsTimeless() < 1 && Objects.isNull(providerContractDto.getEndTime())) {
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_CONTRACT_END_DATE_REQUIRED", "合同结束时间不能为空!"));
        }
        if (CollectionUtils.isEmpty(providerContractDto.getMediaList())) {
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_CONTRACT_FILE_MISSING", "合同附件不能为空!"));
        }
    }

    private SaveProviderCommissionDto initProviderCommissionList(SaveProviderCommissionDto dto, Long planId) {
        dto.getCommissionList().stream().forEach(commission -> {
            commission.setId(null);
            commission.setFkInstitutionProviderCommissionPlanId(planId);
        });
        dto.getCombinationList().stream().forEach(combinationInfo -> {
            combinationInfo.setPackageKey(Strings.EMPTY);
            combinationInfo.getCommissionInfoList().stream().forEach(commission -> {
                commission.setId(null);
                commission.setFkInstitutionProviderCommissionPlanId(planId);
            });
        });
        dto.getBonusList().stream().forEach(bonusInfo -> {
            bonusInfo.setId(null);
            bonusInfo.setFkInstitutionProviderCommissionPlanId(planId);
        });
        return dto;
    }

    public static boolean isOverlapping(Date oldStartDate, Date oldEndDate, Date newStartDate, Date newEndDate) {
        // 1. 确保新开始时间不早于旧开始时间
        if (newStartDate.before(oldStartDate)) {
            return true;
        }
        // 直接判断重叠情况
        if (Objects.isNull(newEndDate)) {
            // 新的结束日期为空（无限久），只要新开始日期不晚于旧的开始日期，就算重叠
            return !newStartDate.after(oldEndDate);
        }
        // 常规比较：判断两个日期范围是否有交集
        return !(newEndDate.before(oldStartDate) || newStartDate.after(oldEndDate));
    }
}

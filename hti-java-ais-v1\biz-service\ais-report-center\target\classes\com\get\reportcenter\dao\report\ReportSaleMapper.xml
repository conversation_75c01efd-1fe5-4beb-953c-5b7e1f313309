<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.reportcenter.dao.report.ReportSaleMapper">
	<resultMap id="BaseResultMap" type="com.get.reportcenter.entity.ReportSale">
		<result column="id" jdbcType="BIGINT" property="id" />
		<result column="fk_user_id" jdbcType="BIGINT" property="fkUserId" />
		<result column="report_name" jdbcType="VARCHAR" property="reportName" />
		<result column="report_query" jdbcType="VARCHAR" property="reportQuery" javaType="com.get.salecenter.dto.StudentApplicationStatisticsDto" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
		<result column="report_result" jdbcType="VARCHAR" property="reportResult" javaType="com.get.salecenter.vo.PeriodicStatisticsVo" typeHandler="com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler" />
		<result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
		<result column="report_status" jdbcType="INTEGER" property="reportStatus" />
		<result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
		<result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
		<result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
		<result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
	</resultMap>
  <sql id="Base_Column_List">
		t.id,
		t.fk_user_id,
		t.report_name,
		t.report_query,
		t.report_result,
		t.report_time,
		t.report_status,
		t.gmt_create,
		t.gmt_create_user,
		t.gmt_modified,
		t.gmt_modified_user
	</sql>

	<select id="getLastReportSale" resultType="com.get.reportcenter.entity.ReportSale" resultMap="BaseResultMap">
		SELECT <include refid="Base_Column_List" />
		FROM m_report_sale AS t
		WHERE t.fk_user_id = #{fkStaffId}
		ORDER BY t.report_time DESC LIMIT 1
	</select>

  <select id="getReportSale" resultType="com.get.reportcenter.entity.ReportSale" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List" />
    FROM m_report_sale AS t
    WHERE  t.fk_user_id = #{fkUserId}
    AND DATE_FORMAT(t.gmt_create,'%Y-%m-%d') = DATE_FORMAT(NOW(),'%Y-%m-%d')
    AND t.report_query ->'$.bdName' = #{studentApplicationStatisticsDto.bdName}
    AND t.report_query -> '$.fkCompanyId' = #{studentApplicationStatisticsDto.fkCompanyId}
    <choose>
		<when test="studentApplicationStatisticsDto.fkAreaRegionId != null">
			AND t.report_query -> '$.fkAreaRegionId' = #{studentApplicationStatisticsDto.fkAreaRegionId}
		</when>
		<otherwise>
			AND JSON_CONTAINS(t.report_query -> '$.fkAreaRegionId','null')
		</otherwise>
	</choose>

	<choose>
		<when test="studentApplicationStatisticsDto.fkInstitutionId != null">
			AND t.report_query -> '$.fkInstitutionId' = #{studentApplicationStatisticsDto.fkInstitutionId}
		</when>
		<otherwise>
			AND JSON_CONTAINS(t.report_query -> '$.fkInstitutionId','null')
		</otherwise>
	</choose>


	<if test="studentApplicationStatisticsDto.fkCompanyIdList != null and studentApplicationStatisticsDto.fkCompanyIdList.size() > 0">
	   <foreach collection="studentApplicationStatisticsDto.fkCompanyIdList" item="fkCompanyId" >
		   AND JSON_CONTAINS(t.report_query -> '$.fkCompanyIdList',JSON_Array(#{fkCompanyId}))
	   </foreach>
	</if>
	  AND JSON_LENGTH(t.report_query -> '$.fkCompanyIdList') = ${studentApplicationStatisticsDto.fkCompanyIdList.size()}

	 <if test="studentApplicationStatisticsDto.fkAreaCountryIds != null and studentApplicationStatisticsDto.fkAreaCountryIds.size()>0">
		 <foreach collection="studentApplicationStatisticsDto.fkAreaCountryIds" item="fkAreaCountryId" >
			 AND JSON_CONTAINS(t.report_query -> '$.fkAreaCountryIds',JSON_Array(#{fkAreaCountryId}))
		 </foreach>
	 </if>
	  AND JSON_LENGTH(t.report_query -> '$.fkAreaCountryIds') = ${studentApplicationStatisticsDto.fkAreaCountryIds.size()}

	  <if test="studentApplicationStatisticsDto.staffFollowerIds != null and studentApplicationStatisticsDto.staffFollowerIds.size()>0">
		  <foreach collection="studentApplicationStatisticsDto.staffFollowerIds" item="staffFollowerId">
			  AND JSON_CONTAINS(t.report_query -> '$.staffFollowerIds',JSON_Array(#{fkAreaCountryId}))
		  </foreach>
	  </if>
	  AND JSON_LENGTH(t.report_query -> '$.staffFollowerIds') = ${studentApplicationStatisticsDto.staffFollowerIds.size()}

	  <if test="studentApplicationStatisticsDto.fkAreaCountryIdList != null and studentApplicationStatisticsDto.fkAreaCountryIdList.size()>0">
		  <foreach collection="studentApplicationStatisticsDto.fkAreaCountryIdList" item="fkAreaCountryId">
			  AND JSON_CONTAINS(t.report_query -> '$.fkAreaCountryIdList',JSON_Array(#{fkAreaCountryId}))
		  </foreach>
	  </if>
	  AND JSON_LENGTH(t.report_query -> '$.fkAreaCountryIdList') = ${studentApplicationStatisticsDto.fkAreaCountryIdList.size()}

	  <if test="studentApplicationStatisticsDto.fkInstitutionGroupIds != null and studentApplicationStatisticsDto.fkInstitutionGroupIds.size()>0">
		  <foreach collection="studentApplicationStatisticsDto.fkInstitutionGroupIds" item="fkInstitutionGroupId">
			  AND JSON_CONTAINS(t.report_query -> '$.fkAreaCountryIdList',JSON_Array(#{fkInstitutionGroupId}))
		  </foreach>
	  </if>
	  AND JSON_LENGTH(t.report_query -> '$.fkInstitutionGroupIds') = ${studentApplicationStatisticsDto.fkInstitutionGroupIds.size()}

	  ORDER BY t.report_time DESC LIMIT 1
  </select>
    <select id="getReportSaleStatusById" resultType="java.lang.Integer">
		select report_status from m_report_sale where id = #{fkReportSaleId}
	</select>
    <select id="getReportSaleById" resultType="com.get.reportcenter.dto.ReportSaleDto">
		select * from m_report_sale where id = #{fkReportSaleId}
	</select>
    <select id="getLastReportSaleVoByReportNameAndUserId" resultType="com.get.reportcenter.dto.ReportSaleDto">
		select * from m_report_sale where report_name = #{key} and fk_user_id = #{staffId} order by gmt_create desc limit 1
	</select>

    <update id="updateReportSaleStatus">
		UPDATE m_report_sale SET report_status = #{reportStatus} WHERE id = #{fkReportSaleId}
	</update>


	<insert id="insertSelective" parameterType="com.get.reportcenter.dto.ReportSaleDto" keyProperty="id" useGeneratedKeys="true">
		insert into m_report_sale
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="id != null">
				id,
			</if>
			<if test="fkUserId != null">
				fk_user_id,
			</if>
			<if test="reportName != null">
				report_name,
			</if>
			<if test="reportQuery != null">
				report_query,
			</if>
			<if test="reportResult != null">
				report_result,
			</if>
			<if test="reportTime != null">
				report_time,
			</if>
			<if test="reportStatus != null">
				report_status,
			</if>
			<if test="gmtCreate != null">
				gmt_create,
			</if>
			<if test="gmtCreateUser != null">
				gmt_create_user,
			</if>
			<if test="gmtModified != null">
				gmt_modified,
			</if>
			<if test="gmtModifiedUser != null">
				gmt_modified_user,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="id != null">
				#{id,jdbcType=BIGINT},
			</if>
			<if test="fkUserId != null">
				#{fkUserId},
			</if>
			<if test="reportName != null">
				#{reportName},
			</if>
			<if test="reportQuery != null">
				#{reportQuery},
			</if>
			<if test="reportResult != null">
				#{reportResult},
			</if>
			<if test="reportTime != null">
				#{reportTime},
			</if>
			<if test="reportStatus != null">
				#{reportStatus},
			</if>
			<if test="gmtCreate != null">
				#{gmtCreate,jdbcType=TIMESTAMP},
			</if>
			<if test="gmtCreateUser != null">
				#{gmtCreateUser,jdbcType=VARCHAR},
			</if>
			<if test="gmtModified != null">
				#{gmtModified,jdbcType=TIMESTAMP},
			</if>
			<if test="gmtModifiedUser != null">
				#{gmtModifiedUser,jdbcType=VARCHAR},
			</if>
		</trim>
	</insert>

</mapper>
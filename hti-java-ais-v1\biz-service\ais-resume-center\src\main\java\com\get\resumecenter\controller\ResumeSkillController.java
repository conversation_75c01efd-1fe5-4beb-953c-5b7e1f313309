package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.ResumeSkillVo;
import com.get.resumecenter.service.IResumeSkillService;
import com.get.resumecenter.dto.ResumeSkillDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 15:44
 * @Description: 简历技能管理
 **/
@Api(tags = "简历技能管理")
@RestController
@RequestMapping("resume/skill")
public class ResumeSkillController {
    @Autowired
    private IResumeSkillService skillService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/简历技能管理/参数详情")
    @GetMapping("/{id}")
    public ResponseBo detail(@PathVariable("id") Long id) {
        ResumeSkillVo resumeSkillVo = skillService.getResumeSkillById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", resumeSkillVo);
        return responseBo;
    }

    /**
     * 新增信息
     *
     * @param skillVo
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历技能管理/新增技能")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ResumeSkillDto.Add.class) ResumeSkillDto skillVo) {
        return SaveResponseBo.ok(skillService.addResumeSkill(skillVo));
    }


    /**
     * 修改信息
     *
     * @param skillVo
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/简历技能管理/修改技能")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ResumeSkillDto.Update.class) ResumeSkillDto skillVo) {
        return UpdateResponseBo.ok(skillService.updateResumeSkill(skillVo));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "人才中心/简历技能管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        skillService.deleteResumeSkill(id);
        return ResponseBo.ok();
    }

}

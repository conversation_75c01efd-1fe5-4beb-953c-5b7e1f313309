package com.get.financecenter.dao;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.financecenter.vo.InvoiceVo;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.entity.ReceivablePlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Mapper
public interface InvoiceReceivablePlanMapper extends GetMapper<InvoiceReceivablePlan> {


    /**
     * 获取发票
     *
     * @param planIds
     * @return
     */
    List<InvoiceVo> getInvoicesByPlanIds(@Param("planIds") Set<Long> planIds);

    /**
     * 获取发票绑定金额
     */
    BigDecimal getInvoiceBingDingAmount(@Param("invoiceIds") List<Long> invoiceIds);

    /**
     * 删除绑定关系
     *
     * @param planIds
     */
    void deleteInvoiceReceivablePlan(@Param("planIds") List<Long> planIds);


    /**
     * 批量绑定关系
     */
    Integer addMappingInvoiceReceivablePlan(@Param("list")List<InvoiceReceivablePlan> list);

    /**
     * 获取与发票绑定的应收计划
     * @param invoiceId
     * @return
     */
    List<InvoiceReceivablePlan> getReceivablePlanByInvoiceId(Long invoiceId);

    /**
     *
     * @param invoiceId
     */
    void deleteInvoiceReceivablePlanByInvoiceId(@Param("invoiceId")Long invoiceId);

    /**
     * 获取应收排序后的发票编号
     * @param ids
     * @return
     */
    List<SelItem> getInvoiceNumByReceivableId(Set<Long> ids);

    ReceivablePlan getReceivablePlanById(Long receivablePlanId);

    /**
     * 获取预付金额
     *
     * @Date 18:09 2024/7/1
     * <AUTHOR>
     */
    BigDecimal getPrepaidAmount(@Param("fkReceivablePlanId") Long fkReceivablePlanId, @Param("fkReceiptFormId") Long fkReceiptFormId);
}
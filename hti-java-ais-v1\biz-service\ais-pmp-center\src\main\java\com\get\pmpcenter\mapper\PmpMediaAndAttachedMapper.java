package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.PmpMediaAndAttached;
import com.get.pmpcenter.vo.common.MediaVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PmpMediaAndAttachedMapper extends BaseMapper<PmpMediaAndAttached> {

    /**
     * 根据表名和表id查询媒体信息列表
     *
     * @param tableName
     * @param tableIds
     * @return
     */
    List<MediaVo> selectMediaList(@Param("tableName") String tableName, @Param("tableIds") List<Long> tableIds);

    /**
     * 根据id查询媒体信息
     *
     * @param id
     * @return
     */
    MediaVo selectMedia(@Param("id") Long id);

    /**
     * 修改文件名
     *
     * @param fileGuid
     * @param fileName
     */
    void updateFileName(@Param("fileGuid") String fileGuid, @Param("fileName") String fileName);
}

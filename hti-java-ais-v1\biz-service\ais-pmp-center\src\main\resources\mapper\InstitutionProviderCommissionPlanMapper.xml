<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanMapper">

    <select id="selectContractCommissionPlanList" resultType="com.get.pmpcenter.vo.institution.ProviderCommissionPlanVo">
        SELECT p.id,
        p.fk_institution_provider_id,
        p.fk_institution_provider_contract_id,
        p.name,
        p.summary,
        p.remark,
        p.start_time,
        p.end_time,
        p.is_timeless,
        p.is_gobal,
        p.is_active,
        p.territory,
        p.territory_chn,
        p.course,
        p.course_chn,
        p.summary_chn,
        p.is_locked,
        p.approval_status,
        p.gmt_create_user,
        p.gmt_create,
        COUNT(r.fk_institution_id) AS institutionCount
        FROM m_institution_provider_commission_plan p
        LEFT JOIN r_institution_provider_commission_plan_institution r
        ON p.id = r.fk_institution_provider_commission_plan_id
        WHERE p.fk_institution_provider_contract_id = #{providerContractId}
        and p.id in
        <foreach item="item" collection="userPlanIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by p.id
        order by p.id desc
    </select>

    <select id="selectInstitutionProviderCommissionPlans"
            resultType="com.get.pmpcenter.vo.institution.InstitutionPlanVo">
        select id as planId,
        fk_institution_provider_id as institutionProviderId,
        fk_institution_provider_contract_id as contractId,
        name as planName,
        start_time,
        end_time,
        is_timeless,
        is_active
        from m_institution_provider_commission_plan where id in
        <foreach item="item" collection="planIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>

</mapper>
package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.CurrencyTypeMapper;
import com.get.financecenter.dao.ExpenseClaimAgentContentMapper;
import com.get.financecenter.dao.ExpenseClaimFormItemMapper;
import com.get.financecenter.dao.ProviderMapper;
import com.get.financecenter.dto.ExpenseClaimFormItemDto;
import com.get.financecenter.entity.ExpenseClaimAgentContent;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import com.get.financecenter.service.IExpenseClaimFeeTypeService;
import com.get.financecenter.service.IExpenseClaimFormItemService;
import com.get.financecenter.utils.RelationTargetProcessorUtils;
import com.get.financecenter.vo.ExpenseClaimFormItemVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.feign.ISaleCenterClient;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: Sea
 * @create: 2021/4/7 16:23
 * @verison: 1.0
 * @description:
 */
@Service
public class ExpenseClaimFormItemServiceImpl extends BaseServiceImpl<ExpenseClaimFormItemMapper, ExpenseClaimFormItem> implements IExpenseClaimFormItemService {
    @Resource
    private ExpenseClaimFormItemMapper expenseClaimFormItemMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IExpenseClaimFeeTypeService expenseClaimFeeTypeService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ProviderMapper providerMapper;
    @Resource
    private ExpenseClaimAgentContentMapper expenseClaimAgentContentMapper;
    @Resource
    private CurrencyTypeMapper currencyTypeMapper;
    @Resource
    private RelationTargetProcessorUtils relationTargetProcessorUtils;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ExpenseClaimFormItemDto> expenseClaimFormItemDtos) {
        if (GeneralTool.isEmpty(expenseClaimFormItemDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ExpenseClaimFormItemDto expenseClaimFormItemDto : expenseClaimFormItemDtos) {
            ExpenseClaimFormItem expenseClaimFormItem = BeanCopyUtils.objClone(expenseClaimFormItemDto, ExpenseClaimFormItem::new);
            utilService.updateUserInfoToEntity(expenseClaimFormItem);
            expenseClaimFormItemMapper.insert(expenseClaimFormItem);
        }
    }

    @Override
    public List<ExpenseClaimFormItemVo> getDtoByExpenseClaimFormId(Long id) {
        List<ExpenseClaimFormItem> expenseClaimFormItems = this.expenseClaimFormItemMapper.selectList(Wrappers.<ExpenseClaimFormItem>query().lambda()
                .eq(ExpenseClaimFormItem::getFkExpenseClaimFormId, id));
        //代理id集合
        Set<Long> agentIds = expenseClaimFormItems.stream().filter(item -> GeneralTool.isNotEmpty(item.getFkAgentId())).map(ExpenseClaimFormItem::getFkAgentId).collect(Collectors.toSet());
        Map<Long, String> agentNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            //feign调用查找代理名称map
            Result<Map<Long, String>> result1 = saleCenterClient.getAgentNamesByIds(agentIds);
            agentNameMap = result1.getData();
        }
        // 2021/9/16 费用类型名称改为获取Map 一次性sql查询
        List<ExpenseClaimFormItemVo> convertDatas = new ArrayList<>();
        for (ExpenseClaimFormItem expenseClaimFormItem : expenseClaimFormItems) {
            ExpenseClaimFormItemVo expenseClaimFormItemVo = BeanCopyUtils.objClone(expenseClaimFormItem, ExpenseClaimFormItemVo::new);
            //设置学生代理名称
            Long agentId = expenseClaimFormItemVo.getFkAgentId();
            expenseClaimFormItemVo.setAgentName(agentNameMap.get(agentId));
            //费用类型名称
            expenseClaimFormItemVo.setExpenseClaimFeeTypeName(expenseClaimFeeTypeService.getExpenseClaimFeeTypeNameById(expenseClaimFormItemVo.getFkExpenseClaimFeeTypeId()));
            StringBuilder relationTargetName = new StringBuilder();
//            if (RelationTargetKeyEnum.STAFF.relationTargetKey.equals(expenseClaimFormItemVo.getRelationTargetKey())) {
//                relationTargetName.append(RelationTargetKeyEnum.STAFF.name).append(":");
//                String staffName = permissionCenterClient.getStaffName(expenseClaimFormItemVo.getRelationTargetId()).getData();
//                relationTargetName.append(staffName);
//            } else if (RelationTargetKeyEnum.PROVIDER.relationTargetKey.equals(expenseClaimFormItemVo.getRelationTargetKey())) {
//                relationTargetName.append(RelationTargetKeyEnum.PROVIDER.name).append(":");
//                Provider provider = providerMapper.selectById(expenseClaimFormItemVo.getRelationTargetId());
//                relationTargetName.append(provider.getName());
//            }
//            else if (RelationTargetKeyEnum.STUDENT.relationTargetKey.equals(expenseClaimFormItemVo.getRelationTargetKey())) {
//                relationTargetName.append(RelationTargetKeyEnum.STUDENT.name).append(":");
//                Student student = saleCenterClient.getStudentById(expenseClaimFormItemVo.getRelationTargetId()).getData();
//                relationTargetName.append(student.getName());
//
//            }else if (RelationTargetKeyEnum.STUDENT_SERVICE_FEE.relationTargetKey.equals(expenseClaimFormItemVo.getRelationTargetKey())){
//                relationTargetName.append(RelationTargetKeyEnum.STUDENT_SERVICE_FEE.name).append(":");
//                String studentServiceFeeNum = saleCenterClient.getServiceFeeNumById(expenseClaimFormItemVo.getRelationTargetId()).getData();
//                relationTargetName.append(studentServiceFeeNum);
//            }
//            expenseClaimFormItemVo.setRelationTargetName(relationTargetName.toString());
            relationTargetProcessorUtils.processRelationTarget(
                    expenseClaimFormItemVo.getRelationTargetKey(),
                    expenseClaimFormItemVo.getRelationTargetId(),
                    null,
                    expenseClaimFormItemVo::setRelationTargetCompanyId,
                    expenseClaimFormItemVo::setRelationTargetName
            );




            StringBuilder detail = new StringBuilder();
            if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getFkAgentId())) {
                Agent agent = saleCenterClient.getAgentById(expenseClaimFormItemVo.getFkAgentId()).getData();
                detail.append("Subagent:").append(agent.getNum()).append(" (").append(agent.getName()).append(")");
            }
            if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getFkExpenseClaimAgentContentId())) {
                ExpenseClaimAgentContent expenseClaimAgentContent = expenseClaimAgentContentMapper.selectById(expenseClaimFormItemVo.getFkExpenseClaimAgentContentId());
                detail.append("内容");
                if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getPeopleCount())) {
                    detail.append("/人数:").append(expenseClaimAgentContent.getName()).append("/").append(expenseClaimFormItemVo.getPeopleCount());
                } else {
                    detail.append(":").append(expenseClaimAgentContent.getName());
                }
            } else if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getPeopleCount())){
                detail.append("人数:").append(expenseClaimFormItemVo.getPeopleCount());
            }
            expenseClaimFormItemVo.setDetail(detail.toString());
            String currencyName = currencyTypeMapper.getCurrencyNameByNum(expenseClaimFormItemVo.getFkCurrencyTypeNum());
            expenseClaimFormItemVo.setFkCurrencyTypeName(expenseClaimFormItemVo.getFkCurrencyTypeNum() + "(" + currencyName + ")");
            convertDatas.add(expenseClaimFormItemVo);
        }
        return convertDatas;
    }

    @Override
    public void deleteByFkid(Long expenseClaimFormId) {
//        Example example = new Example(ExpenseClaimFormItem.class);
//        example.createCriteria().andEqualTo("fkExpenseClaimFormId",expenseClaimFormId);
//        expenseClaimFormItemMapper.deleteByExample(example);
        this.expenseClaimFormItemMapper.delete(Wrappers.<ExpenseClaimFormItem>query().lambda().eq(ExpenseClaimFormItem::getFkExpenseClaimFormId, expenseClaimFormId));

    }
}

package com.von.ware.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.von.common.to.mq.OrderTo;
import com.von.common.to.mq.StockLockedTo;
import com.von.common.utils.PageUtils;
import com.von.common.utils.R;
import com.von.common.vo.SkuHasStockVo;
import com.von.ware.entity.PurchaseDetailEntity;
import com.von.ware.entity.WareSkuEntity;
import com.von.ware.vo.WareSkuLockVo;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 商品库存
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-10-08 23:45:29
 */
public interface WareSkuService extends IService<WareSkuEntity> {

    void unlockStock(StockLockedTo stockLockedTo) throws IOException;

    PageUtils queryPage(Map<String, Object> params);

    void addStock(List<PurchaseDetailEntity> finishDetailList);

    R test();

    List<SkuHasStockVo> getSkuHasStock(List<Long> skuIds);

    Boolean orderLockStock(WareSkuLockVo wareSkuLockVo);

    void unlockStock(OrderTo orderTo);
}


package com.get.remindercenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.service.EmailTemplateService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class EmailTemplateServiceImpl implements EmailTemplateService {

        @Resource
        private EmailTemplateMapper emailTemplateMapper;

    @Override
    public EmailTemplate getEmailTemplateByTypeKey(String typeKey) {
        return emailTemplateMapper.selectOne(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, typeKey));
    }
}

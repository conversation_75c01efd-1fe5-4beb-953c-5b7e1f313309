package com.get.aisplatformcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.ULabelMapper;
import com.get.aisplatformcenter.service.ULabelService;
import com.get.aisplatformcenter.service.ULabelTypeService;
import com.get.aisplatformcenterap.dto.ULabelDto;
import com.get.aisplatformcenterap.entity.ULabelEntity;
import com.get.aisplatformcenterap.vo.LabelAboutAgentVo;
import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.ULabelVo;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.dto.AgentLabelDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class ULabelServiceImpl extends ServiceImpl<ULabelMapper, ULabelEntity>
        implements ULabelService {

    @Resource
    private ULabelMapper uLabelMapper;

    @Resource
    private ULabelTypeService uLabelTypeService;

    @Resource
    private UtilService utilService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;


    @Override
    public List<ULabelVo> searchPage(ULabelDto data, Page page) {
        LambdaQueryWrapper<ULabelEntity> wrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(data.getFkLabelTypeId())){
            wrapper.eq(ULabelEntity::getFkLabelTypeId,data.getFkLabelTypeId());
        }
        if(GeneralTool.isNotEmpty(data.getIsActive())){
            wrapper.eq(ULabelEntity::getIsActive,data.getIsActive());
        }
        if(GeneralTool.isNotEmpty(data.getLabelName())){
            wrapper.like(ULabelEntity::getLabelName,data.getLabelName());
        }
        //labelKey
        if(GeneralTool.isNotEmpty(data.getLabelKey())){
            wrapper.like(ULabelEntity::getLabelKey,data.getLabelKey());
        }
        if(GeneralTool.isNotEmpty(data.getRemark())){
            wrapper.like(ULabelEntity::getRemark,data.getRemark());
        }
        wrapper.orderByDesc(ULabelEntity::getViewOrder);
        IPage<ULabelEntity> pages = uLabelMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);


        List<ULabelEntity> uLabels = pages.getRecords();
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(uLabels)) {
            return Collections.emptyList();
        }
        List<ULabelVo> uLabelVos = new ArrayList<>();
        for (ULabelEntity uLabel:uLabels){
            ULabelVo uLabelVo = BeanCopyUtils.objClone(uLabel, ULabelVo::new);
            uLabelVos.add(uLabelVo);
        }

        return uLabelVos;
    }

    @Override
    public Long addULabelType(ULabelDto uLabelDto) {
        if (uLabelDto == null) {
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","insert_vo_null!"));
        }
        ULabelEntity uLabelEntity = BeanCopyUtils.objClone(uLabelDto, ULabelEntity::new);
        //判断标签类型是否存在
        if(uLabelTypeService.findULabelTypeById(uLabelDto.getFkLabelTypeId())==null){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","insert_vo_null!"));
        }
        check(uLabelDto,"null");
        uLabelEntity.setViewOrder(uLabelMapper.getMaxOrder());
        utilService.updateUserInfoToEntity(uLabelEntity);
        int i = uLabelMapper.insert(uLabelEntity);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return uLabelEntity.getId();
    }

    @Override
    public ULabelVo updateULabel(ULabelDto uLabelDto) {
        if (uLabelDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ULabelEntity result = uLabelMapper.selectById(uLabelDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        check(uLabelDto,"update");
        ULabelEntity uLabelEntity = BeanCopyUtils.objClone(uLabelDto, ULabelEntity::new);
        utilService.updateUserInfoToEntity(uLabelEntity);
        uLabelMapper.updateById(uLabelEntity);
        return findULabelById(uLabelEntity.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (uLabelMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //校验该类型是否绑定相关联系人
        Integer count = uLabelMapper.getAgentLabel(id);
        if (count > 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        uLabelMapper.deleteById(id);
    }

    @Override
    public ULabelVo findULabelById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ULabelEntity uLabelEntity = uLabelMapper.selectById(id);
        ULabelVo uLabelVo = BeanCopyUtils.objClone(uLabelEntity, ULabelVo::new);
        return uLabelVo;
    }

    @Override
    public void sortULabel(List<ULabelDto> uLabelDtoList) {
        if (GeneralTool.isEmpty(uLabelDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ULabelEntity moveDataOne = BeanCopyUtils.objClone(uLabelDtoList.get(0), ULabelEntity::new);
        ULabelEntity uLabelEntityMoveDataOne = uLabelMapper.selectById(moveDataOne.getId());
        if (GeneralTool.isNotEmpty(uLabelEntityMoveDataOne) && !uLabelEntityMoveDataOne.getViewOrder().equals(moveDataOne.getViewOrder()) ){
            throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
        }
        Integer oneViewOrder = moveDataOne.getViewOrder();
        ULabelEntity moveDataTwo = BeanCopyUtils.objClone(uLabelDtoList.get(1), ULabelEntity::new);
        ULabelEntity uLabelEntityMoveDataTwo = uLabelMapper.selectById(moveDataTwo.getId());
        if (GeneralTool.isNotEmpty(uLabelEntityMoveDataTwo) && !uLabelEntityMoveDataTwo.getViewOrder().equals(moveDataTwo.getViewOrder()) ){
            throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
        }
        Integer twoViewOrder = moveDataTwo.getViewOrder();
        moveDataOne.setViewOrder(twoViewOrder);
        utilService.updateUserInfoToEntity(moveDataOne);
        moveDataTwo.setViewOrder(oneViewOrder);
        utilService.updateUserInfoToEntity(moveDataTwo);
        uLabelMapper.updateById(moveDataOne);
        uLabelMapper.updateById(moveDataTwo);
    }


    @Override
    public LabelSearchAboutAgentVo getLabelByLabelTypeIdAndKeyWord(AgentLabelDto agentLabelDto, Page page) {

        LabelSearchAboutAgentVo labelSearchAboutAgentVo = new LabelSearchAboutAgentVo();
//        List<LabelAboutAgentVo> convertDatas = new ArrayList<>();

//        //获取业务下属
//        Long staffId = SecureUtil.getStaffId();
//        //员工id + 业务下属员工ids
//        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
//        staffFollowerIds.add(staffId);
        List<LabelAboutAgentVo> labelAboutAgentVos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(page)) {
            labelAboutAgentVos = uLabelMapper.getLabelByLabelTypeIdAndKeyWord(null, agentLabelDto);
        } else {
            IPage<LabelAboutAgentVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            //获取分页数据
            labelAboutAgentVos = uLabelMapper.getLabelByLabelTypeIdAndKeyWord(iPage, agentLabelDto);
            page.setAll((int) iPage.getTotal());
        }
        if (GeneralTool.isEmpty(labelAboutAgentVos)) {
            return new LabelSearchAboutAgentVo();
        }

        labelSearchAboutAgentVo.setLabelAboutAgentVoList(labelAboutAgentVos);
        labelSearchAboutAgentVo.setPage(page);
        return labelSearchAboutAgentVo;

    }


    public void check(ULabelDto uLabelDto,String type) {
        LambdaQueryWrapper<ULabelEntity> wrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(uLabelDto.getLabelName())){
            wrapper.eq(ULabelEntity::getLabelName,uLabelDto.getLabelName());
            if(GeneralTool.isNotEmpty(type)&&type.equals("update")){
                wrapper.ne(ULabelEntity::getId,uLabelDto.getId());
                wrapper.eq(ULabelEntity::getFkLabelTypeId,uLabelDto.getFkLabelTypeId());
            }else {
                wrapper.eq(ULabelEntity::getFkLabelTypeId,uLabelDto.getFkLabelTypeId());
            }
        }

        if(GeneralTool.isNotEmpty(uLabelMapper.selectList(wrapper))){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
        }
        LambdaQueryWrapper<ULabelEntity> wrapper1 = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(uLabelDto.getLabelKey())){
            wrapper1.eq(ULabelEntity::getLabelKey,uLabelDto.getLabelKey());
            if(GeneralTool.isNotEmpty(type)&&type.equals("update")){
                wrapper1.ne(ULabelEntity::getId,uLabelDto.getId());
                wrapper1.eq(ULabelEntity::getFkLabelTypeId,uLabelDto.getFkLabelTypeId());
            }else {
                wrapper1.eq(ULabelEntity::getFkLabelTypeId,uLabelDto.getFkLabelTypeId());
            }
        }
        if(GeneralTool.isNotEmpty(uLabelMapper.selectList(wrapper1))){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_key_exists"));
        }
    }


}

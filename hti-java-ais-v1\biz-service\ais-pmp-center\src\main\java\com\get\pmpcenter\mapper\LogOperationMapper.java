package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.pmpcenter.dto.common.LogRecordDto;
import com.get.pmpcenter.entity.LogOperation;
import com.get.pmpcenter.vo.common.LogRecordVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/28  11:59
 * @Version 1.0
 */
@Mapper
public interface LogOperationMapper extends BaseMapper<LogOperation> {

    /**
     * 查询学校提供商合同的操作日志
     *
     * @param id
     * @return
     */
    List<LogRecordVo> selectProviderLogs(@Param("id") Long id);


    /**
     * 查询代理合同的操作日志
     *
     * @param id
     * @return
     */
    List<LogRecordVo> selectAgentLogs(@Param("id") Long id);



    /**
     * 日志列表
     *
     * @param page
     * @param param
     * @return
     */
    List<LogRecordVo> commissionLogRecordPage(IPage<LogRecordVo> page, @Param("param") LogRecordDto param);
}

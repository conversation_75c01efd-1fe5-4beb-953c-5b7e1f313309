<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.von.coupon.dao.UndoLogDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.von.coupon.entity.UndoLogEntity" id="undoLogMap">
        <result property="id" column="id"/>
        <result property="branchId" column="branch_id"/>
        <result property="xid" column="xid"/>
        <result property="context" column="context"/>
        <result property="rollbackInfo" column="rollback_info"/>
        <result property="logStatus" column="log_status"/>
        <result property="logCreated" column="log_created"/>
        <result property="logModified" column="log_modified"/>
        <result property="ext" column="ext"/>
    </resultMap>


</mapper>
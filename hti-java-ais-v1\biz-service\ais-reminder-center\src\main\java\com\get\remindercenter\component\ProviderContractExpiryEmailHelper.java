package com.get.remindercenter.component;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionProviderContractReminderVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.ProviderContractExpiryDto;
import com.get.remindercenter.dto.SummitRegistrationReminderDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Component("providerContractExpiryEmailHelper")
@Slf4j
public class ProviderContractExpiryEmailHelper extends EmailAbstractHelper{

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        //获取配置信息
        String value1 = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.REMINDER_EMAIL_PROVIDER_CONTRACT_EXPIRATION.key).getData();
        //从配置信息获取收件人id
        List<Long> staffIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(value1)) {
            cn.hutool.json.JSONObject configJSON = JSONUtil.parseObj(value1);
            JSONArray staffIdsArray = configJSON.getJSONArray("Notifier");

            // 同样地，处理staffIdsArray并将数据添加到staffIds列表中
            if (staffIdsArray != null) {
                for (int i = 0; i < staffIdsArray.size(); i++) {
                    staffIds.add(staffIdsArray.getLong(i)); // 假设Notifier包含的是Long类型的数据
                }
            }

        }

            try {
                ProviderContractExpiryDto providerContractExpiryDto = assembleEmailData(emailSenderQueue);
                if (GeneralTool.isNotEmpty(providerContractExpiryDto.getStaffEmailSet())) {
                    Set<Long> staffIdSet = staffIds.stream().collect(Collectors.toSet());
                    providerContractExpiryDto.getStaffEmailSet().addAll(staffIdSet);
                }
                String template = setEmailTemplate(providerContractExpiryDto);
                if (GeneralTool.isEmpty(template)) {
                    log.error("邮箱模板内容为空，需要配置邮箱模板");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
                }
                StringJoiner emailsCombined = new StringJoiner(", ");

                //根据发送人id获取发送人邮箱
                Map<Long, Staff> data = permissionCenterClient.getStaffMapByStaffIds(providerContractExpiryDto.getStaffEmailSet()).getData();
                for (Long id : providerContractExpiryDto.getStaffEmailSet()) {
                    try {
                    EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                    emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
                    emailSystemMQMessageDto.setTitle(providerContractExpiryDto.getEmailTitle());
                    emailSystemMQMessageDto.setContent(template);
                    emailSystemMQMessageDto.setToEmail(data.get(id).getEmail());
                    //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                    //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                        remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    if (GeneralTool.isNotEmpty(data.get(id).getEmail())) {
                        emailsCombined.add(data.get(id).getEmail());
                    }
                    }catch (Exception e){
                        // 记录发送失败的邮箱
                        String failedEmail = data.get(id) != null &&data.get(id).getEmail() != null ? data.get(id).getEmail() : "staffId:" + id;
                        failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                        log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}",  id, failedEmail, e.getMessage());
                    }
                }
                emailSenderQueue.setEmailTo(emailsCombined.toString());
                LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                        .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
                // 如果 failedEmails 不为空，则额外更新 errorMessage
                if (failedEmails.length() > 0) {
                    updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
                }
                emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
            } catch (Exception e) {
                log.error("ProviderContractExpiryEmailHelper error:{}", e);
                failedEmails.add("邮件发送异常"+e.getMessage());
                emailSenderQueue.setErrorMessage(failedEmails.toString());
                emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
                emailSenderQueue.setOperationStatus(-1);
                emailSenderQueueMapper.updateById(emailSenderQueue);
            }
        }



    public ProviderContractExpiryDto assembleEmailData(EmailSenderQueue emailSenderQueue ) {
        ProviderContractExpiryDto reminderDto = new ProviderContractExpiryDto();
        //获取到期天数
        String day = emailSenderQueue.getEmailParameter();
        Integer count = null;
        if(GeneralTool.isNotEmpty( day)){
             count = Integer.parseInt(day);
        }
        Map<String, String> map = new HashMap<>();
        BeanUtils.copyProperties(emailSenderQueue, reminderDto);
        //根据合同id获取学校提供商和合同信息
        InstitutionProviderContractReminderVo providerContractReminderVo =institutionCenterClient.getContractExpiredByProviderId(emailSenderQueue.getFkTableId());
        //获取创建人
        Long creatorEmail = providerContractReminderVo.getGmtCreateUserId();
        //获取审批人
        Long fkStaffId = providerContractReminderVo.getFkStaffId();
        //获取审批人直属上司
        Long supervisors = permissionCenterClient.getStaffSupervisorIdByStaffId(fkStaffId).getData();

        if (GeneralTool.isNotEmpty(creatorEmail)) {
            reminderDto.getStaffEmailSet().add(creatorEmail);
        }
        if (GeneralTool.isNotEmpty(supervisors)) {
            reminderDto.getStaffEmailSet().add(supervisors);
        }
        if (GeneralTool.isNotEmpty(fkStaffId)) {
            reminderDto.getStaffEmailSet().add(fkStaffId);
        }

        //根据创建人的公司区分中英文
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        //String versionValue2 = versionConfigMap.get();
        String versionValue2 = "zh";
        //设置标题
        String title1 = "";
        String taskType = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if(!versionValue2.equals("en")){
            title1 = "【合同到期提醒】"+providerContractReminderVo.getFullName()+"("
                    + sdf.format(providerContractReminderVo.getStartTime()) + "至" + sdf.format(providerContractReminderVo.getEndTime())
                    + "，提前" + count + "天提醒)";
        }else {
            title1 = "[Contract expiration reminder]"+providerContractReminderVo.getName() + "("
                    + sdf.format(providerContractReminderVo.getStartTime()) + "TO" + sdf.format(providerContractReminderVo.getEndTime())
                    + ",Remind" + count + "days in advance)";
        }
        String name = providerContractReminderVo.getFullName();
        map.put("name", name);
        map.put("startTime",sdf.format(providerContractReminderVo.getStartTime()));
        map.put("endTime",sdf.format(providerContractReminderVo.getEndTime()));
        map.put("count",day);
        map.put("contractTitle",providerContractReminderVo.getContractName());
        reminderDto.setMap(map);
        reminderDto.setLanguageCode(versionValue2);
        reminderDto.setEmailTitle(title1);
        //插入标题
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
        //根据学校提供商
        return reminderDto;
    }

    private String setEmailTemplate(ProviderContractExpiryDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.PROVIDER_CONTRACT_EXPIRE.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.PROVIDER_CONTRACT_EXPIRE.getEmailTemplateKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }

}

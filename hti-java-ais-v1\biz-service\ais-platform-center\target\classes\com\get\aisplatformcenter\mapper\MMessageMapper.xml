<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.MMessageMapper">

    <resultMap id="BaseResultMap" type="com.get.aisplatformcenterap.entity.MMessageEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="fkPlatformId" column="fk_platform_id" jdbcType="BIGINT"/>
            <result property="fkPlatformCode" column="fk_platform_code" jdbcType="VARCHAR"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="weight" column="weight" jdbcType="INTEGER"/>
            <result property="jumpMode" column="jump_mode" jdbcType="INTEGER"/>
            <result property="jumpUrl" column="jump_url" jdbcType="VARCHAR"/>
            <result property="webTitle" column="web_title" jdbcType="VARCHAR"/>
            <result property="webMetaDescription" column="web_meta_description" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="searchPage" resultType="com.get.aisplatformcenterap.vo.MMessageVo">
        <if test="query.status==null or (query.status!=null and query.status==2)">
            SELECT a.* FROM (
                SELECT
                    message.id,
                    message.fk_company_id,
                    message.fk_platform_id,
                    message.fk_platform_code,
                    message.title,
                    message.weight,
                    message.jump_mode,
                    message.jump_url,
                    message.web_title,
                    message.web_meta_description,
                    message.start_time,
                    message.end_time,
                    message.remark,
                    message.status,
                    message.gmt_create,
                    message.gmt_create_user,
                    message.gmt_modified,
                    message.gmt_modified_user,
                    platform.name  AS  platFormName,
                    company.num AS companyName
                    FROM ais_platform_center.m_message message INNER JOIN ais_platform_center.m_platform platform ON message.fk_platform_id=platform.id
                    LEFT JOIN ais_permission_center.m_company company ON message.fk_company_id=company.id
                    WHERE message.status=2
                    <if test="query.fkCompanyId != null ">
                        AND message.fk_company_id=#{query.fkCompanyId}
                    </if>
                    <if test="query.fkPlatformId != null ">
                        AND message.fk_platform_id=#{query.fkPlatformId}
                    </if>
                    <if test="query.title!=null and query.title!='' ">
                        AND position(#{query.title,jdbcType=VARCHAR} in message.title)
                    </if>



                ORDER BY message.weight DESC  limit 1000
            ) a
        </if>
        <if test="query.status==null">
            UNION ALL
        </if>
        <if test="query.status==null or (query.status!=null and query.status==1)">
            SELECT b.* FROM (SELECT
                message.id,
                message.fk_company_id,
                message.fk_platform_id,
                message.fk_platform_code,
                message.title,
                message.weight,
                message.jump_mode,
                message.jump_url,
                message.web_title,
                message.web_meta_description,
                message.start_time,
                message.end_time,
                message.remark,
                message.status,
                message.gmt_create,
                message.gmt_create_user,
                message.gmt_modified,
                message.gmt_modified_user,
                platform.name platFormName,
                company.num AS companyName
            FROM ais_platform_center.m_message message INNER JOIN ais_platform_center.m_platform platform ON message.fk_platform_id=platform.id
            LEFT JOIN ais_permission_center.m_company company ON message.fk_company_id=company.id
            WHERE message.status=1
            <if test="query.fkCompanyId != null ">
                AND message.fk_company_id=#{query.fkCompanyId}
            </if>
            <if test="query.fkPlatformId != null ">
                AND message.fk_platform_id=#{query.fkPlatformId}
            </if>
            <if test="query.title!=null and query.title!='' ">
                AND position(#{query.title,jdbcType=VARCHAR} in message.title)
            </if>
            ORDER BY message.start_time  limit 1000
            ) b
        </if>
        <if test="query.status==null">
            UNION ALL
        </if>
        <if test="query.status==null or  (query.status!=null and query.status==0)">
            SELECT c.* FROM (SELECT
                 message.id,
                 message.fk_company_id,
                 message.fk_platform_id,
                 message.fk_platform_code,
                 message.title,
                 message.weight,
                 message.jump_mode,
                 message.jump_url,
                 message.web_title,
                 message.web_meta_description,
                 message.start_time,
                 message.end_time,
                 message.remark,
                 message.status,
                 message.gmt_create,
                 message.gmt_create_user,
                 message.gmt_modified,
                 message.gmt_modified_user,
                 platform.name platFormName,
                 company.num AS companyName
             FROM ais_platform_center.m_message message INNER JOIN ais_platform_center.m_platform platform ON message.fk_platform_id=platform.id
             LEFT JOIN ais_permission_center.m_company company ON message.fk_company_id=company.id
             WHERE message.status=0
             <if test="query.fkCompanyId != null ">
                AND message.fk_company_id=#{query.fkCompanyId}
             </if>
             <if test="query.fkPlatformId != null ">
                AND message.fk_platform_id=#{query.fkPlatformId}
             </if>
             <if test="query.title!=null and query.title!='' ">
                AND position(#{query.title,jdbcType=VARCHAR} in message.title)
             </if>
            ORDER BY message.end_time DESC  limit 1000
            ) c
        </if>

    </select>


    <update id="updateByIds">
        UPDATE m_message
        <if test="type!=null and type==1">
            SET status=0
        </if>
        <if test="type!=null and type==2">
            SET status=2
        </if>
            WHERE
        id IN
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>

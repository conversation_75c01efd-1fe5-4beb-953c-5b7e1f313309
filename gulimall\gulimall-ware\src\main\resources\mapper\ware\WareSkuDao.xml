<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.von.ware.dao.WareSkuDao">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.von.ware.entity.WareSkuEntity" id="wareSkuMap">
        <result property="id" column="id"/>
        <result property="skuId" column="sku_id"/>
        <result property="wareId" column="ware_id"/>
        <result property="stock" column="stock"/>
        <result property="skuName" column="sku_name"/>
        <result property="stockLocked" column="stock_locked"/>
    </resultMap>

    <!--  更新库存数量  -->
    <update id="updateStock">
        UPDATE wms_ware_sku
        SET stock = stock + #{wareSkuEntity.stock}
        WHERE
            sku_id = #{wareSkuEntity.skuId}
        AND ware_id = #{wareSkuEntity.wareId}
    </update>

    <!--    库存锁定-->
    <update id="lockSkuStock">
        UPDATE wms_ware_sku
        SET stock_locked = stock_locked + #{num}
        WHERE
            sku_id = #{skuId}
        AND ware_id = #{wareId}
        AND stock - wms_ware_sku.stock_locked >= #{num}
    </update>
    <update id="unLockStock">
        UPDATE wms_ware_sku
        SET stock_locked = stock_locked - #{skuNum}
        WHERE
            sku_id = #{skuId}
        AND ware_id = #{wareId}
    </update>

    <select id="getSkuHasStock" resultType="java.lang.Long">
        SELECT
            SUM(stock - wms_ware_sku.stock_locked)
        FROM
            wms_ware_sku
        WHERE
            sku_id = #{skuId}
    </select>
    <select id="listWareIdHasSkuStock" resultType="java.lang.Long">
        SELECT
            ware_id
        FROM
            gulimall_wms.wms_ware_sku
        WHERE
            sku_id = #{skuId}
        AND stock - stock_locked > 0
    </select>


</mapper>
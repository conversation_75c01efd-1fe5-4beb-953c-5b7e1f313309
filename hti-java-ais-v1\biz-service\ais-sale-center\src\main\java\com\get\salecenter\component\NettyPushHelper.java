package com.get.salecenter.component;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.salecenter.config.NettyPushConfig;
import com.get.salecenter.vo.StudentCountVo;
import com.get.salecenter.service.IStudentOfferItemService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 首页推送帮助类
 *
 * <AUTHOR>
 * @date 2021/11/17 16:18
 */
@Component
public class NettyPushHelper {
    @Resource
    @Lazy
    private IStudentOfferItemService offerItemService;

    public Boolean nettyPush(Long itemId) {
//        //主动推送的数据 111111
//        ConcurrentHashMap<String, List<StudentCountVo>> pushFlagMap = NettyPushConfig.getPushFlagMap();
//        List<StudentCountVo> studentCountVos = pushFlagMap.get(ProjectKeyEnum.PUSH_FLAG.key);
//        if (GeneralTool.isNotEmpty(studentCountVos)) {
//            studentCountVos.add(iStudentOfferItemService.getStudentCountRecord(itemId));
//        } else {
//            studentCountVos = new ArrayList<>();
//            studentCountVos.add(iStudentOfferItemService.getStudentCountRecord(itemId));
//        }
//        pushFlagMap.put(ProjectKeyEnum.PUSH_FLAG.key, studentCountVos);

        //主动推送的数据
        ConcurrentHashMap<String, List<StudentCountVo>> pushFlagMap = NettyPushConfig.getPushFlagMap();
        List<StudentCountVo> studentCountVos = pushFlagMap.get(ProjectKeyEnum.PUSH_FLAG.key);
        if (GeneralTool.isNotEmpty(studentCountVos)) {
            studentCountVos.add(offerItemService.getStudentCountRecord(itemId));
        } else {
            studentCountVos = new ArrayList<>();
            studentCountVos.add(offerItemService.getStudentCountRecord(itemId));
        }
        pushFlagMap.put(ProjectKeyEnum.PUSH_FLAG.key, studentCountVos);
        return true;
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.resumecenter.dao.ResumeSkillMapper">
    <resultMap id="BaseResultMap" type="com.get.resumecenter.entity.ResumeSkill">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_resume_id" jdbcType="BIGINT" property="fkResumeId"/>
        <result column="fk_skill_type_id" jdbcType="BIGINT" property="fkSkillTypeId"/>
        <result column="skill_level" jdbcType="VARCHAR" property="skillLevel"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_resume_id, fk_skill_type_id, skill_level, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
  </sql>

    <insert id="insertSelective" parameterType="com.get.resumecenter.entity.ResumeSkill" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_resume_skill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkResumeId != null">
                fk_resume_id,
            </if>
            <if test="fkSkillTypeId != null">
                fk_skill_type_id,
            </if>
            <if test="skillLevel != null">
                skill_level,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkResumeId != null">
                #{fkResumeId,jdbcType=BIGINT},
            </if>
            <if test="fkSkillTypeId != null">
                #{fkSkillTypeId,jdbcType=BIGINT},
            </if>
            <if test="skillLevel != null">
                #{skillLevel,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="isExistBySkillTypeId" resultType="java.lang.Boolean">
      SELECT IFNULL(max(id),0) id from m_resume_skill where fk_skill_type_id=#{skillTypeId}
    </select>
</mapper>
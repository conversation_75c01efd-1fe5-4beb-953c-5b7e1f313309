package com.get.pmpcenter.event.publisher;

import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.event.CommissionChangedEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:17
 * @Version 1.0
 * 佣金事件发布者
 */
@Component
public class CommissionChangedEventPublisher {

    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    public CommissionChangedEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void publishCommissionChangedEvent(List<LogDto> logs) {
        CommissionChangedEvent event = new CommissionChangedEvent(this,logs);
        eventPublisher.publishEvent(event);
    }
}

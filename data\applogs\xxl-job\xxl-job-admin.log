2025-07-13 11:40:06,621 INFO [main] c.get.common.start.StartServiceImpl [StartServiceImpl.java : 39] 成功从von.properties加载配置
2025-07-13 11:40:07,907 INFO [background-preinit] o.h.validator.internal.util.Version [Version.java : 21] HV000001: Hibernate Validator 6.1.7.Final
2025-07-13 11:40:10,971 INFO [main] c.a.n.c.c.i.LocalConfigInfoProcessor [LocalConfigInfoProcessor.java : 67] LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
2025-07-13 11:40:10,984 INFO [main] c.alibaba.nacos.common.remote.client [RpcClientFactory.java : 77] [RpcClientFactory] create a new rpc client of 58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0
2025-07-13 11:40:11,184 INFO [main] org.reflections.Reflections [Reflections.java : 232] Reflections took 114 ms to scan 1 urls, producing 3 keys and 6 values 
2025-07-13 11:40:11,268 INFO [main] org.reflections.Reflections [Reflections.java : 232] Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
2025-07-13 11:40:11,287 INFO [main] org.reflections.Reflections [Reflections.java : 232] Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
2025-07-13 11:40:11,494 INFO [main] org.reflections.Reflections [Reflections.java : 232] Reflections took 199 ms to scan 260 urls, producing 0 keys and 0 values 
2025-07-13 11:40:11,506 INFO [main] org.reflections.Reflections [Reflections.java : 232] Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
2025-07-13 11:40:11,529 INFO [main] org.reflections.Reflections [Reflections.java : 232] Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
2025-07-13 11:40:11,544 INFO [main] org.reflections.Reflections [Reflections.java : 232] Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
2025-07-13 11:40:11,762 INFO [main] org.reflections.Reflections [Reflections.java : 232] Reflections took 213 ms to scan 260 urls, producing 0 keys and 0 values 
2025-07-13 11:40:11,766 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0]RpcClient init label, labels={module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
2025-07-13 11:40:11,768 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$364/1012902452
2025-07-13 11:40:11,769 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0]Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$365/931647450
2025-07-13 11:40:11,773 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0]Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
2025-07-13 11:40:11,775 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0]RpcClient init, ServerListFactory =com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
2025-07-13 11:40:11,812 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0] Try to connect to server on start up, server: {serverIp='localhost', server main port=18848}
2025-07-13 11:40:15,591 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0] Success to connect to server [localhost:18848] on start up,connectionId=1752378056777_172.18.0.1_52137
2025-07-13 11:40:15,593 INFO [com.alibaba.nacos.client.remote.worker] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0]Notify connected event to listeners.
2025-07-13 11:40:15,594 INFO [com.alibaba.nacos.client.remote.worker] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 677] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0] Connected,notify listen context...
2025-07-13 11:40:15,595 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-13 11:40:15,597 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [58e2f332-0c9f-4f7d-860c-1fc3d8f0e492_config-0]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
2025-07-13 11:40:15,835 INFO [main] c.a.nacos.client.config.impl.Limiter [Limiter.java : 56] limitTime:5.0
2025-07-13 11:40:15,898 INFO [main] c.a.n.client.config.utils.JvmUtil [JvmUtil.java : 53] isMultiInstance:false
2025-07-13 11:40:15,959 WARN [main] c.a.c.n.c.NacosPropertySourceBuilder [NacosPropertySourceBuilder.java : 87] Ignore the empty nacos configuration and get it based on dataId[xxljob-admin] & group[local]
2025-07-13 11:40:15,966 WARN [main] c.a.c.n.c.NacosPropertySourceBuilder [NacosPropertySourceBuilder.java : 87] Ignore the empty nacos configuration and get it based on dataId[xxljob-admin.yaml] & group[local]
2025-07-13 11:40:15,975 WARN [main] c.a.c.n.c.NacosPropertySourceBuilder [NacosPropertySourceBuilder.java : 87] Ignore the empty nacos configuration and get it based on dataId[xxljob-admin-dev.yaml] & group[local]
2025-07-13 11:40:15,983 WARN [main] c.a.c.n.c.NacosPropertySourceBuilder [NacosPropertySourceBuilder.java : 87] Ignore the empty nacos configuration and get it based on dataId[xxljob-admin-local.yaml] & group[local]
2025-07-13 11:40:15,985 INFO [main] o.s.c.b.c.PropertySourceBootstrapConfiguration [PropertySourceBootstrapConfiguration.java : 112] Located property source: [BootstrapPropertySource {name='bootstrapProperties-xxljob-admin-local.yaml,local'}, BootstrapPropertySource {name='bootstrapProperties-xxljob-admin-dev.yaml,local'}, BootstrapPropertySource {name='bootstrapProperties-xxljob-admin.yaml,local'}, BootstrapPropertySource {name='bootstrapProperties-xxljob-admin,local'}, BootstrapPropertySource {name='bootstrapProperties-xxljob-admin-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-get-local.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-get.yaml,DEFAULT_GROUP'}]
2025-07-13 11:40:15,996 INFO [main] c.xxl.job.admin.JobAdminApplication [SpringApplication.java : 652] The following profiles are active: dev,local
2025-07-13 11:40:19,150 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 249] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-13 11:40:19,160 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 127] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-13 11:40:19,278 INFO [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 187] Finished Spring Data repository scanning in 29ms. Found 0 Redis repository interfaces.
2025-07-13 11:40:19,509 WARN [main] o.s.boot.actuate.endpoint.EndpointId [EndpointId.java : 155] Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-13 11:40:20,263 INFO [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 295] BeanFactory id=af294e24-3aed-3315-b564-179c4657f89e
2025-07-13 11:40:20,269 INFO [main] c.g.c.s.p.GetPropertySourcePostProcessor [GetPropertySourcePostProcessor.java : 134] getPropertySourcePostProcessor init.
2025-07-13 11:40:20,269 INFO [main] c.g.c.s.p.GetPropertySourcePostProcessor [GetPropertySourcePostProcessor.java : 46] GetPropertySourcePostProcessor process @GetPropertySource bean.
2025-07-13 11:40:20,418 WARN [main] c.g.c.s.p.GetPropertySourcePostProcessor [GetPropertySourcePostProcessor.java : 51] Not found @GetPropertySource on spring bean class.
2025-07-13 11:40:22,223 INFO [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-13 11:40:23,019 INFO [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-13 11:40:23,724 WARN [main] io.undertow.websockets.jsr [Bootstrap.java : 68] UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-13 11:40:23,817 INFO [main] io.undertow.servlet [ServletContextImpl.java : 371] Initializing Spring embedded WebApplicationContext
2025-07-13 11:40:23,819 INFO [main] o.s.b.w.s.c.ServletWebServerApplicationContext [ServletWebServerApplicationContext.java : 285] Root WebApplicationContext: initialization completed in 7789 ms
2025-07-13 11:40:24,248 WARN [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 126] No URLs will be polled as dynamic configuration sources.
2025-07-13 11:40:24,248 INFO [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 127] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-13 11:40:24,284 INFO [main] c.n.config.DynamicPropertyFactory [DynamicPropertyFactory.java : 281] DynamicPropertyFactory is initialized with configuration sources: com.netflix.config.ConcurrentCompositeConfiguration@648e6c6b
2025-07-13 11:40:26,497 INFO [main] c.x.j.a.c.scheduler.XxlJobScheduler [XxlJobScheduler.java : 73] >>>>>>>>> init xxl-job admin success.
2025-07-13 11:40:26,533 WARN [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 126] No URLs will be polled as dynamic configuration sources.
2025-07-13 11:40:26,534 INFO [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 127] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-07-13 11:40:26,578 INFO [xxl-job, admin JobLosedMonitorHelper] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 110] HikariPool-1 - Starting...
2025-07-13 11:40:27,236 INFO [xxl-job, admin JobLosedMonitorHelper] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 123] HikariPool-1 - Start completed.
2025-07-13 11:40:27,397 INFO [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'applicationTaskExecutor'
2025-07-13 11:40:27,963 INFO [main] c.a.c.s.SentinelWebAutoConfiguration [SentinelWebAutoConfiguration.java : 80] [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-13 11:40:28,056 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /}" onto com.xxl.job.admin.controller.IndexController#index(Model)
2025-07-13 11:40:28,057 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /help}" onto com.xxl.job.admin.controller.IndexController#help()
2025-07-13 11:40:28,057 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{POST /logout}" onto com.xxl.job.admin.controller.IndexController#logout(HttpServletRequest, HttpServletResponse)
2025-07-13 11:40:28,058 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /toLogin}" onto com.xxl.job.admin.controller.IndexController#toLogin(HttpServletRequest, HttpServletResponse, ModelAndView)
2025-07-13 11:40:28,058 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{POST /login}" onto com.xxl.job.admin.controller.IndexController#loginDo(HttpServletRequest, HttpServletResponse, String, String, String)
2025-07-13 11:40:28,058 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /chartInfo}" onto com.xxl.job.admin.controller.IndexController#chartInfo(Date, Date)
2025-07-13 11:40:28,058 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /api/{uri}}" onto com.xxl.job.admin.controller.JobApiController#api(HttpServletRequest, String, String)
2025-07-13 11:40:28,058 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobcode}" onto com.xxl.job.admin.controller.JobCodeController#index(HttpServletRequest, Model, int)
2025-07-13 11:40:28,059 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobcode/save}" onto com.xxl.job.admin.controller.JobCodeController#save(Model, int, String, String)
2025-07-13 11:40:28,059 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobgroup}" onto com.xxl.job.admin.controller.JobGroupController#index(Model)
2025-07-13 11:40:28,059 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobgroup/remove}" onto com.xxl.job.admin.controller.JobGroupController#remove(int)
2025-07-13 11:40:28,059 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobgroup/update}" onto com.xxl.job.admin.controller.JobGroupController#update(XxlJobGroup)
2025-07-13 11:40:28,059 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobgroup/save}" onto com.xxl.job.admin.controller.JobGroupController#save(XxlJobGroup)
2025-07-13 11:40:28,060 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobgroup/loadById}" onto com.xxl.job.admin.controller.JobGroupController#loadById(int)
2025-07-13 11:40:28,060 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobgroup/pageList}" onto com.xxl.job.admin.controller.JobGroupController#pageList(HttpServletRequest, int, int, String, String)
2025-07-13 11:40:28,060 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo}" onto com.xxl.job.admin.controller.JobInfoController#index(HttpServletRequest, Model, int)
2025-07-13 11:40:28,060 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo/add}" onto com.xxl.job.admin.controller.JobInfoController#add(XxlJobInfo)
2025-07-13 11:40:28,060 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo/remove}" onto com.xxl.job.admin.controller.JobInfoController#remove(int)
2025-07-13 11:40:28,060 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo/update}" onto com.xxl.job.admin.controller.JobInfoController#update(XxlJobInfo)
2025-07-13 11:40:28,061 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo/start}" onto com.xxl.job.admin.controller.JobInfoController#start(int)
2025-07-13 11:40:28,061 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo/pageList}" onto com.xxl.job.admin.controller.JobInfoController#pageList(int, int, int, int, String, String, String)
2025-07-13 11:40:28,061 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo/trigger}" onto com.xxl.job.admin.controller.JobInfoController#triggerJob(int, String, String)
2025-07-13 11:40:28,061 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo/stop}" onto com.xxl.job.admin.controller.JobInfoController#pause(int)
2025-07-13 11:40:28,062 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /jobinfo/nextTriggerTime}" onto com.xxl.job.admin.controller.JobInfoController#nextTriggerTime(String, String)
2025-07-13 11:40:28,062 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /joblog}" onto com.xxl.job.admin.controller.JobLogController#index(HttpServletRequest, Model, Integer)
2025-07-13 11:40:28,062 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /joblog/pageList}" onto com.xxl.job.admin.controller.JobLogController#pageList(HttpServletRequest, int, int, int, int, int, String)
2025-07-13 11:40:28,062 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /joblog/logDetailCat}" onto com.xxl.job.admin.controller.JobLogController#logDetailCat(String, long, long, int)
2025-07-13 11:40:28,062 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /joblog/logKill}" onto com.xxl.job.admin.controller.JobLogController#logKill(int)
2025-07-13 11:40:28,063 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /joblog/getJobsByGroup}" onto com.xxl.job.admin.controller.JobLogController#getJobsByGroup(int)
2025-07-13 11:40:28,063 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /joblog/logDetailPage}" onto com.xxl.job.admin.controller.JobLogController#logDetailPage(int, Model)
2025-07-13 11:40:28,063 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /joblog/clearLog}" onto com.xxl.job.admin.controller.JobLogController#clearLog(int, int, int)
2025-07-13 11:40:28,063 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /user}" onto com.xxl.job.admin.controller.UserController#index(Model)
2025-07-13 11:40:28,063 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /user/add}" onto com.xxl.job.admin.controller.UserController#add(XxlJobUser)
2025-07-13 11:40:28,063 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /user/remove}" onto com.xxl.job.admin.controller.UserController#remove(HttpServletRequest, int)
2025-07-13 11:40:28,063 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /user/update}" onto com.xxl.job.admin.controller.UserController#update(HttpServletRequest, XxlJobUser)
2025-07-13 11:40:28,064 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /user/pageList}" onto com.xxl.job.admin.controller.UserController#pageList(int, int, String, int)
2025-07-13 11:40:28,064 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /user/updatePwd}" onto com.xxl.job.admin.controller.UserController#updatePwd(HttpServletRequest, String)
2025-07-13 11:40:28,064 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /error}" onto org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)
2025-07-13 11:40:28,065 INFO [main] c.g.c.c.v.GetRequestMappingHandlerMapping [GetRequestMappingHandlerMapping.java : 76] Mapped "{ /error, produces [text/html]}" onto org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#errorHtml(HttpServletRequest, HttpServletResponse)
2025-07-13 11:40:28,119 INFO [main] o.s.b.a.w.s.WelcomePageHandlerMapping [WelcomePageHandlerMapping.java : 57] Adding welcome page template: index
2025-07-13 11:40:28,876 INFO [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-07-13 11:40:28,900 INFO [main] com.alibaba.nacos.client.naming [InitUtils.java : 67] initializer namespace from System Property :null
2025-07-13 11:40:28,902 INFO [main] com.alibaba.nacos.client.naming [InitUtils.java : 76] initializer namespace from System Environment :null
2025-07-13 11:40:28,904 INFO [main] com.alibaba.nacos.client.naming [InitUtils.java : 86] initializer namespace from System Property :null
2025-07-13 11:40:28,977 INFO [main] c.alibaba.nacos.common.remote.client [RpcClientFactory.java : 77] [RpcClientFactory] create a new rpc client of c0f913d6-99da-48e3-8959-60141938e70b
2025-07-13 11:40:28,978 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b]RpcClient init label, labels={module=naming, source=sdk}
2025-07-13 11:40:28,980 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b]RpcClient init, ServerListFactory =com.alibaba.nacos.client.naming.core.ServerListManager
2025-07-13 11:40:28,981 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b] Try to connect to server on start up, server: {serverIp='localhost', server main port=18848}
2025-07-13 11:40:29,097 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b] Success to connect to server [localhost:18848] on start up,connectionId=1752378069537_172.18.0.1_51461
2025-07-13 11:40:29,098 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
2025-07-13 11:40:29,098 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b]Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$4
2025-07-13 11:40:29,100 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b]Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
2025-07-13 11:40:29,100 INFO [main] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b]Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.NamingGrpcConnectionEventListener
2025-07-13 11:40:30,409 INFO [main] o.s.b.f.a.AutowiredAnnotationBeanPostProcessor [AutowiredAnnotationBeanPostProcessor.java : 367] Inconsistent constructor declaration on bean with name 'com.get.core.start.server.ServerInfo': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public com.get.core.start.server.ServerInfo(org.springframework.boot.autoconfigure.web.ServerProperties)
2025-07-13 11:40:31,001 INFO [xxl-job, admin JobScheduleHelper#scheduleThread] c.x.j.a.c.thread.JobScheduleHelper [JobScheduleHelper.java : 67] >>>>>>>>> init xxl-job admin scheduler success.
2025-07-13 11:40:32,300 INFO [main] o.s.b.a.e.web.EndpointLinksResolver [EndpointLinksResolver.java : 58] Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-13 11:40:32,768 INFO [main] com.alibaba.nacos.client.naming [ServiceInfoHolder.java : 181] init new ips(0) service: local@@xxljob-admin@@DEFAULT -> []
2025-07-13 11:40:32,790 INFO [main] com.alibaba.nacos.client.naming [ServiceInfoHolder.java : 166] current ips:(0) service: local@@xxljob-admin@@DEFAULT -> []
2025-07-13 11:40:32,810 INFO [main] io.undertow.servlet [ServletContextImpl.java : 371] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-13 11:40:32,810 INFO [main] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 525] Initializing Servlet 'dispatcherServlet'
2025-07-13 11:40:32,826 INFO [main] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 547] Completed initialization in 15 ms
2025-07-13 11:40:32,847 INFO [main] io.undertow [Undertow.java : 117] starting server: Undertow - 2.1.7.Final
2025-07-13 11:40:32,863 INFO [main] org.xnio [Xnio.java : 95] XNIO version 3.8.0.Final
2025-07-13 11:40:32,881 INFO [main] org.xnio.nio [NioXnio.java : 59] XNIO NIO Implementation Version 3.8.0.Final
2025-07-13 11:40:32,987 INFO [main] org.jboss.threads [Version.java : 52] JBoss Threads version 3.1.0.Final
2025-07-13 11:40:33,112 INFO [main] o.s.b.w.e.undertow.UndertowWebServer [UndertowWebServer.java : 133] Undertow started on port(s) 9009 (http) with context path '/xxl-job-admin'
2025-07-13 11:40:33,121 INFO [main] com.alibaba.nacos.client.naming [NamingGrpcClientProxy.java : 111] [REGISTER-SERVICE] local registering service xxljob-admin with instance Instance{instanceId='null', ip='************', port=9009, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD, management.context-path=/xxl-job-admin/actuator}}
2025-07-13 11:40:33,130 INFO [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, local xxljob-admin ************:9009 register finished
2025-07-13 11:40:33,407 INFO [nacos-grpc-client-executor-6] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b]receive server push request,request=NotifySubscriberRequest,requestId=9
2025-07-13 11:40:33,415 INFO [nacos-grpc-client-executor-6] com.alibaba.nacos.client.naming [ServiceInfoHolder.java : 235] new ips(1) service: local@@xxljob-admin@@DEFAULT -> [{"instanceId":"************#9009#DEFAULT#local@@xxljob-admin","ip":"************","port":9009,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"local@@xxljob-admin","metadata":{"preserved.register.source":"SPRING_CLOUD","management.context-path":"/xxl-job-admin/actuator"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-13 11:40:33,417 INFO [nacos-grpc-client-executor-6] com.alibaba.nacos.client.naming [ServiceInfoHolder.java : 166] current ips:(1) service: local@@xxljob-admin@@DEFAULT -> [{"instanceId":"************#9009#DEFAULT#local@@xxljob-admin","ip":"************","port":9009,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"local@@xxljob-admin","metadata":{"preserved.register.source":"SPRING_CLOUD","management.context-path":"/xxl-job-admin/actuator"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-07-13 11:40:33,418 INFO [nacos-grpc-client-executor-6] c.alibaba.nacos.common.remote.client [LoggerUtils.java : 60] [c0f913d6-99da-48e3-8959-60141938e70b]ack server push request,request=NotifySubscriberRequest,requestId=9
2025-07-13 11:40:34,024 INFO [main] c.get.core.start.StartEventListener [StartEventListener.java : 30] -[XXLJOB-ADMIN]-启动，端口:[9009]，环境:[dev,local]-
2025-07-13 11:40:34,056 INFO [main] c.xxl.job.admin.JobAdminApplication [StartupInfoLogger.java : 61] Started JobAdminApplication in 27.414 seconds (JVM running for 32.798)
2025-07-13 11:40:34,073 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 384] [config_rpc_client] [subscribe] xxljob-admin-local.yaml+local+local
2025-07-13 11:40:34,076 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 169] [config_rpc_client] [add-listener] ok, tenant=local, dataId=xxljob-admin-local.yaml, group=local, cnt=1
2025-07-13 11:40:34,078 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 384] [config_rpc_client] [subscribe] xxljob-admin.yaml+local+local
2025-07-13 11:40:34,078 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 169] [config_rpc_client] [add-listener] ok, tenant=local, dataId=xxljob-admin.yaml, group=local, cnt=1
2025-07-13 11:40:34,078 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 384] [config_rpc_client] [subscribe] xxljob-admin+local+local
2025-07-13 11:40:34,079 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 169] [config_rpc_client] [add-listener] ok, tenant=local, dataId=xxljob-admin, group=local, cnt=1
2025-07-13 11:40:34,080 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 384] [config_rpc_client] [subscribe] get.yaml+DEFAULT_GROUP+local
2025-07-13 11:40:34,081 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 169] [config_rpc_client] [add-listener] ok, tenant=local, dataId=get.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-13 11:40:34,081 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 384] [config_rpc_client] [subscribe] xxljob-admin-dev.yaml+local+local
2025-07-13 11:40:34,082 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 169] [config_rpc_client] [add-listener] ok, tenant=local, dataId=xxljob-admin-dev.yaml, group=local, cnt=1
2025-07-13 11:40:34,083 INFO [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 384] [config_rpc_client] [subscribe] get-local.yaml+DEFAULT_GROUP+local
2025-07-13 11:40:34,084 INFO [main] c.a.n.client.config.impl.CacheData [CacheData.java : 169] [config_rpc_client] [add-listener] ok, tenant=local, dataId=get-local.yaml, group=DEFAULT_GROUP, cnt=1
2025-07-13 11:44:06,957 WARN [Thread-27] c.a.nacos.common.notify.NotifyCenter [NotifyCenter.java : 136] [NotifyCenter] Start destroying Publisher
2025-07-13 11:44:06,960 WARN [Thread-20] c.a.n.c.http.HttpClientBeanHolder [HttpClientBeanHolder.java : 108] [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-13 11:44:06,961 WARN [Thread-27] c.a.nacos.common.notify.NotifyCenter [NotifyCenter.java : 153] [NotifyCenter] Destruction of the end
2025-07-13 11:44:06,963 WARN [Thread-20] c.a.n.c.http.HttpClientBeanHolder [HttpClientBeanHolder.java : 114] [HttpClientBeanHolder] Destruction of the end
2025-07-13 11:44:06,972 INFO [SpringContextShutdownHook] io.undertow [Undertow.java : 252] stopping server: Undertow - 2.1.7.Final
2025-07-13 11:44:06,988 INFO [SpringContextShutdownHook] io.undertow.servlet [ServletContextImpl.java : 371] Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-13 11:44:06,990 INFO [SpringContextShutdownHook] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 218] Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-07-13 11:44:06,995 ERROR [SpringContextShutdownHook] c.a.cloud.nacos.discovery.NacosWatch [NacosWatch.java : 170] namingService unsubscribe failed, properties:NacosDiscoveryProperties{serverAddr='localhost:18848', endpoint='', namespace='local', watchDelay=30000, logName='', service='xxljob-admin', weight=1.0, clusterName='DEFAULT', group='local', namingLoadCacheAtStart='false', metadata={preserved.register.source=SPRING_CLOUD, management.context-path=/xxl-job-admin/actuator}, registerEnabled=true, ip='************', networkInterface='', port=9009, secure=false, accessKey='', secretKey='', heartBeatInterval=null, heartBeatTimeout=null, ipDeleteTimeout=null}
java.lang.IllegalStateException: UT015023: This Context has been already destroyed
	at io.undertow.servlet.spec.ServletContextImpl.getDeploymentInfo(ServletContextImpl.java:211)
	at io.undertow.servlet.spec.ServletContextImpl.getInitParameterNames(ServletContextImpl.java:438)
	at org.springframework.web.context.support.ServletContextPropertySource.getPropertyNames(ServletContextPropertySource.java:41)
	at com.alibaba.spring.util.PropertySourcesUtils.getPropertyNames(PropertySourcesUtils.java:130)
	at com.alibaba.spring.util.PropertySourcesUtils.getSubProperties(PropertySourcesUtils.java:103)
	at com.alibaba.spring.util.PropertySourcesUtils.getSubProperties(PropertySourcesUtils.java:57)
	at com.alibaba.cloud.nacos.NacosDiscoveryProperties.enrichNacosDiscoveryProperties(NacosDiscoveryProperties.java:616)
	at com.alibaba.cloud.nacos.NacosDiscoveryProperties.getNacosProperties(NacosDiscoveryProperties.java:610)
	at com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:165)
	at com.alibaba.cloud.nacos.discovery.NacosWatch.stop(NacosWatch.java:97)
	at org.springframework.context.support.DefaultLifecycleProcessor.doStop(DefaultLifecycleProcessor.java:238)
	at org.springframework.context.support.DefaultLifecycleProcessor.access$300(DefaultLifecycleProcessor.java:53)
	at org.springframework.context.support.DefaultLifecycleProcessor$LifecycleGroup.stop(DefaultLifecycleProcessor.java:377)
	at org.springframework.context.support.DefaultLifecycleProcessor.stopBeans(DefaultLifecycleProcessor.java:210)
	at org.springframework.context.support.DefaultLifecycleProcessor.onClose(DefaultLifecycleProcessor.java:128)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1022)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-07-13 11:44:07,225 INFO [SpringContextShutdownHook] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 90] De-registering from Nacos Server now...
2025-07-13 11:44:07,225 WARN [SpringContextShutdownHook] o.s.c.a.CommonAnnotationBeanPostProcessor [InitDestroyAnnotationBeanPostProcessor.java : 185] Destroy method on bean with name 'nacosAutoServiceRegistration' threw an exception: java.lang.IllegalStateException: UT015023: This Context has been already destroyed
2025-07-13 11:44:07,226 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [ServiceInfoHolder.java : 256] com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
2025-07-13 11:44:07,226 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [FailoverReactor.java : 140] com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
2025-07-13 11:44:07,226 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [FailoverReactor.java : 142] com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
2025-07-13 11:44:07,226 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [ServiceInfoHolder.java : 258] com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
2025-07-13 11:44:07,226 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [NamingClientProxyDelegate.java : 176] com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
2025-07-13 11:44:07,226 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [ServiceInfoUpdateService.java : 130] com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
2025-07-13 11:44:07,527 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [ServiceInfoUpdateService.java : 132] com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
2025-07-13 11:44:07,527 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [ServerListManager.java : 188] com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
2025-07-13 11:44:07,527 WARN [SpringContextShutdownHook] com.alibaba.nacos.client.naming [NamingHttpClientManager.java : 72] [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-13 11:44:07,527 WARN [SpringContextShutdownHook] com.alibaba.nacos.client.naming [NamingHttpClientManager.java : 79] [NamingHttpClientManager] Destruction of the end
2025-07-13 11:44:07,528 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [ServerListManager.java : 193] com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
2025-07-13 11:44:07,528 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [NamingHttpClientProxy.java : 519] com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
2025-07-13 11:44:07,528 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [BeatReactor.java : 162] com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin
2025-07-13 11:44:07,529 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [BeatReactor.java : 164] com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop
2025-07-13 11:44:07,529 WARN [SpringContextShutdownHook] com.alibaba.nacos.client.naming [NamingHttpClientManager.java : 72] [NamingHttpClientManager] Start destroying NacosRestTemplate
2025-07-13 11:44:07,529 WARN [SpringContextShutdownHook] com.alibaba.nacos.client.naming [NamingHttpClientManager.java : 79] [NamingHttpClientManager] Destruction of the end
2025-07-13 11:44:07,529 INFO [SpringContextShutdownHook] c.a.n.c.identify.CredentialWatcher [CredentialWatcher.java : 105] [null] CredentialWatcher is stopped
2025-07-13 11:44:07,530 INFO [SpringContextShutdownHook] c.a.n.c.identify.CredentialService [CredentialService.java : 99] [null] CredentialService is freed
2025-07-13 11:44:07,530 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [NamingHttpClientProxy.java : 523] com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
2025-07-13 11:44:07,530 INFO [SpringContextShutdownHook] c.alibaba.nacos.common.remote.client [RpcClient.java : 462] Shutdown rpc client ,set status to shutdown
2025-07-13 11:44:07,530 INFO [SpringContextShutdownHook] c.alibaba.nacos.common.remote.client [RpcClient.java : 464] Shutdown  client event executor java.util.concurrent.ScheduledThreadPoolExecutor@220f0c85[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
2025-07-13 11:44:07,530 INFO [SpringContextShutdownHook] c.alibaba.nacos.common.remote.client [RpcClient.java : 466] Close current connection 1752378069537_172.18.0.1_51461
2025-07-13 11:44:07,532 INFO [nacos-grpc-client-executor-54] c.a.n.c.r.client.grpc.GrpcClient [LoggerUtils.java : 60] [1752378069537_172.18.0.1_51461]Ignore complete event,isRunning:false,isAbandon=false
2025-07-13 11:44:07,537 INFO [SpringContextShutdownHook] c.a.n.c.r.client.grpc.GrpcClient [GrpcClient.java : 83] Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@31e81715[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 55]
2025-07-13 11:44:07,537 INFO [SpringContextShutdownHook] com.alibaba.nacos.client.naming [NamingClientProxyDelegate.java : 182] com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
2025-07-13 11:44:07,539 INFO [SpringContextShutdownHook] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 218] Shutting down ExecutorService 'applicationTaskExecutor'
2025-07-13 11:44:08,540 INFO [xxl-job, admin JobScheduleHelper#scheduleThread] c.x.j.a.c.thread.JobScheduleHelper [JobScheduleHelper.java : 227] >>>>>>>>>>> xxl-job, JobScheduleHelper#scheduleThread stop
2025-07-13 11:44:09,000 INFO [xxl-job, admin JobScheduleHelper#ringThread] c.x.j.a.c.thread.JobScheduleHelper [JobScheduleHelper.java : 279] >>>>>>>>>>> xxl-job, JobScheduleHelper#ringThread stop
2025-07-13 11:44:09,540 INFO [SpringContextShutdownHook] c.x.j.a.c.thread.JobScheduleHelper [JobScheduleHelper.java : 368] >>>>>>>>>>> xxl-job, JobScheduleHelper stop
2025-07-13 11:44:09,540 INFO [xxl-job, admin JobLogReportHelper] c.x.j.a.c.thread.JobLogReportHelper [JobLogReportHelper.java : 132] >>>>>>>>>>> xxl-job, job log report thread stop
2025-07-13 11:44:09,541 INFO [xxl-job, admin JobLosedMonitorHelper] c.x.j.a.c.thread.JobCompleteHelper [JobCompleteHelper.java : 116] >>>>>>>>>>> xxl-job, JobLosedMonitorHelper stop
2025-07-13 11:44:09,541 INFO [xxl-job, admin JobFailMonitorHelper] c.x.j.a.c.t.JobFailMonitorHelper [JobFailMonitorHelper.java : 91] >>>>>>>>>>> xxl-job, job fail monitor thread stop
2025-07-13 11:44:09,541 INFO [xxl-job, admin JobRegistryMonitorHelper-registryMonitorThread] c.x.j.a.c.thread.JobRegistryHelper [JobRegistryHelper.java : 132] >>>>>>>>>>> xxl-job, job registry monitor thread stop
2025-07-13 11:44:09,541 INFO [SpringContextShutdownHook] c.x.j.a.c.t.JobTriggerPoolHelper [JobTriggerPoolHelper.java : 91] >>>>>>>>> xxl-job trigger thread pool shutdown success.
2025-07-13 11:44:09,543 INFO [SpringContextShutdownHook] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 350] HikariPool-1 - Shutdown initiated...
2025-07-13 11:44:09,557 INFO [SpringContextShutdownHook] com.zaxxer.hikari.HikariDataSource [HikariDataSource.java : 352] HikariPool-1 - Shutdown completed.

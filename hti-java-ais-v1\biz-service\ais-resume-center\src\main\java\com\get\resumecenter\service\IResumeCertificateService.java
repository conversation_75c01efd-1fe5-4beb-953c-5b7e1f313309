package com.get.resumecenter.service;


import com.get.resumecenter.vo.ResumeCertificateVo;
import com.get.resumecenter.dto.ResumeCertificateDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 17:02
 * @Description: 证书管理接口
 **/
public interface IResumeCertificateService {

    /**
     * 根据简历id 查询教育经历
     *
     * @param resumeId
     * @return
     */
    List<ResumeCertificateVo> getResumeCertificateListDto(Long resumeId);


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    ResumeCertificateVo getResumeCertificateById(Long id);


    /**
     * 添加
     *
     * @param certificateVo
     * @return
     */
    Long addResumeCertificate(ResumeCertificateDto certificateVo);

    /**
     * 修改
     *
     * @param certificateVo
     * @return
     */
    ResumeCertificateVo updateResumeCertificate(ResumeCertificateDto certificateVo);

    /**
     * 删除
     *
     * @param id
     */
    void deleteResumeCertificate(Long id);


}

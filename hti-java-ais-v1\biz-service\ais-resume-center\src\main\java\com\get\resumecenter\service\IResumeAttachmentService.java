package com.get.resumecenter.service;


import com.get.resumecenter.vo.MediaAndAttachedVo;
import com.get.resumecenter.vo.ResumeAttachmentVo;
import com.get.resumecenter.dto.MediaAndAttachedDto;
import com.get.resumecenter.dto.ResumeAttachmentDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/29
 * @TIME: 16:43
 * @Description:
 **/
public interface IResumeAttachmentService {

    /**
     * @return java.util.List<com.get.resumecenter.vo.ResumeAttachmentVo>
     * @Description: 列表
     * @Param [resumeAttachmentVo, page]
     * <AUTHOR>
     */
    List<ResumeAttachmentVo> getResumeAttachment(Long resumeId);
    /**
     * 查询附件
     *
     * @param data
     * @return
     * @
     */
    List<MediaAndAttachedVo> getResumeMedia(MediaAndAttachedDto data);
    /**
     * @return java.lang.Long
     * @Description: 新增
     * @Param [attachmentVo]
     * <AUTHOR>
     */
    Long add(ResumeAttachmentDto attachmentVo);

    /**
     * @return com.get.resumecenter.vo.ResumeAttachmentVo
     * @Description: 修改
     * @Param [resumeAttachmentDto]
     * <AUTHOR>
     */
    ResumeAttachmentVo update(ResumeAttachmentDto resumeAttachmentDto);

    /**
     * @return com.get.resumecenter.vo.ResumeAttachmentVo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    ResumeAttachmentVo findResumeAttachmentById(Long id);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);


}

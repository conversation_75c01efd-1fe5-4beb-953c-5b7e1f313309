package com.get.aisplatformcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.ULabelMapper;
import com.get.aisplatformcenter.service.ULabelService;
import com.get.aisplatformcenterap.dto.ULabelDto;
import com.get.aisplatformcenterap.dto.ULabelTypeDto;
import com.get.aisplatformcenterap.entity.ULabelEntity;
import com.get.aisplatformcenterap.entity.ULabelTypeEntity;
import com.get.aisplatformcenter.service.ULabelTypeService;
import com.get.aisplatformcenter.mapper.ULabelTypeMapper;
import com.get.aisplatformcenterap.vo.ULabelTypeExportVo;
import com.get.aisplatformcenterap.vo.ULabelTypeVo;

import com.get.aisplatformcenterap.vo.ULabelVo;
import com.get.common.eunms.BusinessErrorCodeEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;

import com.get.file.utils.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【u_label_type】的数据库操作Service实现
* @createDate 2024-12-19 11:15:30
*/
@Service
public class ULabelTypeServiceImpl extends ServiceImpl<ULabelTypeMapper, ULabelTypeEntity>
    implements ULabelTypeService {

    @Resource
    private ULabelTypeMapper uLabelTypeMapper;

    @Resource
    private ULabelMapper uLabelMapper;

    @Resource
    private ULabelService uLabelService;

    @Autowired
    private UtilService utilService;

    @Override
    public List<ULabelTypeVo> searchPage(ULabelTypeDto data, Page page) {

        LambdaQueryWrapper<ULabelTypeEntity> typeWrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(data.getTypeName())){
            typeWrapper.like(ULabelTypeEntity::getTypeName,  data.getTypeName() );
        }
        if(GeneralTool.isNotEmpty(data.getTypeKey())){
            typeWrapper.like(ULabelTypeEntity::getTypeKey, data.getTypeKey());
        }
        typeWrapper.orderByDesc(ULabelTypeEntity::getViewOrder);
        IPage<ULabelTypeEntity> pages = uLabelTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), typeWrapper);


        List<ULabelTypeEntity> uLabelTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(uLabelTypes)) {
            return Collections.emptyList();
        }
        ULabelDto uLabelDto = new ULabelDto();
        List<ULabelTypeVo> uLabelTypeVos = new ArrayList<>();
        List<ULabelVo> labelVoList = new ArrayList<>();
        for (ULabelTypeEntity uLabelType:uLabelTypes){
            ULabelTypeVo uLabelTypeVo = BeanCopyUtils.objClone(uLabelType, ULabelTypeVo::new);
            uLabelDto.setFkLabelTypeId(uLabelTypeVo.getId());
            if(GeneralTool.isNotEmpty(uLabelDto.getFkLabelTypeId())){
                labelVoList=uLabelService.searchPage(uLabelDto,new Page<>());
            }
            uLabelTypeVo.setLabelVoList(labelVoList);
            uLabelTypeVos.add(uLabelTypeVo);
        }

        return uLabelTypeVos;
    }

    @Override
    public Long addULabelType(ULabelTypeDto uLabelTypeDto) {
        if (uLabelTypeDto == null) {
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","insert_vo_null!"));
        }
        ULabelTypeEntity uLabelTypeEntity = BeanCopyUtils.objClone(uLabelTypeDto, ULabelTypeEntity::new);
        //检查值是否有重复
        check(uLabelTypeDto,null);
        uLabelTypeEntity.setViewOrder(uLabelTypeMapper.getMaxOrder());
        utilService.updateUserInfoToEntity(uLabelTypeEntity);
        int i = uLabelTypeMapper.insert(uLabelTypeEntity);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return uLabelTypeEntity.getId();
    }


    @Override
    public ULabelTypeVo updateULabelType(ULabelTypeDto uLabelTypeDto) {
        if (uLabelTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ULabelTypeEntity result = uLabelTypeMapper.selectById(uLabelTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ULabelTypeEntity uLabelTypeEntity = BeanCopyUtils.objClone(uLabelTypeDto, ULabelTypeEntity::new);


        if(GeneralTool.isEmpty(uLabelTypeDto.getId())||GeneralTool.isEmpty(uLabelTypeDto.getTypeKey())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //判断是否有重复的值
        check(uLabelTypeDto,"update");
        utilService.updateUserInfoToEntity(uLabelTypeEntity);
        uLabelTypeMapper.updateById(uLabelTypeEntity);
        return findULabelTypeById(uLabelTypeEntity.getId());
    }

    @Override
    public ResponseBo delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (uLabelTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        LambdaQueryWrapper<ULabelEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ULabelEntity::getFkLabelTypeId,id);
        wrapper.eq(ULabelEntity::getIsActive,1);
        List<ULabelEntity> uLabelEntities = uLabelMapper.selectList(wrapper);
        if(GeneralTool.isNotEmpty(uLabelEntities)){
            return DeleteResponseBo.error(BusinessErrorCodeEnum.LABEL_EXISTENCE.getCode(),BusinessErrorCodeEnum.LABEL_EXISTENCE.getMessage());
        }
        uLabelTypeMapper.deleteById(id);
        return DeleteResponseBo.ok();
    }

    @Override
    public ResponseBo batchDelete(Set<Long> uLabelTypes) {
        if (GeneralTool.isEmpty(uLabelTypes)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<Long> ids = new ArrayList<>();
        for (Long id:uLabelTypes){
            if (uLabelTypeMapper.selectById(id) == null) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
            }
            LambdaQueryWrapper<ULabelEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ULabelEntity::getFkLabelTypeId,id);
            List<ULabelEntity> uLabelEntities = uLabelMapper.selectList(wrapper);
            if(GeneralTool.isNotEmpty(uLabelEntities)){
                return DeleteResponseBo.error(BusinessErrorCodeEnum.LABEL_EXISTENCE.getCode(),BusinessErrorCodeEnum.LABEL_EXISTENCE.getMessage());
            }
            if(GeneralTool.isEmpty(uLabelEntities)){
                ids.add(id);
            }
        }

        uLabelTypeMapper.deleteBatchIds(ids);
        return DeleteResponseBo.ok();
    }

    @Override
    public ULabelTypeVo findULabelTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ULabelTypeEntity uLabelTypeEntity = uLabelTypeMapper.selectById(id);
        ULabelTypeVo uLabelTypeVo = BeanCopyUtils.objClone(uLabelTypeEntity, ULabelTypeVo::new);
        return uLabelTypeVo;
    }


public void export(ULabelTypeDto uLabelTypeDto, HttpServletResponse response) {
    List<ULabelTypeExportVo> dataList = uLabelTypeMapper.getExportLabelTypeInfo(uLabelTypeDto);
    if (dataList == null || dataList.isEmpty()) {
        throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
    }

    // 表头
    List<String> headers = new ArrayList<>();
    headers.add("标签类型ID");
    headers.add("类型名称"); // 允许重复
    headers.add("类型系统Key");  // 允许重复
    headers.add("描述");
    headers.add("标签ID");
    headers.add("类型名称");
    headers.add("类型系统Key");
    headers.add("标签描述");


    // 数据行
    List<List<String>> rows = new ArrayList<>();
    for (ULabelTypeExportVo vo : dataList) {
        List<String> row = new ArrayList<>();
        row.add(vo.getId() != null ? vo.getId().toString() : "");
        row.add(vo.getTypeName());
        row.add(vo.getTypeKey());
        row.add(vo.getRemark());
        row.add(vo.getLabelId());
        row.add(vo.getLabelName());
        row.add(vo.getLabelKey());
        row.add(vo.getLabelRemark());
        rows.add(row);
    }

    // 写入 Excel
    FileUtils.exportExcel(response, headers, rows, "export");
}

    public void check(ULabelTypeDto uLabelTypeDto,String type) {
        LambdaQueryWrapper<ULabelTypeEntity> wrapper = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(uLabelTypeDto.getTypeName())){
            wrapper.eq(ULabelTypeEntity::getTypeName,uLabelTypeDto.getTypeName());
            if(GeneralTool.isNotEmpty(type)&&type.equals("update")){
                wrapper.ne(ULabelTypeEntity::getId,uLabelTypeDto.getId());
            }
        }

        if(GeneralTool.isNotEmpty(uLabelTypeMapper.selectList(wrapper))){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
        }
        LambdaQueryWrapper<ULabelTypeEntity> wrapper1 = new LambdaQueryWrapper<>();
        if(GeneralTool.isNotEmpty(uLabelTypeDto.getTypeKey())){
            wrapper1.eq(ULabelTypeEntity::getTypeKey,uLabelTypeDto.getTypeKey());
            if(GeneralTool.isNotEmpty(type)&&type.equals("update")){
                wrapper1.ne(ULabelTypeEntity::getId,uLabelTypeDto.getId());
            }
        }
        if(GeneralTool.isNotEmpty(uLabelTypeMapper.selectList(wrapper1))){
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_key_exists"));
        }
    }

    @Override
    public List<ULabelTypeVo> selectLabelType() {
        LambdaQueryWrapper<ULabelTypeEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ULabelTypeEntity::getId,ULabelTypeEntity::getTypeName);
        List<ULabelTypeEntity> uLabelTypeEntities = uLabelTypeMapper.selectList(wrapper);
        return uLabelTypeEntities.stream().map(
                uLabelTypeEntity -> BeanCopyUtils.objClone(uLabelTypeEntity, ULabelTypeVo::new)
                ).collect(Collectors.toList());
    }

    @Override
    public void sortULabelType(List<ULabelTypeDto> uLabelTypeDtoList) {
        if (GeneralTool.isEmpty(uLabelTypeDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ULabelTypeEntity moveDataOne = BeanCopyUtils.objClone(uLabelTypeDtoList.get(0), ULabelTypeEntity::new);
        ULabelTypeEntity uLabelEntityTypeMoveDataOne = uLabelTypeMapper.selectById(moveDataOne.getId());
        if (GeneralTool.isNotEmpty(uLabelEntityTypeMoveDataOne) && !uLabelEntityTypeMoveDataOne.getViewOrder().equals(moveDataOne.getViewOrder()) ){
            throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
        }
        Integer oneViewOrder = moveDataOne.getViewOrder();
        ULabelTypeEntity moveDataTwo = BeanCopyUtils.objClone(uLabelTypeDtoList.get(1), ULabelTypeEntity::new);
        ULabelTypeEntity uLabelEntityTypeMoveDataTwo = uLabelTypeMapper.selectById(moveDataTwo.getId());
        if (GeneralTool.isNotEmpty(uLabelEntityTypeMoveDataTwo) && !uLabelEntityTypeMoveDataTwo.getViewOrder().equals(moveDataTwo.getViewOrder()) ){
            throw new GetServiceException(LocaleMessageUtils.getMessage("inconsistent_data"));
        }
        Integer twoViewOrder = moveDataTwo.getViewOrder();
        moveDataOne.setViewOrder(twoViewOrder);
        utilService.updateUserInfoToEntity(moveDataOne);
        moveDataTwo.setViewOrder(oneViewOrder);
        utilService.updateUserInfoToEntity(moveDataTwo);
        uLabelTypeMapper.updateById(moveDataOne);
        uLabelTypeMapper.updateById(moveDataTwo);
    }
}





package com.get.pmpcenter.event;

import com.get.pmpcenter.dto.common.LogDto;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:12
 * @Version 1.0
 */
public class CommissionChangedEvent extends ApplicationEvent {

    @ApiModelProperty("操作类型")
    private List<LogDto> logs;

    public CommissionChangedEvent(Object source, List<LogDto> logs) {
        super(source);
        this.logs = logs;
    }

    public List<LogDto> getLogs() {
        return logs;
    }

    public void setLogs(List<LogDto> logs) {
        this.logs = logs;
    }
}

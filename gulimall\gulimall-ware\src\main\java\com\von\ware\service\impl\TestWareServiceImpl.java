package com.von.ware.service.impl;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.von.ware.entity.WareInfoEntity;
import com.von.ware.service.TestWareService;
import com.von.ware.service.TestWareService2;
import com.von.ware.service.WareInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2024/12/17 下午10:34
 * @Version 1.0
 */
@Service
@RequiredArgsConstructor
public class TestWareServiceImpl implements TestWareService {

    private final WareInfoService wareInfoService;

    private final TestWareService2 testWareService2;

    public static ThreadLocal<String> uuidThreadLocal = new ThreadLocal<>();

    /**
     *
     * 事物失效的解决案例
     *
     */
    @Override
    @Transactional
    public void addA() {
        String uuid = UuidUtils.generateUuid().replace("-", "").substring(0, 10);
        uuidThreadLocal.set(uuid);
        WareInfoEntity ware = WareInfoEntity.builder().name(uuid).address("方法A").build();
        this.wareInfoService.save(ware);

        // 重点代码
        TestWareServiceImpl testWareService = (TestWareServiceImpl) AopContext.currentProxy();
        testWareService.testB();

    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void testB() {
        String uuid = this.uuidThreadLocal.get();
        WareInfoEntity ware = WareInfoEntity.builder().name(uuid).address("方法B").build();
        this.wareInfoService.save(ware);
    }


}

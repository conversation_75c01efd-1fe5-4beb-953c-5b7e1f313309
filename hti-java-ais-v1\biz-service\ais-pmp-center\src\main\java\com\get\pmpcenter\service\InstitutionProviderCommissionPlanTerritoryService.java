package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.dto.institution.PlanTerritoryDto;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanTerritory;

import java.util.List;

/**
 * @Author:<PERSON>
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface InstitutionProviderCommissionPlanTerritoryService extends IService<InstitutionProviderCommissionPlanTerritory> {

    /**
     * 保存佣金方案适用国家/区域
     *
     * @param providerCommissionPlanId
     * @param countryIds
     * @param isInclude
     */
    List<LogDto> saveProviderCommissionPlanTerritory(Long contractId, Long providerCommissionPlanId, List<Long> countryIds, Integer isInclude,Boolean saveLog);


    /**
     * 保存佣金方案适用国家/区域
     *
     * @param contractId
     * @param providerCommissionPlanId
     * @param territoryList
     * @return
     */
    List<LogDto> saveProviderCommissionPlanTerritory(Long contractId, Long providerCommissionPlanId, List<PlanTerritoryDto> territoryList);

}

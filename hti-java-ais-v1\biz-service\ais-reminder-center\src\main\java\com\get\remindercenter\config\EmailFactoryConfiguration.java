package com.get.remindercenter.config;

import com.get.remindercenter.component.*;
import com.get.remindercenter.enums.EmailTemplateEnum;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 邮件工厂Config
 */
@Configuration
public class EmailFactoryConfiguration {

    /**
     * 学习计划佣金结算通知 邮件组件类
     */
    @Resource(name = "studentOfferItemCommissionNoticeEmailHelper")
    private StudentOfferItemCommissionNoticeEmailHelper studentOfferItemCommissionNoticeEmailHelper;

    /**
     * 接受Offer截止提醒/支付押金截止提醒/代理学生接受Offer截止提醒/代理学生支付押金截止提醒 通知 邮件组件类
     */
    @Resource(name = "acceptOfferDeadlineReminderEmailHelper")
    private AcceptOfferDeadlineReminderEmailHelper acceptOfferDeadlineReminderEmailHelper;

    /**
     * 学习计划相同学校课程提醒
     */
    @Resource(name = "studyPlanSameSchoolCourseEmailHelper")
    private StudyPlanSameSchoolCourseEmailHelper studyPlanSameSchoolCourseEmailHelper;

    /**
     * 工工休单工作流提醒 / 申请方案终止作废工作流提醒 / 非工休单工作流提醒 邮件组件类
     */
    @Resource(name = "workLeaveReminderEmailHelper")
    private WorkLeaveReminderEmailHelper  workLeaveReminderEmailHelper;

    /**
     * 活动费用汇总收款计划变更提醒 邮件组件类
     */
    @Resource(name = "eventFeeCollectionChangeReminderEmailHelper")
    private EventFeeCollectionChangeReminderEmailHelper eventFeeCollectionChangeReminderEmailHelper;

    /**
     * 奖励推广活动提醒 邮件组件类
     */
    @Resource(name = "rewardPromotionActivityReminderEmailHelper")
    private RewardPromotionActivityReminderEmailHelper rewardPromotionActivityReminderEmailHelper;

    /**
     * 多人任务提醒 邮件组件类
     */
    @Resource(name = "multiUserTaskReminderEmailHelper")
    private MultiUserTaskReminderEmailHelper multiUserTaskReminderEmailHelper;

    /**
     *  峰会提交报名册提醒 邮件组件类
     */
    @Resource(name = "summitRegistrationReminderEmailHelper")
    private SummitRegistrationReminderEmailHelper summitRegistrationReminderEmailHelper;

    /**
     * 学校提供商合同到期提醒 邮件组件类
     */
    @Resource(name = "providerContractExpiryEmailHelper")
    private ProviderContractExpiryEmailHelper providerContractExpiryEmailHelper;

    /**
     * 申请方案创建邮件 邮件组件类
      */
    @Resource(name = "studentOfferCreateEmailHelper")
    private StudentOfferCreateEmailHelper studentOfferCreateEmailHelper;

    /**
     * 申请步骤变更邮件 邮件组件类
     */
    @Resource(name = "applicationStepAdmittedEmailHelper")
    private ApplicationStepAdmittedEmailHelper applicationStepAdmittedEmailHelper;

    /**
     * 申请计划佣金通知 邮件组件类
     */
    @Resource(name = "commissionNoticeEmailHelper")
    private CommissionNoticeEmailHelper commissionNoticeEmailHelper;

    /**
     * 留学保险佣金结算通知 邮件组件类
     */
    @Resource(name = "insuranceCommissionNoticeEmailHelper")
    private InsuranceCommissionNoticeEmailHelper insuranceCommissionNoticeEmailHelper;

    /**
     * 留学住宿佣金结算通知 邮件组件类
     */
    @Resource(name = "accommodationCommissionNoticeEmailHelper")
    private AccommodationCommissionNoticeEmailHelper accommodationCommissionNoticeEmailHelper;

    /**
     * 留学服务费佣金结算通知 邮件组件类
     */
    @Resource(name = "serviceFeeCommissionNoticeEmailHelper")
     private ServiceFeeCommissionNoticeEmailHelper serviceFeeCommissionNoticeEmailHelper;

    /**
     * 延迟入学通知 邮件组件类
     */
    @Resource(name = "deferEntranceRemindEmailHelper")
    private DeferEntranceRemindEmailHelper deferEntranceRemindEmailHelper;

    /**
     * 申请计划更新通知佣金部 邮件组件类
     */
    @Resource(name = "offerItemCommissionNoticeEmailHelper")
    private OfferItemCommissionNoticeEmailHelper offerItemCommissionNoticeEmailHelper;


    @Resource(name = "systemSendEmailHelper")
    private SystemSendEmailHelper systemSendEmailHelper;

    /**
     * 通用邮件处理器
     */
    @Resource(name = "universalEmailHelper")
    private UniversalEmailHelper universalEmailHelper;


    @Bean(name = "emailHelperMap")
    public Map<String, EmailAbstractHelper> payHelperMap() {
        Map<String, EmailAbstractHelper> payHelperMap = new HashMap<>();
        payHelperMap.put(EmailTemplateEnum.STUDENT_OFFER_ITEM_COMMISSION_NOTICE.getEmailTemplateKey(), studentOfferItemCommissionNoticeEmailHelper);
        payHelperMap.put(EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND.getEmailTemplateKey(), acceptOfferDeadlineReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.PAY_DEPOSIT_DUE_REMIND.getEmailTemplateKey(), acceptOfferDeadlineReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.getEmailTemplateKey(), acceptOfferDeadlineReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.getEmailTemplateKey(), acceptOfferDeadlineReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.COURSE_OPENING_REMINDER.getEmailTemplateKey(), acceptOfferDeadlineReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.STUDY_PLAN_SAME_SCHOOL_COURSE_REMINDER.getEmailTemplateKey(),studyPlanSameSchoolCourseEmailHelper);
        payHelperMap.put(EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey(), workLeaveReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.APPLN_TERM_INVAL_WORKFLOW_REMINDER.getEmailTemplateKey(), workLeaveReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.NON_WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey(), workLeaveReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.EVENT_FEE_PLAN_CHANGE_REMINDER.getEmailTemplateKey(), eventFeeCollectionChangeReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.REWARD_PROMOTION_ACTIVITY_REMINDER.getEmailTemplateKey(), rewardPromotionActivityReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.MULTI_USER_TASK_REMINDER.getEmailTemplateKey(), multiUserTaskReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.NEW_MULTI_USER_TASK_REMINDER.getEmailTemplateKey(), multiUserTaskReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.SUMMIT_REGISTRATION_REMINDER.getEmailTemplateKey(), summitRegistrationReminderEmailHelper);
        payHelperMap.put(EmailTemplateEnum.PROVIDER_CONTRACT_EXPIRE.getEmailTemplateKey(), providerContractExpiryEmailHelper);
        payHelperMap.put(EmailTemplateEnum.REMINDER_EMAIL_CREATE_STUDENT_OFFER.getEmailTemplateKey(), studentOfferCreateEmailHelper);
        payHelperMap.put(EmailTemplateEnum.REMINDER_EMAIL_STEP_ADMITTED.getEmailTemplateKey(),applicationStepAdmittedEmailHelper);
        payHelperMap.put(EmailTemplateEnum.REMINDER_COMMISSION_NOTICE.getEmailTemplateKey(),commissionNoticeEmailHelper);
        payHelperMap.put(EmailTemplateEnum.INSURANCE_COMMISSION_NOTICE.getEmailTemplateKey(),insuranceCommissionNoticeEmailHelper);
        payHelperMap.put(EmailTemplateEnum.ACCOMMODATION_COMMISSION_NOTICE.getEmailTemplateKey(),accommodationCommissionNoticeEmailHelper);
        payHelperMap.put(EmailTemplateEnum.SERVICE_FEE_COMMISSION_NOTICE.getEmailTemplateKey(),serviceFeeCommissionNoticeEmailHelper);
        payHelperMap.put(EmailTemplateEnum.DEFER_ENTRANCE_REMIND.getEmailTemplateKey(),deferEntranceRemindEmailHelper);
        payHelperMap.put(EmailTemplateEnum.OFFER_ITEM_COMMISSION_NOTICE.getEmailTemplateKey(),offerItemCommissionNoticeEmailHelper);
        // 通用邮件模板处理策略
        payHelperMap.put(EmailTemplateEnum.AGENT_APPLICATION_APPROVED_HAS_ACCOUNT.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_APPLICATION_APPROVED_NO_ACCOUNT.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_APPLICATION_REJECTED.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_RENEWAL_APPROVED_HAS_ACCOUNT.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_RENEWAL_APPROVED_NO_ACCOUNT.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_RENEWAL_REJECTED.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.CONTRACT_APPROVAL_PASSED_HAS_ACCOUNT.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.CONTRACT_APPROVAL_PASSED_NO_ACCOUNT.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_CONTRACT_APPROVAL_PASSED.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.CONTRACT_APPROVAL_REJECTED.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_CONTRACT_RENEWAL.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_APPLICATION_SUBMITTED.getEmailTemplateKey(), universalEmailHelper);
        payHelperMap.put(EmailTemplateEnum.AGENT_APPLICATION_APPROVE_NOTICE.getEmailTemplateKey(), universalEmailHelper);


        return payHelperMap;
    }

}

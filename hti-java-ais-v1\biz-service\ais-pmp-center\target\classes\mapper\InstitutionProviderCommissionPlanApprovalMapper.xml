<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanApprovalMapper">

<!--        <select id="getApprovalList"-->
<!--                resultType="com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval">-->
<!--            select a.*,-->
<!--            s.name as staffName-->
<!--            from m_institution_provider_commission_plan_approval a-->
<!--            left join ais_permission_center.m_staff s on a.fk_staff_id = s.id-->
<!--            where a.fk_institution_provider_commission_plan_id-->
<!--            in-->
<!--            <foreach collection="plaIds" item="plaId" open="(" separator="," close=")">-->
<!--                #{plaId}-->
<!--            </foreach>-->
<!--            order by a.gmt_create desc-->
<!--        </select>-->

    <select id="getApprovalList"
            resultType="com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval">
        select a.*,
        s.name as staffName
        from m_institution_provider_commission_plan_approval a
        left join ais_permission_center.m_staff s on a.fk_staff_id = s.id
        where
        <foreach item="planId" collection="plaIds" separator=" OR ">
            FIND_IN_SET(#{planId}, a.fk_institution_provider_commission_plan_ids)
        </foreach>
        order by a.gmt_create desc
    </select>

    <select id="getBatchApprovalList"
            resultType="com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval">
        select a.*
        from m_institution_provider_commission_plan_approval a
        where a.fk_staff_id = #{staffId}
        and a.type_key = 'mass'
        and a.approval_status = 1
        and exists(
        select 1 from m_institution_provider_commission_plan p
        where FIND_IN_SET(p.id, a.fk_institution_provider_commission_plan_ids) and p.approval_status = 1
        )
        AND (
        <foreach item="planId" collection="plaIds" separator=" OR ">
            FIND_IN_SET(#{planId}, a.fk_institution_provider_commission_plan_ids)
        </foreach>
        )
        order by a.gmt_create desc
    </select>

</mapper>
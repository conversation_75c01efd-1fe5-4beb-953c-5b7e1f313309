package com.von.ware.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/10/30 16:50
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MergeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long purchaseId;

    /**
     * 采购需求列表
     */
    private List<Long> items;

}

package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.ResumeEducationVo;
import com.get.resumecenter.service.IResumeEducationService;
import com.get.resumecenter.dto.ResumeEducationDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 14:36
 * @Description: 简历教育经历管理
 **/
@Api(tags = "简历教育经历管理")
@RestController
@RequestMapping("resume/education")
public class ResumeEducationController {
    @Autowired
    private IResumeEducationService educationService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/简历教育经历管理/参数详情")
    @GetMapping("/{id}")
    public ResponseBo detail(@PathVariable("id") Long id) {
        ResumeEducationVo educationDto = educationService.getResumeEducationById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", educationDto);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param educationVo
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历教育经历管理/新增教育经验")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ResumeEducationDto.Add.class) ResumeEducationDto educationVo) {
        return SaveResponseBo.ok(educationService.addResumeEducation(educationVo));
    }


    /**
     * 修改信息
     *
     * @param educationVo
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/简历教育经历管理/修改教育经验")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ResumeEducationDto.Update.class) ResumeEducationDto educationVo) {
        return UpdateResponseBo.ok(educationService.updateResumeEducation(educationVo));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "人才中心/简历教育经历管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        educationService.deleteResumeEducation(id);
        return ResponseBo.ok();
    }

}

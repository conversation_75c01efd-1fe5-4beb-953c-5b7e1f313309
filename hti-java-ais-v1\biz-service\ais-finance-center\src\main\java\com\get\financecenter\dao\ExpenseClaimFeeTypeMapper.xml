<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ExpenseClaimFeeTypeMapper">

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_expense_claim_fee_type

  </select>

  <select id="checkName" resultType="int">
      select count(*) from u_expense_claim_fee_type where type_name=#{typeName}
  </select>

  <select id="selectByFkAccountingItemId" resultType="com.get.financecenter.entity.ExpenseClaimFeeType">
      select * from u_expense_claim_fee_type where fk_accounting_item_id = #{fkAccountingItemId}
  </select>
</mapper>
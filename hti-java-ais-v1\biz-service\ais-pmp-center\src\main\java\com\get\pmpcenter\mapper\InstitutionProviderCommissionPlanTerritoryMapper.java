package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanTerritory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface InstitutionProviderCommissionPlanTerritoryMapper extends BaseMapper<InstitutionProviderCommissionPlanTerritory> {

    /**
     * 根据适用地区查询方案id
     * @param countryId
     * @return
     */
    List<Long> selectPlansByTerritoryId(@Param("countryId") Long countryId);
}

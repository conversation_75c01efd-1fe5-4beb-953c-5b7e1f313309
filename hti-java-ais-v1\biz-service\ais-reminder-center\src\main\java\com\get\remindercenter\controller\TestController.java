package com.get.remindercenter.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.component.AcceptOfferDeadlineReminderEmailHelper;
import com.get.remindercenter.dao.RemindTaskMapper;
import com.get.remindercenter.dto.AcceptOfferDeadlineReminderDto;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.dto.RemindTaskUpdateDto;
import com.get.remindercenter.dto.RemindTemplateListDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.RemindTask;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.EmailSenderQueueService;
import com.get.remindercenter.service.RemindTaskService;
import com.get.remindercenter.vo.RemindTemplateVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.get.core.tool.utils.DateUtil.now;

@Api(tags = "补日程提醒")
@RestController
@RequestMapping("reminder/test")
@Slf4j
public class TestController {

    @Autowired
    private EmailSenderQueueService emailSenderQueueService;

    @Autowired
    private RemindTaskMapper remindTaskMapper;

    @Autowired
    private RemindTaskService remindTaskService;

    @Resource
    private  AcceptOfferDeadlineReminderEmailHelper emailHelper;

    /**
     * @Description: 新增
     * @Author:
     * @Date:11:35
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新增", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.ADD, description = "提醒中心/新增")
    @PostMapping("addData")
    public ResponseBo addTask() {

        try {
            log.info("任务启动------------------");
            List<EmailSenderQueue> emailSenderQueues = emailSenderQueueService.getEmailSenderQueues();
            log.info("<=====================任务数量：" + emailSenderQueues.size() + "=========================>");
            if (GeneralTool.isNotEmpty(emailSenderQueues)) {
                for (EmailSenderQueue emailSenderQueue : emailSenderQueues) {
                    AcceptOfferDeadlineReminderDto reminderDto= emailHelper.assembleEmailData(emailSenderQueue);
                    List<RemindTaskDto> remindTaskVos = new ArrayList<>();
                    List<RemindTaskDto> remindTaskList = new ArrayList<>();
                    for (Long staffId:reminderDto.getStaffEmailSet()){
                        RemindTaskDto remindTaskVo = new RemindTaskDto();
                        remindTaskVo.setFkStaffId(staffId);
                        remindTaskVo.setStartTime(reminderDto.getOperationTime());
                        remindTaskVo.setTaskTitle(reminderDto.getEmailTitle());
                        remindTaskVo.setFkTableName(reminderDto.getFkTableName());
                        remindTaskVo.setFkTableId(reminderDto.getFkTableId());
                        remindTaskVo.setFkDbName(reminderDto.getFkDbName());
                        remindTaskVo.setFkRemindEventTypeKey(reminderDto.getFkEmailTypeKey());
                        remindTaskVos.add(remindTaskVo);
                        }
                    for(RemindTaskDto remindTaskDto:remindTaskVos){
                        LambdaQueryWrapper<RemindTask> lambdaQueryWrapper = Wrappers.<RemindTask>lambdaQuery()
                                .eq(RemindTask::getFkTableName, remindTaskDto.getFkTableName())
                                .eq(RemindTask::getFkTableId, remindTaskDto.getFkTableId())
                                .eq(RemindTask::getFkRemindEventTypeKey,remindTaskDto.getFkRemindEventTypeKey())
                                .eq(RemindTask::getFkStaffId, remindTaskDto.getFkStaffId())
                                .eq(RemindTask::getStartTime,reminderDto.getOperationTime());
                        RemindTask remindTask = remindTaskMapper.selectOne(lambdaQueryWrapper);
                        if(GeneralTool.isEmpty(remindTask)){
                            remindTaskList.add(remindTaskDto);
                        }
                    }
                    Boolean result =  remindTaskService.batchAddTask(remindTaskList);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("任务异常：", e);
        }
        return SaveResponseBo.ok();
    }

}

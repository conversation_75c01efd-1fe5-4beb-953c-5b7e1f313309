package com.von.coupon.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.von.common.to.MemberPrice;
import com.von.common.to.SkuReductionTo;
import com.von.common.utils.PageUtils;
import com.von.common.utils.Query;
import com.von.coupon.dao.SkuFullReductionDao;
import com.von.coupon.entity.MemberPriceEntity;
import com.von.coupon.entity.SkuFullReductionEntity;
import com.von.coupon.entity.SkuLadderEntity;
import com.von.coupon.service.MemberPriceService;
import com.von.coupon.service.SkuFullReductionService;
import com.von.coupon.service.SkuLadderService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("skuFullReductionService")
@RequiredArgsConstructor
public class SkuFullReductionServiceImpl extends ServiceImpl<SkuFullReductionDao, SkuFullReductionEntity> implements SkuFullReductionService {


    private final SkuLadderService skuLadderService;


    private final MemberPriceService memberPriceService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<SkuFullReductionEntity> page = this.page(
                new Query<SkuFullReductionEntity>().getPage(params),
                new QueryWrapper<SkuFullReductionEntity>()
        );

        return new PageUtils(page);
    }

    @Override
    public void saveSkuReduction(SkuReductionTo skuReductionTo) {
        SkuLadderEntity skuLadderEntity = new SkuLadderEntity();
        skuLadderEntity.setSkuId(skuReductionTo.getSkuId());
        skuLadderEntity.setDiscount(skuReductionTo.getDiscount());
        skuLadderEntity.setFullCount(skuReductionTo.getFullCount());
        skuLadderEntity.setAddOther(skuReductionTo.getCountStatus());
        this.skuLadderService.save(skuLadderEntity);

        SkuFullReductionEntity skuFullReductionEntity = BeanUtil.copyProperties(skuReductionTo, SkuFullReductionEntity.class);
        this.save(skuFullReductionEntity);

        List<MemberPrice> memberPriceList = skuReductionTo.getMemberPrice();
        List<MemberPriceEntity> memberPriceEntityList = memberPriceList.stream().map(memberPrice -> {
            MemberPriceEntity memberPriceEntity = new MemberPriceEntity();
            memberPriceEntity.setSkuId(skuReductionTo.getSkuId());
            memberPriceEntity.setMemberLevelId(memberPrice.getId());
            memberPriceEntity.setMemberLevelName(memberPrice.getName());
            memberPriceEntity.setMemberPrice(memberPrice.getPrice());
            return memberPriceEntity;
        }).collect(Collectors.toList());
        this.memberPriceService.saveBatch(memberPriceEntityList);
    }


}
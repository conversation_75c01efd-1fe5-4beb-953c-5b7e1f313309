package com.von.ware.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.von.common.constant.WareConstant;
import com.von.common.utils.PageUtils;
import com.von.common.utils.Query;
import com.von.ware.dao.PurchaseDao;
import com.von.ware.entity.PurchaseDetailEntity;
import com.von.ware.entity.PurchaseEntity;
import com.von.ware.service.PurchaseDetailService;
import com.von.ware.service.PurchaseService;
import com.von.ware.service.WareSkuService;
import com.von.ware.vo.MergeVo;
import com.von.ware.vo.PurchaseDoneVo;
import com.von.ware.vo.PurchaseItemDoneVo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service("purchaseService")
@RequiredArgsConstructor
public class PurchaseServiceImpl extends ServiceImpl<PurchaseDao, PurchaseEntity> implements PurchaseService {


    private final PurchaseDetailService purchaseDetailService;


    private final WareSkuService wareSkuService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        IPage<PurchaseEntity> page = this.page(
                new Query<PurchaseEntity>().getPage(params),
                new QueryWrapper<PurchaseEntity>()
        );

        return new PageUtils(page);
    }

    /**
     * TODO 这里的status可以使用枚举进行处理
     *
     * @param params
     * @return
     */
    @Override
    public PageUtils queryPageUnreceivePurchase(Map<String, Object> params) {
        IPage<PurchaseEntity> page = this.page(
                new Query<PurchaseEntity>().getPage(params),
                new QueryWrapper<PurchaseEntity>().eq("status", WareConstant.PurchaseStatusEnum.CREATE.getCode()).or()
                        .eq("status", WareConstant.PurchaseStatusEnum.ASSIGNED.getCode())
        );

        return new PageUtils(page);
    }

    @Override
    @Transactional
    public void mergePurchase(MergeVo mergeVo) {
        Long purchaseId = mergeVo.getPurchaseId();
        if (ObjectUtil.isNull(purchaseId)) {
            PurchaseEntity purchaseEntity = new PurchaseEntity();
            purchaseEntity.setStatus(WareConstant.PurchaseStatusEnum.CREATE.getCode());
            this.save(purchaseEntity);
            purchaseId = purchaseEntity.getId();
        }
        // TODO 确认采购单状态是 0或者1

        List<Long> items = mergeVo.getItems();
        Long finalPurchaseId = purchaseId;
        List<PurchaseDetailEntity> purchaseDetailEntityList = items.stream().map(item -> {
            PurchaseDetailEntity purchaseDetailEntity = new PurchaseDetailEntity();
            purchaseDetailEntity.setPurchaseId(finalPurchaseId);
            purchaseDetailEntity.setStatus(WareConstant.PurchaseDetailStatusEnum.ASSIGNED.getCode());

            return purchaseDetailEntity;
        }).collect(Collectors.toList());
        this.purchaseDetailService.saveBatch(purchaseDetailEntityList);
    }

    @Override
    public List<PurchaseEntity> test() {
        return this.list();
    }

    @Override
    @Transactional
    public void received(List<Long> idList) {
        if (CollectionUtil.isEmpty(idList)) {
            return;
        }
        // 更新采购单状态
        this.updateMainStatus(idList);

        // 更新采购单子表状态
        this.updateDetailState(idList);
    }

    /**
     * 采购入库
     * TODO 这里还可以用多个方法对大方法进行简化
     *
     * @param purchaseDoneVo
     */
    @Override
    @Transactional
    public void done(PurchaseDoneVo purchaseDoneVo) {
        Boolean flag = true;
        // 主表ID
        Long id = purchaseDoneVo.getId();

        // 改变采购项的状态
        List<PurchaseItemDoneVo> purchaseDoneVoItemList = purchaseDoneVo.getItems();
        if (CollectionUtil.isEmpty(purchaseDoneVoItemList)) {
            return;
        }

        // 改变子表状态
        List<PurchaseDetailEntity> purchaseDetailEntityList = new ArrayList<>();
        for (PurchaseItemDoneVo purchaseItemDoneVo : purchaseDoneVoItemList) {
            PurchaseDetailEntity purchaseDetailEntity = new PurchaseDetailEntity();
            Integer status = purchaseItemDoneVo.getStatus();
            Long purchaseItemDoneVoItemId = purchaseItemDoneVo.getItemId();
            if (WareConstant.PurchaseDetailStatusEnum.HASERROR.getCode() == status) {
                flag = false;
                purchaseDetailEntity.setStatus(status);
            } else {
                purchaseDetailEntity.setStatus(WareConstant.PurchaseDetailStatusEnum.FINISH.getCode());
            }
            purchaseDetailEntity.setId(purchaseItemDoneVoItemId);
            purchaseDetailEntityList.add(purchaseDetailEntity);
        }
        // 更新
        this.purchaseDetailService.updateBatchById(purchaseDetailEntityList);
        // 获取成功状态的采购子表, 并保存到库存表
        this.saveFinishStock(purchaseDetailEntityList);


        // 改变主表的状态
        PurchaseEntity purchaseEntity = new PurchaseEntity();
        purchaseEntity.setId(id);
        purchaseEntity.setStatus(flag ? WareConstant.PurchaseStatusEnum.FINISH.getCode() : WareConstant.PurchaseStatusEnum.HASERROR.getCode());
        this.updateById(purchaseEntity);


    }

    /**
     * 保存采购子表状态为成功的数据到库存表
     *
     * @param purchaseDetailEntityList
     */
    private void saveFinishStock(List<PurchaseDetailEntity> purchaseDetailEntityList) {
        List<Long> finishIds = purchaseDetailEntityList.stream()
                .filter(purchaseDetailEntity -> purchaseDetailEntity.getStatus() == WareConstant.PurchaseDetailStatusEnum.FINISH.getCode())
                .map(PurchaseDetailEntity::getId)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(finishIds)) {
            List<PurchaseDetailEntity> finishDetailList = this.purchaseDetailService.listByIds(finishIds);
            this.wareSkuService.addStock(finishDetailList);
        }
    }

    /**
     * 更新主表状态
     *
     * @param idList
     */
    private void updateMainStatus(List<Long> idList) {
        QueryWrapper<PurchaseEntity> purchaseEntityQueryWrapper = new QueryWrapper<PurchaseEntity>()
                .in("id", idList)
                .and(wp -> {
                    wp.eq("status", WareConstant.PurchaseStatusEnum.CREATE.getCode())
                            .or().eq("status", WareConstant.PurchaseStatusEnum.ASSIGNED.getCode());
                });
        List<PurchaseEntity> unReceivedPurchase = this.list(purchaseEntityQueryWrapper);
        if (CollectionUtil.isNotEmpty(unReceivedPurchase)) {
            unReceivedPurchase.stream().forEach(purchaseEntity -> purchaseEntity.setStatus(WareConstant.PurchaseStatusEnum.RECEIVE.getCode()));
            this.updateBatchById(unReceivedPurchase);
        }
    }

    /**
     * 更新子表状态
     *
     * @param idList
     */
    private void updateDetailState(List<Long> idList) {
        QueryWrapper<PurchaseDetailEntity> detailQueryWrapper = new QueryWrapper<PurchaseDetailEntity>()
                .in("purchase_id", idList)
                .and(wp -> {
                    wp.eq("status", WareConstant.PurchaseDetailStatusEnum.CREATE.getCode())
                            .or().eq("status", WareConstant.PurchaseDetailStatusEnum.ASSIGNED.getCode());
                });
        List<PurchaseDetailEntity> purchaseDetailEntityList = this.purchaseDetailService.list(detailQueryWrapper);
        if (CollectionUtil.isNotEmpty(purchaseDetailEntityList)) {
            purchaseDetailEntityList.forEach(purchaseEntity -> purchaseEntity.setStatus(WareConstant.PurchaseDetailStatusEnum.BUYING.getCode()));
            this.purchaseDetailService.updateBatchById(purchaseDetailEntityList);
        }
    }

}
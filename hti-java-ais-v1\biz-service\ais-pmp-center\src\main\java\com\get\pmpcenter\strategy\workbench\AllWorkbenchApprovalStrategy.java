package com.get.pmpcenter.strategy.workbench;

import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;
import com.get.pmpcenter.strategy.WorkbenchApprovalStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author:Oliver
 * @Date: 2025/4/27
 * @Version 1.0
 * @apiNote:
 */
@Component
public class AllWorkbenchApprovalStrategy implements WorkbenchApprovalStrategy {

    @Autowired
    private ProviderCommissionPlanApprovalStrategy providerStrategy;

    @Autowired
    private AgentCommissionPlanApprovalStrategy agentStrategy;

    @Autowired
    private ProviderCommissionPlanTerritoryApprovalStrategy batchTerritoryStrategy;

    @Override
    public List<WorkbenchApprovalVo> getApprovalList(PmpWorkbenchApprovalDto workbenchApprovalDto) {
        return Stream.of(
                        providerStrategy.getApprovalList(workbenchApprovalDto),
                        agentStrategy.getApprovalList(workbenchApprovalDto),
                        batchTerritoryStrategy.getApprovalList(workbenchApprovalDto)
                ).flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    @Override
    public String getApprovalType() {
        return "ALL";
    }
}

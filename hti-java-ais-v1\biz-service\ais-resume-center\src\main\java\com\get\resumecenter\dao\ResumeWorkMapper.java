package com.get.resumecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.resumecenter.entity.ResumeWork;
import org.apache.ibatis.annotations.Mapper;

;

@Mapper
public interface ResumeWorkMapper extends BaseMapper<ResumeWork> {


    int insertSelective(ResumeWork record);


    /**
     * @return java.lang.Boolean
     * @Description: 删除校验
     * @Param [industryTypeId]
     * <AUTHOR>
     */
    <PERSON><PERSON>an isExistByIndustryTypeId(Long industryTypeId);

}
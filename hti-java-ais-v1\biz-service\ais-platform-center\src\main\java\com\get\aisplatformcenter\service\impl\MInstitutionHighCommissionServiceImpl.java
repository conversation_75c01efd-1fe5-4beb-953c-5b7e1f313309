package com.get.aisplatformcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.MInstitutionHighCommissionMapper;
import com.get.aisplatformcenter.mapper.SMediaAndAttachedMapper;
import com.get.aisplatformcenter.service.MInstitutionHighCommissionService;
import com.get.aisplatformcenter.util.MyDateUtils;
import com.get.aisplatformcenter.util.TencentCloudUtils;
import com.get.aisplatformcenterap.entity.SMediaAndAttachedEntity;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.partnercenter.dto.DeletePatchParamsDto;
import com.get.partnercenter.dto.HighCommissionDto;
import com.get.partnercenter.dto.HighCommissionPutAwayParamsDto;
import com.get.partnercenter.entity.MInstitutionHighCommissionEntity;
import com.get.partnercenter.vo.HighCommissionComboxVo;
import com.get.partnercenter.vo.HighCommissionVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【m_institution_high_commission(学校高佣表)】的数据库操作Service实现
* @createDate 2024-12-05 11:23:44
*/
@Service
public class MInstitutionHighCommissionServiceImpl extends ServiceImpl<MInstitutionHighCommissionMapper, MInstitutionHighCommissionEntity>
    implements MInstitutionHighCommissionService {

    @Autowired
    private MInstitutionHighCommissionMapper highCommissionMapper;
    @Autowired
    private UtilService utilService;

    @Autowired
    private TencentCloudUtils tencentCloudUtils;

    @Autowired
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;


    @Override
    public List<HighCommissionVo> searchPage(HighCommissionDto params, Page page) {

        IPage<HighCommissionVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setMMageAddress(urlBase);

        List<HighCommissionVo> result=highCommissionMapper.searchPage(pages,params);

        page.setAll((int) pages.getTotal());

        return result;
    }

    @Override
    public Long saveOrUpdateHightCommission(HighCommissionDto dto) {
        if (dto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        MInstitutionHighCommissionEntity highCommissionpo = BeanCopyUtils.objClone(dto,MInstitutionHighCommissionEntity::new);

        if(highCommissionpo.getId()==null){
            LambdaQueryWrapper<MInstitutionHighCommissionEntity> wrapper=new LambdaQueryWrapper<>();
            wrapper.eq(MInstitutionHighCommissionEntity::getFkInstitutionId,dto.getFkInstitutionId())
                    .eq(MInstitutionHighCommissionEntity::getFkCompanyId,dto.getFkCompanyId())
                    .eq(MInstitutionHighCommissionEntity::getFkAgentCommissionTypeId,dto.getFkAgentCommissionTypeId())
            ;

            //等级条件判断
            MInstitutionHighCommissionEntity po=highCommissionMapper.selectOne(wrapper);
            if(po!=null ){
                throw new GetServiceException(ResultCode.VERIFY_FAILED,
                        LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","该学校存在了对应的等级设置!"));
            }
            utilService.updateUserInfoToEntity(highCommissionpo);
            highCommissionpo.setStatus(1);//默认是上架
            highCommissionMapper.insert(highCommissionpo);
            UserInfo user = GetAuthInfo.getUser();
            //保存附件
            if(StringUtils.isNotBlank(dto.getFileGuid())){
                SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                sMediaAndAttachedEntity.setFkFileGuid(dto.getFileGuid());
                sMediaAndAttachedEntity.setFkTableName("m_institution_high_commission");
                sMediaAndAttachedEntity.setFkTableId(highCommissionpo.getId());
                sMediaAndAttachedEntity.setRemark("高佣图片");
                sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                sMediaAndAttachedEntity.setGmtCreate(new Date());

                sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);

            }
        }else{
            //修改
            if(StringUtils.isNotBlank(dto.getFileGuid())){
                SMediaAndAttachedEntity attPo=sMediaAndAttachedMapper.selectOne(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                        .eq(SMediaAndAttachedEntity::getFkTableName,"m_institution_high_commission")
                        .eq(SMediaAndAttachedEntity::getFkTableId,dto.getId()).last("limit 1")
                );
                if(attPo!=null){
                    if(StringUtils.isBlank(attPo.getFkFileGuid()) ||  !attPo.getFkFileGuid().equals(dto.getFileGuid()) ){
                        sMediaAndAttachedMapper.deleteById(attPo.getId());
                        SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                        sMediaAndAttachedEntity.setFkFileGuid(dto.getFileGuid());
                        sMediaAndAttachedEntity.setFkTableName("m_institution_high_commission");
                        sMediaAndAttachedEntity.setFkTableId(highCommissionpo.getId());
                        sMediaAndAttachedEntity.setRemark("高佣图片");
                        sMediaAndAttachedEntity.setGmtCreateUser(SecureUtil.getLoginId());
                        sMediaAndAttachedEntity.setGmtCreate(new Date());
                        sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                    }
                }else{
                    SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                    sMediaAndAttachedEntity.setFkFileGuid(dto.getFileGuid());
                    sMediaAndAttachedEntity.setFkTableName("m_institution_high_commission");
                    sMediaAndAttachedEntity.setFkTableId(highCommissionpo.getId());
                    sMediaAndAttachedEntity.setRemark("高佣图片");
                    sMediaAndAttachedEntity.setGmtCreateUser(SecureUtil.getLoginId());
                    sMediaAndAttachedEntity.setGmtCreate(new Date());
                    sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                }
            }


            utilService.updateUserInfoToEntity(highCommissionpo);
            highCommissionMapper.updateById(highCommissionpo);
        }

        return highCommissionpo.getId();
    }

    @Override
    public Long putAway(HighCommissionPutAwayParamsDto dto) {

        if (dto == null) {
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","数据为空!"));
        }

        utilService.updateUserInfoToEntity(dto);
        if(dto.getType()==2) {//上架
            highCommissionMapper.updatePutAway(dto);
        }else if (dto.getType()==1) {//下架

            highCommissionMapper.updatePutAway(dto);

        }



        return 0l;
    }

    @Override
    public void deleteBatch(DeletePatchParamsDto patchVo) {
        if(GeneralTool.isEmpty(patchVo.getIds())){
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","ID不能为空!"));
        }

        List<MInstitutionHighCommissionEntity> list=highCommissionMapper.selectBatchIds(Arrays.asList(patchVo.getIds()));

        Set<Integer> statusArr=list.stream().map(MInstitutionHighCommissionEntity::getStatus).collect(Collectors.toSet());
        if(statusArr.contains(2)){
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","上架数据不能删除!"));
        }

        highCommissionMapper.deleteBatchIds(Arrays.asList(patchVo.getIds()));

    }

    @Override
    public HighCommissionVo getDetail(Long id) {
        HighCommissionDto params=new HighCommissionDto();
        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setMMageAddress(urlBase);
        params.setId(id);

        HighCommissionVo highCommissionDetail=highCommissionMapper.getDetail(params);


        SMediaAndAttachedEntity attPo=sMediaAndAttachedMapper.selectOne(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                .eq(SMediaAndAttachedEntity::getFkTableName,"m_institution_high_commission")
                .eq(SMediaAndAttachedEntity::getFkTableId,id).last("limit 1")
        );
        if(attPo!=null){
            highCommissionDetail.setFileGuid(attPo.getFkFileGuid());
        }

        return highCommissionDetail;
    }

    @Override
    public List<HighCommissionComboxVo> searchList() {
//        List<HighCommissionComboxVo> resultList=new ArrayList<HighCommissionComboxVo>();
//        UserInfo user = GetAuthInfo.getUser();
//        Long fkCompanyId=user.getFkCompanyId();
//        HighCommissionDto commissionDto=new HighCommissionDto();
//        commissionDto.setFkCompanyId(fkCompanyId);
//
//        //先使用旧PMP
//        //resultList=highCommissionMapper.resultList(commissionDto);
//
//        commissionDto.setYear(MyDateUtils.getYear(new Date()));
//        resultList=highCommissionMapper.resulOldPMPtList(commissionDto);
        return highCommissionMapper.commissionInstitutionList();
    }


}





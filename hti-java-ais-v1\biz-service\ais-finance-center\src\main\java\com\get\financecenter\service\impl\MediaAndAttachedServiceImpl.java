package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.financecenter.dao.MediaAndAttachedMapper;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.entity.FMediaAndAttached;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.dto.MediaAndAttachedDto;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/8/14 9:58
 * @verison: 1.0
 * @description:
 */
@Service
public class MediaAndAttachedServiceImpl extends BaseServiceImpl<MediaAndAttachedMapper, FMediaAndAttached> implements IMediaAndAttachedService {
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IFileCenterClient fileCenterClient;

    //111111
    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;

        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.FINANCECENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        fileDtos = result.getData();

        return fileDtos;
    }

    //111111
    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;

        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.FINANCECENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        fileDtos = result.getData();

        return fileDtos;
    }

    @Override
    public void deleteMediaAttached(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        FMediaAndAttached mediaAndAttached = mediaAndAttachedMapper.selectById(id);
        if (GeneralTool.isEmpty(mediaAndAttached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        Result<Boolean> result = fileCenterClient.delete(mediaAndAttached.getFkFileGuid(), LoggerModulesConsts.FINANCECENTER);
        if (result.isSuccess()) {
            int i = mediaAndAttachedMapper.deleteById(id);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public FMediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        FMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, FMediaAndAttached::new);
        Integer nextIndexKey = mediaAndAttachedMapper.getNextIndexKey(andAttached.getFkTableId(), mediaAttachedVo.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //实例化对象
        andAttached.setIndexKey(nextIndexKey);
        utilService.updateUserInfoToEntity(andAttached);
        mediaAndAttachedMapper.insertSelective(andAttached);
        FMediaAndAttachedVo mediaAndAttachedDto = BeanCopyUtils.objClone(andAttached, FMediaAndAttachedVo::new);
        mediaAndAttachedDto.setFilePath(mediaAttachedVo.getFilePath());
        mediaAndAttachedDto.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
        mediaAndAttachedDto.setId(andAttached.getId());
        mediaAndAttachedDto.setFileKey(mediaAttachedVo.getFileKey());
        return mediaAndAttachedDto;
    }

    @Override
    public List<FMediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        List<FMediaAndAttached> mediaAndAttacheds = getMediaAndAttacheds(attachedVo);
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    public List<FMediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<MediaAndAttached> mediaAndAttacheds = getMediaAndAttacheds(attachedVo);
//        page.restPage(mediaAndAttacheds);
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<FMediaAndAttached> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            wrapper.eq(FMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        wrapper.eq(FMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        wrapper.eq(FMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        wrapper.orderByDesc(FMediaAndAttached::getIndexKey);

        List<FMediaAndAttached> mediaAndAttacheds = this.list(wrapper);
        return getFileMedia(mediaAndAttacheds);
    }

    @Override
    public void updateTableId(Long id, Long tableId) {
        FMediaAndAttached mediaAndAttached = new FMediaAndAttached();
        mediaAndAttached.setFkTableId(tableId);
        mediaAndAttached.setId(id);
        mediaAndAttachedMapper.updateById(mediaAndAttached);
    }

    @Override
    public void deleteMediaAndAttachedByTableId(Long tableId, String tableName) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableId", tableId);
//        criteria.andEqualTo("fkTableName", tableName);
//        attachedMapper.deleteByExample(example);

        //111111删除功能待测试是否正常
        mediaAndAttachedMapper.delete(Wrappers.<FMediaAndAttached>query().lambda()
                .eq(FMediaAndAttached::getFkTableId, tableId).eq(FMediaAndAttached::getFkTableName, tableName));
    }


    @Override
    public List<Map<String, Object>> findAgentMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.SALEAGENT);
    }

    @Override
    public List<Map<String, Object>> findContractMediaType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.SALECONTRACT);
    }

    @Override
    public void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        FMediaAndAttached ro = BeanCopyUtils.objClone(mediaAttachedVos.get(0), FMediaAndAttached::new);
        Integer oneorder = ro.getIndexKey();
        FMediaAndAttached rt = BeanCopyUtils.objClone(mediaAttachedVos.get(1), FMediaAndAttached::new);
        Integer twoorder = rt.getIndexKey();
        ro.setIndexKey(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setIndexKey(oneorder);
        utilService.updateUserInfoToEntity(rt);
        mediaAndAttachedMapper.updateById(ro);
        mediaAndAttachedMapper.updateById(rt);
    }


    /**
     * 获取媒体附件
     *
     * @param attachedVo
     * @return
     * @throws GetServiceException
     */
    private List<FMediaAndAttached> getMediaAndAttacheds(MediaAndAttachedDto attachedVo) throws GetServiceException {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
//        }
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
//        example.orderBy("indexKey").desc();
//        return attachedMapper.selectByExample(example);
        LambdaQueryWrapper<FMediaAndAttached> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
            wrapper.eq(FMediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        wrapper.eq(FMediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        wrapper.eq(FMediaAndAttached::getFkTableId, attachedVo.getFkTableId());
        wrapper.orderByDesc(FMediaAndAttached::getIndexKey);

        List<FMediaAndAttached> mediaAndAttacheds = this.list(wrapper);
        return mediaAndAttacheds;
    }

    private List<FMediaAndAttachedVo> getFileMedia(List<FMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(FMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, FMediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.FINANCECENTER, guidList);
        Result<List<FileDto>> result = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<FileDto> fileDtos = result.getData();
        //返回结果不为空时
        List<FMediaAndAttachedVo> collect = null;
        if (GeneralTool.isNotEmpty(fileDtos)) {
            //遍历查询GUID是否一致
            collect = mediaAndAttachedDtos.stream().map(mediaAndAttachedDto -> fileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        /*mediaAndAttachedDto.setFkTableName(null);*/
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        return collect;
    }
}

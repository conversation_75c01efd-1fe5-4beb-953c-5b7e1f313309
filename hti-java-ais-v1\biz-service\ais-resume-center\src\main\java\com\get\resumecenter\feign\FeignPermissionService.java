//package com.get.resumecenter.feign;
//
//
//import com.get.common.result.ListResponseBo;
//import com.get.common.result.ResponseBo;
//import com.get.resumecenter.vo.MediaAndAttachedDto;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//
///**
// * <AUTHOR>
// * @DATE: 2020/12/23
// * @TIME: 10:45
// * @Description:
// **/
//@Component
//@FeignClient(name = "permission-center")
//public interface FeignPermissionService {
//
//    @GetMapping("permission/company/getAllCompanyDto")
//    ListResponseBo getAllCompanyDto() ;
//
//
//    /**
//     * @return com.get.common.result.ResponseBo<com.get.permissioncenter.vo.MediaAndAttachedDto>
//     * @Description: 获取公司logo
//     * @Param [voSearchBean]
//     * <AUTHOR>
//     */
//    @GetMapping("permission/company/getCompanyIcon")
//    ResponseBo<MediaAndAttachedDto> getCompanyIcon(@RequestParam("fkCompanyId") Long fkCompanyId) ;
//
//    /**
//     * @Description: 保存简历guid
//     * @Param [guid]
//     * @return com.get.common.result.ResponseBo
//     * <AUTHOR>
//     **/
//    @PostMapping("permission/staff/saveResumeGuid")
//    ResponseBo saveResumeGuid(@RequestParam("guid")String guid) ;
//
//    /**
//     * @Description: 删除简历guid
//     * @Param [guid]
//     * @return com.get.common.result.ResponseBo
//     * <AUTHOR>
//     **/
//    @PostMapping("permission/staff/deleteGuid")
//    ResponseBo deleteGuid(@RequestParam("guid") String guid) ;
//}

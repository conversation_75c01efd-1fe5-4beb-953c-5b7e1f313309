<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.registrationcenter.dao.UserMapper">
    <resultMap id="BaseResultMap" type="com.get.registrationcenter.entity.User">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_platform_type" jdbcType="VARCHAR" property="fkPlatformType"/>
        <result column="login_id" jdbcType="VARCHAR" property="loginId"/>
        <result column="login_ps" jdbcType="VARCHAR" property="loginPs"/>
        <result column="num" jdbcType="VARCHAR" property="num"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="name_en" jdbcType="VARCHAR" property="nameEn"/>
        <result column="family_name_py" jdbcType="VARCHAR" property="familyNamePy"/>
        <result column="first_name_py" jdbcType="VARCHAR" property="firstNamePy"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="gender" jdbcType="INTEGER" property="gender"/>
        <result column="birthday" jdbcType="DATE" property="birthday"/>
        <result column="identity_card" jdbcType="VARCHAR" property="identityCard"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="mobile_area_code" jdbcType="VARCHAR" property="mobileAreaCode"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId"/>
        <result column="fk_area_state_id" jdbcType="BIGINT" property="fkAreaStateId"/>
        <result column="fk_area_city_id" jdbcType="BIGINT" property="fkAreaCityId"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="qq" jdbcType="VARCHAR" property="qq"/>
        <result column="wechat" jdbcType="VARCHAR" property="wechat"/>
        <result column="whatsapp" jdbcType="VARCHAR" property="whatsapp"/>
        <result column="wechat_nickname" jdbcType="VARCHAR" property="wechatNickname"/>
        <result column="wechat_icon_url" jdbcType="VARCHAR" property="wechatIconUrl"/>
        <result column="wechat_openid" jdbcType="VARCHAR" property="wechatOpenid"/>
        <result column="is_active" jdbcType="BIT" property="isActive"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="position" jdbcType="VARCHAR" property="position"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        fk_platform_type,
        login_id,
        login_ps,
        num,
        name,
        name_en,
        family_name_py,
        first_name_py,
        nickname,
        gender,
        birthday,
        identity_card,
        mobile,
        mobile_area_code,
        email,
        fk_area_country_id,
        fk_area_state_id,
        fk_area_city_id,
        zip_code,
        address,
        qq,
        wechat,
        whatsapp,
        wechat_nickname,
        wechat_icon_url,
        wechat_openid,
        company,
        position,
        is_active,
        gmt_create,
        gmt_create_user,
        gmt_modified,
        gmt_modified_user
    </sql>
</mapper>
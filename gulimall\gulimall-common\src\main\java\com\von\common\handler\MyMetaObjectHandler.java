package com.von.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    /// 拦截器调整
   /* @Override
    public void insertFill(MetaObject metaObject) {
        this.exeFill("gmtCreate", new Date(), metaObject);
        this.exeFill("gmtModified", new Date(), metaObject);
        this.exeFill("ii", 11, metaObject);
    }

    // TODO: 问题测试
    // 需求 空值插入, 非空根据注解判断
    public void exeFill(String fieldName, Object fieldVal, MetaObject metaObject){
        if (ObjectUtil.isNotNull(metaObject) && metaObject.hasSetter(fieldName) && metaObject.hasGetter(fieldName)) {
            Object classObj = metaObject.getOriginalObject();
            Field[] declaredFields;
            boolean isUpData=false;
            if(fieldName.indexOf("et.")!=-1){
                Class<?> et = metaObject.getGetterType("et");
                declaredFields = et.getDeclaredFields();
                fieldName=fieldName.substring(3);
                isUpData=true;
            }else {
                declaredFields=classObj.getClass().getDeclaredFields();
            }
            if(declaredFields!=null){
                for(Field field:declaredFields){
                    if(field.getName().equals(fieldName)){
                        TableField tableField = field.getAnnotation(TableField.class);
                        Object fieldName2 = getFieldValByName(fieldName, metaObject);
                        if(tableField!=null && tableField.fill()!= FieldFill.DEFAULT && (isUpData && fieldName2==null?true:false)){
                            this.setFieldValByName(fieldName, fieldVal, metaObject);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.exeFill("et.gmtModified", new Date(), metaObject);
        this.exeFill("et.ii", 44, metaObject);
    }*/

    @Override
    public void insertFill(MetaObject metaObject) {
        this.setFieldValByName("createTime", new Date(), metaObject);
        this.setFieldValByName("updateTime", new Date(), metaObject);
        this.setFieldValByName("modifyTime", new Date(), metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("updateTime", new Date(), metaObject);
        this.setFieldValByName("modifyTime", new Date(), metaObject);
    }

}

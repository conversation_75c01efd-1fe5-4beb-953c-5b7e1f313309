<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MInstitutionHighCommissionMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MInstitutionHighCommissionEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkInstitutionId" column="fk_institution_id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="recommendInfo" column="recommend_info" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="searchPage" resultType="com.get.partnercenter.vo.HighCommissionVo">
            SELECT mInstitutionHighCommission.*,
                   mInstitution.name AS institutionName,
                   company.num AS companyName,
                   (
                       SELECT  CONCAT(#{query.mMageAddress}, partnerFile.file_key) from app_partner_center.s_media_and_attached sAttached
                                                                                             INNER JOIN app_file_center.m_file_partner partnerFile ON partnerFile.file_guid = sAttached.fk_file_guid
                       WHERE sAttached.fk_table_name='m_institution_high_commission'    AND sAttached.fk_table_id=mInstitutionHighCommission.id limit 1
                   ) AS file_key
        <!--
        ,
        uAgentCommissionType.type_name  AS agentTypeName -->

 FROM  app_partner_center.m_institution_high_commission mInstitutionHighCommission
 LEFT JOIN ais_institution_center.m_institution mInstitution ON mInstitution.id=mInstitutionHighCommission.fk_institution_id
<!-- LEFT JOIN ais_pmp2_center.u_agent_commission_type uAgentCommissionType ON uAgentCommissionType.id=mInstitutionHighCommission.fk_agent_commission_type_id-->
            LEFT JOIN ais_permission_center.m_company company ON mInstitutionHighCommission.fk_company_id=company.id

            <if test="query.institutionProviderName!=null  and query.institutionProviderName!=''">
            INNER JOIN (
                SELECT distinct rInstitutionProviderInstitution.fk_institution_id  FROM
                    ais_institution_center.r_institution_provider_institution rInstitutionProviderInstitution
                INNER JOIN ais_institution_center.m_institution_provider mInstitutionProvider ON mInstitutionProvider.id=rInstitutionProviderInstitution.fk_institution_provider_id
                     WHERE 1=1  AND
                    (
                    mInstitutionProvider.name like concat("%",#{query.institutionProviderName},"%") OR mInstitutionProvider.name_chn like concat("%",#{query.institutionProviderName},"%")
                    )
                ) a  ON a.fk_institution_id=mInstitution.id
            </if>


        WHERE 1=1
        <if test="query.fkCompanyId != null ">
            AND mInstitutionHighCommission.fk_company_id=#{query.fkCompanyId}
        </if>

        <if test="query.status!=null">
            AND mInstitutionHighCommission.status=#{query.status}
        </if>

        <if test="query.institutionName!=null  and query.institutionName!=''">
            AND
            (
            mInstitution.name like concat("%",#{query.institutionName},"%") OR mInstitution.name_chn like concat("%",#{query.institutionName},"%")
            )
        </if>
        <if test="query.areAcountryId!=null ">
            AND mInstitution.fk_area_country_id =#{query.areAcountryId}
        </if>
        <if test="query.fkAgentCommissionTypeId!=null">
            AND mInstitutionHighCommission.fk_agent_commission_type_id=#{query.fkAgentCommissionTypeId}
        </if>

        ORDER BY mInstitutionHighCommission.weight DESC ,mInstitutionHighCommission.gmt_create ASC


    </select>
    <select id="getDetail" resultType="com.get.partnercenter.vo.HighCommissionVo">
        SELECT mInstitutionHighCommission.*,
               mInstitution.name AS institutionName,
               company.name_chn AS companyName,
               (
                   SELECT  CONCAT(#{mMageAddress}, partnerFile.file_key) from app_partner_center.s_media_and_attached sAttached
                                                                                        INNER JOIN app_file_center.m_file_partner partnerFile ON partnerFile.file_guid = sAttached.fk_file_guid
                   WHERE sAttached.fk_table_name='m_institution_high_commission'    AND sAttached.fk_table_id=mInstitutionHighCommission.id limit 1
               ) AS file_key
        <!--
        ,
        uAgentCommissionType.type_name  AS agentTypeName-->

 FROM  app_partner_center.m_institution_high_commission mInstitutionHighCommission
           LEFT JOIN ais_institution_center.m_institution mInstitution ON mInstitution.id=mInstitutionHighCommission.fk_institution_id
        <!-- LEFT JOIN ais_pmp2_center.u_agent_commission_type uAgentCommissionType ON uAgentCommissionType.id=mInstitutionHighCommission.fk_agent_commission_type_id-->
         LEFT JOIN ais_permission_center.m_company company ON mInstitutionHighCommission.fk_company_id=company.id
WHERE mInstitutionHighCommission.id=#{id}


</select>
<select id="resultList" resultType="com.get.partnercenter.vo.HighCommissionComboxVo">

SELECT DISTINCT mInstitution.id AS  fkInstitutionId,
               mInstitution.name AS institutionName
FROM ais_institution_center.m_institution mInstitution
        INNER JOIN ais_pmp2_center.r_institution_provider_commission_plan_institution rCommissionPlanInstitution ON mInstitution.id=rCommissionPlanInstitution.fk_institution_id
        INNER JOIN ais_pmp2_center.m_institution_provider_commission_plan mProviderCommissionPlan ON mProviderCommissionPlan.id=rCommissionPlanInstitution.fk_institution_provider_commission_plan_id
WHERE mProviderCommissionPlan.is_active=1

</select>



<select id="resulOldPMPtList" resultType="com.get.partnercenter.vo.HighCommissionComboxVo">

SELECT DISTINCT mInstitution.id AS  fkInstitutionId,
               mInstitution.name AS institutionName
FROM ais_institution_center.m_institution mInstitution
        INNER JOIN
    ais_institution_center.u_area_country AS country ON mInstitution.fk_area_country_id = country.id
        INNER JOIN
    ais_mps_center.r_institution_provider_commission_institution rCommissionInstitution ON
        rCommissionInstitution.fk_institution_id = mInstitution.id
        INNER JOIN
    ais_mps_center.m_institution_provider_commission AS commission ON
        commission.id = rCommissionInstitution.fk_institution_provider_commission_id
WHERE commission.is_active=1 and commission.year = #{year}

</select>



<update id="updatePutAway">
UPDATE m_institution_high_commission
<if test="type!=null and type==1">
   SET status=0
   <if test="gmtModifiedUser!=null and gmtModifiedUser!=''">
       ,gmt_modified_user=#{gmtModifiedUser}
   </if>
   <if test="gmtModified!=null ">
       ,gmt_modified=#{gmtModified}
   </if>
</if>
<if test="type!=null and type==2">
   SET status=1
   <if test="gmtModifiedUser!=null and gmtModifiedUser!=''">
       ,gmt_modified_user=#{gmtModifiedUser}
   </if>
   <if test="gmtModified!=null ">
       ,gmt_modified=#{gmtModified}
   </if>
</if>


WHERE
id IN
<foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
   #{id}
</foreach>

</update>
</mapper>

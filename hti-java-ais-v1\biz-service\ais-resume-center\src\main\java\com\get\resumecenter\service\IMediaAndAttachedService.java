package com.get.resumecenter.service;

import com.get.filecenter.dto.FileDto;
import com.get.resumecenter.dto.MediaAndAttachedDto;
import com.get.resumecenter.vo.MediaAndAttachedVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 10:55
 * @Description: 媒体附件
 **/
public interface IMediaAndAttachedService {

    /**
     * 查询附件
     *
     * @param attachedVo
     * @return
     */
    List<MediaAndAttachedVo> getFile(MediaAndAttachedDto attachedVo);


    /**
     * 上传文件
     *
     * @param multipartFiles
     * @return
     * @
     */
    List<FileDto> upload(MultipartFile[] multipartFiles);

    /**
     * 上传附件
     *
     * @param multipartFiles
     * @return
     * @
     */
    List<FileDto> uploadAttached(MultipartFile[] multipartFiles);

    /**
     * 保存上传的文件
     *
     * @param mediaAndAttachedDto
     * @return
     */
    MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAndAttachedDto);


    /**
     * 删除中间表信息
     */

    void deleteById(Long id);


    /**
     * 修改媒体附件表的fk_table_id
     *
     * @param mediaId
     * @param fkTableId
     */
    void updateTableId(Long mediaId, Long fkTableId);


    /**
     * 通过附件GUID获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
    List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo);


    /**
     * @return void
     * @Description: 删除
     * @Param [attachedVo]
     * <AUTHOR>
     */
    void delete(MediaAndAttachedDto attachedVo);

    /**
     * @return com.get.resumecenter.vo.MediaAndAttachedDto
     * @Description: 修改
     * @Param [mediaAndAttacheds]
     * <AUTHOR>
     */
    void update(MediaAndAttachedDto mediaAndAttachedDto);
}

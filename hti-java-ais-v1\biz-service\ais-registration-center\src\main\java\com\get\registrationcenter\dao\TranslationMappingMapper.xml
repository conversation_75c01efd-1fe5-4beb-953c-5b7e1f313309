<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.registrationcenter.dao.TranslationMappingMapper">

  <insert id="insertSelective" parameterType="com.get.registrationcenter.entity.RegistrationTranslationMapping" keyProperty="id" useGeneratedKeys="true">
    insert into s_translation_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkTableName != null">
        fk_table_name,
      </if>
      <if test="fkColumnName != null">
        fk_column_name,
      </if>
      <if test="inputTitle != null">
        input_title,
      </if>
      <if test="inputType != null">
        input_type,
      </if>
      <if test="maxLength != null">
        max_length,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkTableName != null">
        #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkColumnName != null">
        #{fkColumnName,jdbcType=VARCHAR},
      </if>
      <if test="inputTitle != null">
        #{inputTitle,jdbcType=VARCHAR},
      </if>
      <if test="inputType != null">
        #{inputType,jdbcType=INTEGER},
      </if>
      <if test="maxLength != null">
        #{maxLength,jdbcType=INTEGER},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

</mapper>
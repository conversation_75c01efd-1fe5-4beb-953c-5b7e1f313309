spring.cloud.nacos.config.server-addr=localhost:8848

spring.cloud.nacos.discovery.server-addr=localhost:8848
spring.application.name=gulimall-auth-server
spring.session.store-type=redis
server.port=20000
server.error.include-binding-errors=always
server.error.include-message=always
server.servlet.session.timeout=30m
logging.level.com.von=info


# \u89E3\u51B3springboot \u9AD8\u7248\u672C\u51B2\u7A81\u95EE\u9898
# \u5F00\u542F\u5FAA\u73AF\u5F15\u7528, \u751F\u4EA7\u73AF\u5883\u4E0D\u63A8\u8350\u5F00\u542F
spring.main.allow-circular-references=true
# SpringBoot2.6.x\u4E0ESwagger2 3.0.0\u7248\u672C\u51B2\u7A81
spring.mvc.pathmatch.matching-strategy=ant_path_matcher
spring.cloud.nacos.config.import-check.enabled=false
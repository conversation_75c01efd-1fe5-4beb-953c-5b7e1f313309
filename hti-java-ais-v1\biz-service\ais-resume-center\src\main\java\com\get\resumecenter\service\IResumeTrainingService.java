package com.get.resumecenter.service;


import com.get.resumecenter.vo.ResumeTrainingVo;
import com.get.resumecenter.dto.ResumeTrainingDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 10:56
 * @Description: 简历培训经历
 **/
public interface IResumeTrainingService {

    /**
     * 根据简历id 查询所有的培训经历
     *
     * @param resumeId
     * @return
     * @
     */
    List<ResumeTrainingVo> getResumeTrainingListDto(Long resumeId);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    ResumeTrainingVo getResumeTrainingById(Long id);


    /**
     * 添加
     *
     * @param trainingVo
     * @return
     */
    Long addResumeTraining(ResumeTrainingDto trainingVo);

    /**
     * 修改
     *
     * @param trainingVo
     * @return
     */
    ResumeTrainingVo updateResumeTraining(ResumeTrainingDto trainingVo);

    /**
     * 删除
     *
     * @param id
     */
    void deleteResumeTraining(Long id);


}

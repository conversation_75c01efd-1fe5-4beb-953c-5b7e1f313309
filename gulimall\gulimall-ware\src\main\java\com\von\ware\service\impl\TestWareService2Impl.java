package com.von.ware.service.impl;

import com.von.ware.entity.WareInfoEntity;
import com.von.ware.service.TestWareService2;
import com.von.ware.service.WareInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2024/12/17 下午11:10
 * @Version 1.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class TestWareService2Impl implements TestWareService2 {


    private final WareInfoService wareInfoService;


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void testC() {
        String uuid = TestWareServiceImpl.uuidThreadLocal.get();
        log.info("方法C");
        WareInfoEntity ware = WareInfoEntity.builder().name(uuid).address("方法C").build();
        this.wareInfoService.save(ware);
    }

}

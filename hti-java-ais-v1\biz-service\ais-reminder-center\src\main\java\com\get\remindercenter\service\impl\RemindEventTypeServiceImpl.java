package com.get.remindercenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.dao.RemindEventTypeMapper;
import com.get.remindercenter.dao.RemindTemplateMapper;
import com.get.remindercenter.vo.RemindEventTypeVo;
import com.get.remindercenter.entity.RemindEventType;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.service.RemindEventTypeService;
import com.get.remindercenter.dto.RemindEventTypeListDto;
import com.get.remindercenter.dto.RemindEventTypeUpdateDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:43
 * Date: 2021/11/12
 * Description:提醒事件类型管理业务实现类
 */
@Service
public class RemindEventTypeServiceImpl implements RemindEventTypeService {

    @Resource
    private UtilService utilService;
    @Resource
    private RemindEventTypeMapper remindEventTypeMapper;
    @Resource
    private RemindTemplateMapper remindTemplateMapper;

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public List<RemindEventTypeVo> datas(RemindEventTypeListDto remindEventTypeListDto, Page page) {
//        Example example = new Example(RemindEventType.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<RemindEventType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(remindEventTypeListDto.getTypeName())) {
//            criteria.andLike("typeName","%"+remindEventTypeListDto.getTypeName()+"%");
            lambdaQueryWrapper.like(RemindEventType::getTypeName, remindEventTypeListDto.getTypeName());
        }
        if (GeneralTool.isNotEmpty(remindEventTypeListDto.getTypeKey())) {
//            criteria.andLike("typeKey","%"+remindEventTypeListDto.getTypeKey()+"%");
            lambdaQueryWrapper.like(RemindEventType::getTypeKey, remindEventTypeListDto.getTypeKey());
        }
        lambdaQueryWrapper.orderByDesc(RemindEventType::getViewOrder);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<RemindEventType> iPage = remindEventTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<RemindEventType> remindEventTypes = iPage.getRecords();
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(remindEventTypes)) {
            return new ArrayList<>();
        }
//        page.restPage(remindEventTypes);
        return BeanCopyUtils.copyListProperties(remindEventTypes, RemindEventTypeVo::new);
    }


    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public void add(RemindEventTypeUpdateDto remindEventTypeUpdateDto) {
        if (GeneralTool.isEmpty(remindEventTypeUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //查询类型key是否存在
//        Example example = new Example(RemindEventType.class);
//        example.createCriteria().andEqualTo("typeKey",remindEventTypeUpdateDto.getTypeKey());
        List<RemindEventType> remindEventTypes = remindEventTypeMapper.selectList(Wrappers.<RemindEventType>lambdaQuery().eq(RemindEventType::getTypeKey, remindEventTypeUpdateDto.getTypeKey()));
        if (GeneralTool.isNotEmpty(remindEventTypes)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("remind_event_type_key_exist"));
        }
        RemindEventType remindEventType = BeanCopyUtils.objClone(remindEventTypeUpdateDto, RemindEventType::new);
        utilService.updateUserInfoToEntity(remindEventType);
        Integer maxViewOrder = remindEventTypeMapper.getMaxViewOrder();
        remindEventType.setViewOrder(maxViewOrder);
        remindEventTypeMapper.insert(remindEventType);
    }


    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public void update(RemindEventTypeUpdateDto remindEventTypeUpdateDto) {
        if (GeneralTool.isEmpty(remindEventTypeUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        RemindEventType remindEventType = remindEventTypeMapper.selectById(remindEventTypeUpdateDto.getId());
        if (GeneralTool.isEmpty(remindEventType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        BeanUtils.copyProperties(remindEventTypeUpdateDto, remindEventType);
        utilService.updateUserInfoToEntity(remindEventType);
        remindEventTypeMapper.updateById(remindEventType);
    }


    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public RemindEventTypeVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        RemindEventType remindEventType = remindEventTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(remindEventType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(remindEventType, RemindEventTypeVo::new);
    }


    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //校验提醒模板是否有关联类型key，有的话不允许删除
        RemindEventType remindEventType = remindEventTypeMapper.selectById(id);
//        Example example = new Example(RemindTemplate.class);
//        example.createCriteria().andEqualTo("fkRemindEventTypeKey",remindEventType.getTypeKey());
//        List<RemindTemplate> remindTemplates = remindTemplateMapper.selectByExample(example);
        List<RemindTemplate> remindTemplates = remindTemplateMapper.selectList(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, remindEventType.getTypeKey()));
        if (GeneralTool.isNotEmpty(remindTemplates)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("remind_event_type_key_associated_template"));
        }
        remindEventTypeMapper.deleteById(id);
    }


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public void movingOrder(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        RemindEventType ro = remindEventTypeMapper.selectById(ids.get(0));
        Integer oneorder = ro.getViewOrder();
        RemindEventType rt = remindEventTypeMapper.selectById(ids.get(1));
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        remindEventTypeMapper.updateById(ro);
        remindEventTypeMapper.updateById(rt);
    }
}

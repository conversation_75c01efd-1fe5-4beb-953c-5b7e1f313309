package com.von.actuator;

import de.codecentric.boot.admin.server.config.EnableAdminServer;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@EnableAdminServer
@EnableDiscoveryClient
//@ComponentScan(value = "com.von", excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {MyThreadConfig.class}))
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
public class GulimallActuatorAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(GulimallActuatorAdminApplication.class, args);
    }

}

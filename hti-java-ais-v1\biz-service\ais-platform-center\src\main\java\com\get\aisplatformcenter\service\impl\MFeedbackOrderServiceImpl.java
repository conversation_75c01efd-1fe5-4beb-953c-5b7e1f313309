package com.get.aisplatformcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.MFeedbackOrderReplyMapper;
import com.get.aisplatformcenter.mapper.SMediaAndAttachedMapper;
import com.get.aisplatformcenter.service.MFeedbackOrderService;
import com.get.aisplatformcenter.mapper.MFeedbackOrderMapper;
import com.get.aisplatformcenter.util.TencentCloudUtils;
import com.get.aisplatformcenterap.dto.SMediaAndAttachedPublicDto;
import com.get.aisplatformcenterap.dto.work.MFeedbackOrderDto;
import com.get.aisplatformcenterap.dto.work.MFeedbackOrderReplyDto;
import com.get.aisplatformcenterap.entity.MFeedbackOrderEntity;
import com.get.aisplatformcenterap.entity.MFeedbackOrderReplyEntity;
import com.get.aisplatformcenterap.entity.UFeedbackOrderTypeEntity;
import com.get.aisplatformcenterap.vo.FileArray;
import com.get.aisplatformcenterap.vo.FilePubArray;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderDetailVo;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderReplyVo;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderVo;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【m_feedback_order】的数据库操作Service实现
* @createDate 2025-05-26 16:46:43
*/
@Service
public class MFeedbackOrderServiceImpl extends ServiceImpl<MFeedbackOrderMapper, MFeedbackOrderEntity>
    implements MFeedbackOrderService{
    @Resource
    private MFeedbackOrderMapper mFeedbackOrderMapper;
    @Resource
    private TencentCloudUtils tencentCloudUtils;
    @Resource
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;
    @Resource
    private MFeedbackOrderReplyMapper mFeedbackOrderReplyMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public List<MFeedbackOrderVo> searchPage(MFeedbackOrderDto params, Page page) {

        //员工id + 业务下属员工ids
        Long staffId = SecureUtil.getStaffId();
        IPage<MFeedbackOrderVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));



        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);
        params.setStaffFollowerIds(staffFollowerIds);

        List<MFeedbackOrderVo> agentDtos = mFeedbackOrderMapper.searchPage(pages, params);
        page.setAll((int) pages.getTotal());
        return agentDtos;
    }

    @Override
    public MFeedbackOrderDetailVo getDetail(Long id) {
        MFeedbackOrderDetailVo result = mFeedbackOrderMapper.getDetail(id);


        SMediaAndAttachedPublicDto params=new SMediaAndAttachedPublicDto(); //反馈附件

        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setMMageAddress(urlBase);
        params.setFkTableName("m_feedback_order");
        params.setFkTableId(id);
        List<FilePubArray> fileArrays=sMediaAndAttachedMapper.selectPublicPlatformFileArrays(params);
        if(ObjectUtils.isNotEmpty(fileArrays)){
            result.setFileArray(fileArrays);

        }

        List<MFeedbackOrderReplyEntity> replyVoList=mFeedbackOrderReplyMapper.selectList(new LambdaQueryWrapper<MFeedbackOrderReplyEntity>()
                .eq(MFeedbackOrderReplyEntity::getFkFeedbackOrderId,id)
                .orderByDesc(MFeedbackOrderReplyEntity::getGmtCreate));
        Long staffId = SecureUtil.getStaffId();
        if(ObjectUtils.isNotEmpty(replyVoList)){
            List<MFeedbackOrderReplyVo> resultDetailVo=new ArrayList<>();


            for(MFeedbackOrderReplyEntity orderReply:replyVoList){
                MFeedbackOrderReplyVo orderReplyTmpVo=BeanCopyUtils.objClone(orderReply,MFeedbackOrderReplyVo::new);

                if(ObjectUtils.isNotEmpty(orderReplyTmpVo.getFkStaffId()) && staffId.equals(orderReplyTmpVo.getFkStaffId())
                        ){
                    orderReplyTmpVo.setIsLoginUser("1");
                }else{
                    orderReplyTmpVo.setIsLoginUser("2");
                }


                resultDetailVo.add(orderReplyTmpVo);
            }

            result.setReplyVoList(resultDetailVo);
        }


        return result;
    }

    @Override
    public void solve(Long id) {
        MFeedbackOrderEntity mFeedbackOrder =mFeedbackOrderMapper.selectById(id);
        if(mFeedbackOrder.getStatus()==2 || mFeedbackOrder.getStatus()==3){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","反馈单已关闭!"));

        }
        mFeedbackOrder.setStatus(2);
        utilService.updateUserInfoToEntity(mFeedbackOrder);
        mFeedbackOrderMapper.updateById(mFeedbackOrder);

    }


    @Override
    public Long feedDack(MFeedbackOrderReplyDto replydto) {

        MFeedbackOrderEntity mFeedbackOrder=mFeedbackOrderMapper.selectById(replydto.getFkFeedbackOrderId());
        if(ObjectUtils.isEmpty(mFeedbackOrder)){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","工单不存在!"));
        }
        if(ObjectUtils.isNotEmpty(mFeedbackOrder.getStatus()) && mFeedbackOrder.getStatus()==0){
            mFeedbackOrder.setStatus(1);
            mFeedbackOrderMapper.updateById(mFeedbackOrder);
        }
        MFeedbackOrderReplyEntity mFeedbackOrderReplyEntity = BeanCopyUtils.objClone(replydto,MFeedbackOrderReplyEntity::new);
        UserInfo user = GetAuthInfo.getUser();
        mFeedbackOrderReplyEntity.setFkStaffId(user.getStaffId());

        utilService.updateUserInfoToEntity(mFeedbackOrderReplyEntity);
        mFeedbackOrderReplyMapper.insert(mFeedbackOrderReplyEntity);
        return mFeedbackOrderReplyEntity.getId();
    }

    @Override
    public List<UFeedbackOrderTypeEntity> getUFeedbackOrderType() {
        List<UFeedbackOrderTypeEntity> uFeedbackOrderTypeEntities=mFeedbackOrderMapper.getUFeedbackOrderType();

        return uFeedbackOrderTypeEntities;
    }


}





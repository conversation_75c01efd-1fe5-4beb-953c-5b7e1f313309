package com.get.salecenter.component;

import com.get.core.tool.api.Result;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * @author: Hardy
 * @create: 2022/7/8 10:27
 * @verison: 1.0
 * @description:
 */
@Component
public class FileDownloadHelper {

    @Resource
    private IFileCenterClient fileCenterClient;

    @Async("saleTaskExecutor")
    public CompletableFuture<SaleFileDto> doDownloadSaleFile(FileVo fileVo){
        Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
        SaleFileDto data = result.getData();
        return CompletableFuture.completedFuture(data);
    }
}

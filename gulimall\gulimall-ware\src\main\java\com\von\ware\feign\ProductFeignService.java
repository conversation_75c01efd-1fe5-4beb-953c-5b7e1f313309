package com.von.ware.feign;

import com.von.common.utils.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/10/30 21:25
 * @Version 1.0
 */
@FeignClient("gulimall-product")
public interface ProductFeignService {

    @GetMapping("/product/skuinfo/findListByIds")
    R findListByIds(@RequestParam Set<Long> ids);

}

package com.get.remindercenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.dao.ContactPersonTypeMapper;
import com.get.remindercenter.vo.ContactPersonTypeVo;
import com.get.remindercenter.entity.RemindContactPersonType;
import com.get.remindercenter.service.IContactPersonTypeService;
import com.get.remindercenter.dto.ContactPersonTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/7/22 10:49
 * @verison: 1.0
 * @description: 联系人类型管理实现类
 */
@Service
public class ContactPersonTypeServiceImpl implements IContactPersonTypeService {
    @Resource
    private ContactPersonTypeMapper contactPersonTypeMapper;
    @Resource
    private UtilService utilService;


    @Override
    public ContactPersonTypeVo findContactPersonTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        RemindContactPersonType contactPersonType = contactPersonTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(contactPersonType, ContactPersonTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<ContactPersonTypeDto> contactPersonTypeDtos) {
        if (GeneralTool.isEmpty(contactPersonTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = contactPersonTypeMapper.getMaxViewOrder();
        for (ContactPersonTypeDto contactPersonTypeDto : contactPersonTypeDtos) {
            if (GeneralTool.isEmpty(contactPersonTypeDto.getId())) {
                if (validateAdd(contactPersonTypeDto)) {
                    RemindContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeDto, RemindContactPersonType::new);
                    contactPersonType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(contactPersonType);
                    int i = contactPersonTypeMapper.insert(contactPersonType);
                    if (i < 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(contactPersonTypeDto)) {
                    RemindContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeDto, RemindContactPersonType::new);
                    utilService.updateUserInfoToEntity(contactPersonType);
                    contactPersonTypeMapper.updateById(contactPersonType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findContactPersonTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        contactPersonTypeMapper.deleteById(id);
    }

    @Override
    public ContactPersonTypeVo updateContactPersonType(ContactPersonTypeDto contactPersonTypeDto) {
        RemindContactPersonType contactPersonType = BeanCopyUtils.objClone(contactPersonTypeDto, RemindContactPersonType::new);
        if (contactPersonTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        RemindContactPersonType result = contactPersonTypeMapper.selectById(contactPersonTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        if (validateUpdate(contactPersonTypeDto)) {
            utilService.updateUserInfoToEntity(contactPersonType);
            contactPersonTypeMapper.updateById(contactPersonType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findContactPersonTypeById(contactPersonType.getId());
    }

    @Override
    public List<ContactPersonTypeVo> getContactPersonTypes(ContactPersonTypeDto contactPersonTypeDto, Page page) {
//        Example example = new Example(ContactPersonType.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<RemindContactPersonType> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        if (GeneralTool.isNotEmpty(contactPersonTypeDto)) {
            if (GeneralTool.isNotEmpty(contactPersonTypeDto.getTypeName())) {
//                criteria.andLike("typeName", "%" + contactPersonTypeDto.getTypeName() + "%");
                lambdaQueryWrapper.like(RemindContactPersonType::getTypeName, contactPersonTypeDto.getTypeName());
            }
        }
        lambdaQueryWrapper.orderByDesc(RemindContactPersonType::getViewOrder);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<RemindContactPersonType> pages = contactPersonTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<RemindContactPersonType> contactPersonTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
//        page.restPage(contactPersonTypes);

        List<ContactPersonTypeVo> convertDatas = new ArrayList<>();
        for (RemindContactPersonType contactPersonType : contactPersonTypes) {
            ContactPersonTypeVo contactPersonTypeVo = BeanCopyUtils.objClone(contactPersonType, ContactPersonTypeVo::new);
            convertDatas.add(contactPersonTypeVo);
        }
        return convertDatas;
    }

    @Override
    public List<ContactPersonTypeVo> getContactPersonTypes() {
//        Example example = new Example(ContactPersonType.class);
//        example.orderBy("viewOrder").desc();
//        List<ContactPersonType> contactPersonTypes = contactPersonTypeMapper.selectByExample(example);
        List<RemindContactPersonType> contactPersonTypes = contactPersonTypeMapper.selectList(Wrappers.<RemindContactPersonType>lambdaQuery().orderByDesc(RemindContactPersonType::getViewOrder));
        return contactPersonTypes.stream()
                .map(contactPersonType -> BeanCopyUtils.objClone(contactPersonType, ContactPersonTypeVo::new)).collect(Collectors.toList());
    }


    @Override
    public void movingOrder(List<ContactPersonTypeDto> contactPersonTypeDtos) {
        if (GeneralTool.isEmpty(contactPersonTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        RemindContactPersonType ro = BeanCopyUtils.objClone(contactPersonTypeDtos.get(0), RemindContactPersonType::new);
        Integer oneorder = ro.getViewOrder();
        RemindContactPersonType rt = BeanCopyUtils.objClone(contactPersonTypeDtos.get(1), RemindContactPersonType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        contactPersonTypeMapper.updateById(ro);
        contactPersonTypeMapper.updateById(rt);
    }

    private boolean validateAdd(ContactPersonTypeDto contactPersonTypeDto) {
//        Example example = new Example(ContactPersonType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeKey", contactPersonTypeDto.getTypeKey());
        List<RemindContactPersonType> list = this.contactPersonTypeMapper.selectList(Wrappers.<RemindContactPersonType>lambdaQuery().eq(RemindContactPersonType::getTypeKey, contactPersonTypeDto.getTypeKey()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(ContactPersonTypeDto contactPersonTypeDto) {
//        Example example = new Example(ContactPersonType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeKey", contactPersonTypeDto.getTypeKey());
//        List<ContactPersonType> list = this.contactPersonTypeMapper.selectByExample(example);
        List<RemindContactPersonType> list = this.contactPersonTypeMapper.selectList(Wrappers.<RemindContactPersonType>lambdaQuery().eq(RemindContactPersonType::getTypeKey, contactPersonTypeDto.getTypeKey()));
        System.out.println(list);
        return list.size() <= 0 || list.get(0).getId().equals(contactPersonTypeDto.getId());
    }
}

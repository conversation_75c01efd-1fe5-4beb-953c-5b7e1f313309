<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State>
            <id />
          </State>
          <State>
            <id>Android</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Correctness</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Correctness &gt; Messages</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Internationalization</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Internationalization &gt; Bidirectional Text</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Performance</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Security</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Usability</id>
          </State>
          <State>
            <id>Android &gt; Lint &gt; Usability &gt; Icons</id>
          </State>
          <State>
            <id>Android Lint for Kotlin</id>
          </State>
          <State>
            <id>Application Server Specific Inspections</id>
          </State>
          <State>
            <id>Batch Applications Issues</id>
          </State>
          <State>
            <id>CDI(Contexts and Dependency Injection) issues</id>
          </State>
          <State>
            <id>DOM issuesJavaScript</id>
          </State>
          <State>
            <id>Google Web Toolkit issues</id>
          </State>
          <State>
            <id>Gradle</id>
          </State>
          <State>
            <id>HTML</id>
          </State>
          <State>
            <id>Hibernate Issues</id>
          </State>
          <State>
            <id>Internationalization issues</id>
          </State>
          <State>
            <id>Internationalization issuesJava</id>
          </State>
          <State>
            <id>JBoss Seam issues</id>
          </State>
          <State>
            <id>JPA issues</id>
          </State>
          <State>
            <id>Java</id>
          </State>
          <State>
            <id>Java EE issues</id>
          </State>
          <State>
            <id>JavaScript</id>
          </State>
          <State>
            <id>Kotlin</id>
          </State>
          <State>
            <id>Maven</id>
          </State>
          <State>
            <id>OSGi</id>
          </State>
          <State>
            <id>Pages Navigation Model</id>
          </State>
          <State>
            <id>Plugin DevKit</id>
          </State>
          <State>
            <id>Portability issuesJava</id>
          </State>
          <State>
            <id>Probable bugsGradle</id>
          </State>
          <State>
            <id>Probable bugsJava</id>
          </State>
          <State>
            <id>RELAX NG</id>
          </State>
          <State>
            <id>SetupSpring CoreSpring</id>
          </State>
          <State>
            <id>Spring</id>
          </State>
          <State>
            <id>Spring CoreSpring</id>
          </State>
          <State>
            <id>Spring IntegrationSpring</id>
          </State>
          <State>
            <id>Spring SecuritySpring</id>
          </State>
          <State>
            <id>Spring Web ServicesSpring</id>
          </State>
          <State>
            <id>Struts</id>
          </State>
          <State>
            <id>Struts 2Struts</id>
          </State>
          <State>
            <id>TestNGJava</id>
          </State>
          <State>
            <id>Web Services</id>
          </State>
          <State>
            <id>WebSocket issues</id>
          </State>
          <State>
            <id>XML</id>
          </State>
          <State>
            <id>XMLSpring CoreSpring</id>
          </State>
          <State>
            <id>XPath</id>
          </State>
        </expanded-state>
        <selected-state>
          <State>
            <id>CheckTagEmptyBody</id>
          </State>
        </selected-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
  <component name="ReaderModeSettings">
    <option name="enabled" value="false" />
  </component>
</project>
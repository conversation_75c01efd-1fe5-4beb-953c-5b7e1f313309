package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.ExpenseClaimFormItemMapper;
import com.get.financecenter.dao.ExpenseClaimFormMapper;
import com.get.financecenter.dto.ExpenseClaimFormItemDto;
import com.get.financecenter.vo.ExpenseClaimFormVo;
import com.get.financecenter.vo.ExpenseClaimFormItemVo;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExpenseClaimFormItemService;
import com.get.financecenter.service.IExpenseClaimFormService;
import com.get.financecenter.service.IMediaAndAttachedService;
import com.get.financecenter.utils.MyStringUtils;
import com.get.financecenter.dto.ExpenseClaimFormDto;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.query.ExpenseClaimFormQueryDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/4/7 10:47
 * @verison: 1.0
 * @description:
 */
@Service
public class ExpenseClaimFormServiceImpl extends BaseServiceImpl<ExpenseClaimFormMapper, ExpenseClaimForm> implements IExpenseClaimFormService {
    @Resource
    private ExpenseClaimFormMapper expenseClaimFormMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private IExpenseClaimFormItemService expenseClaimFormItemService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ExpenseClaimFormItemMapper expenseClaimFormItemMapper;

    @Override
    public ExpenseClaimFormVo findExpenseClaimFormById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExpenseClaimForm expenseClaimForm = expenseClaimFormMapper.selectById(id);
        if (GeneralTool.isEmpty(expenseClaimForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //111111 修改为feign接口调用
        Result<Map<Long, Integer>> result = workflowCenterClient.getFromIdsByStaffId(SecureUtil.getStaffId(), TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key);
        Map<Long, Integer> map = result.getData();
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key, Collections.singletonList(id));
        Result<Map<Long, ActRuTaskVo>> result1 = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = result1.getData();
        ExpenseClaimFormVo expenseClaimFormVo = BeanCopyUtils.objClone(expenseClaimForm, ExpenseClaimFormVo::new);
        expenseClaimFormVo
                //公司名称
                .setCompanyName(permissionCenterClient.getCompanyNameById(expenseClaimFormVo.getFkCompanyId()).getData())
                //部门名称
                .setDepartmentName(permissionCenterClient.getDepartmentNameById(expenseClaimFormVo.getFkDepartmentId()).getData())
                //报销人名称
                .setStaffName(permissionCenterClient.getStaffName(expenseClaimFormVo.getFkStaffId()).getData())
                //币种名称
                .setCurrencyTypeName(currencyTypeService.getCurrencyNameByNum(expenseClaimFormVo.getFkCurrencyTypeNum()))
                .setFkStaffIdVouchCreatedName(permissionCenterClient.getStaffName(expenseClaimFormVo.getFkStaffIdVouchCreated()).getData());

        //报销单状态
        if (GeneralTool.isEmpty(map.get(expenseClaimFormVo.getId()))) {
            //我的申请列表和所有表单列表点详情进去的话，该表单又不属于登录人 需要给这个
            expenseClaimFormVo.setExpenseClaimFormStatus(2);
        } else {
            expenseClaimFormVo.setExpenseClaimFormStatus(map.get(expenseClaimFormVo.getId()));
        }
        //流程对象信息
        ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(id);
        if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
            expenseClaimFormVo.setTaskId(Long.valueOf(actRuTaskVo.getId()));
        }
        if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
            expenseClaimFormVo.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
        }
        if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
            expenseClaimFormVo.setTaskVersion(actRuTaskVo.getTaskVersion());
        }
        //表单明细对象集合
        List<ExpenseClaimFormItemVo> expenseClaimFormItemVos = expenseClaimFormItemService.getDtoByExpenseClaimFormId(id);
        expenseClaimFormVo.setExpenseClaimFormItemDtoList(expenseClaimFormItemVos);
        //报销总金额
        expenseClaimFormVo.setAmountSum(getAmountSum(expenseClaimFormVo));

        Result<HiCommentFeignVo> result2 = workflowCenterClient.getHiComment(id, TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key);
        if (!result2.isSuccess()) {
            throw new GetServiceException(result2.getMessage());
        }
        HiCommentFeignVo hiComment = result2.getData();
        if (GeneralTool.isNotEmpty(hiComment)) {
            expenseClaimFormVo.setAgreeButtonType(hiComment.getAgreeButtonType());
            expenseClaimFormVo.setRefuseButtonType(hiComment.getRefuseButtonType());
        }
        return expenseClaimFormVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addExpenseClaimForm(ExpenseClaimFormDto expenseClaimFormDto) {
        if (GeneralTool.isEmpty(expenseClaimFormDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ExpenseClaimForm expenseClaimForm = BeanCopyUtils.objClone(expenseClaimFormDto, ExpenseClaimForm::new);
        expenseClaimForm.setFkStaffId(SecureUtil.getStaffId());
        //待发起状态
        expenseClaimForm.setStatus(0);
        expenseClaimForm.setIsVouchCreated(false);
        utilService.updateUserInfoToEntity(expenseClaimForm);
        expenseClaimFormMapper.insert(expenseClaimForm);
        //获取表单编号并设置
        String expenseClaimFormNum = MyStringUtils.getFormNum("ECF", expenseClaimForm.getId());
        expenseClaimForm.setNum(expenseClaimFormNum);
        expenseClaimFormMapper.updateById(expenseClaimForm);

        Long id = expenseClaimForm.getId();
        //保存表单明细内容
        if (GeneralTool.isNotEmpty(expenseClaimFormDto.getExpenseClaimFormItemVos())) {
            for (ExpenseClaimFormItemDto expenseClaimFormItemDto : expenseClaimFormDto.getExpenseClaimFormItemVos()) {
                ExpenseClaimFormItem expenseClaimFormItem = BeanCopyUtils.objClone(expenseClaimFormItemDto, ExpenseClaimFormItem::new);
                expenseClaimFormItem.setFkExpenseClaimFormId(id);
                utilService.updateUserInfoToEntity(expenseClaimFormItem);
                expenseClaimFormItemMapper.insert(expenseClaimFormItem);
            }
        }

        return expenseClaimForm.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ExpenseClaimFormVo updateExpenseClaimForm(ExpenseClaimFormDto expenseClaimFormDto) {
        if (expenseClaimFormDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ExpenseClaimForm result = expenseClaimFormMapper.selectById(expenseClaimFormDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ExpenseClaimForm expenseClaimFeeType = BeanCopyUtils.objClone(expenseClaimFormDto, ExpenseClaimForm::new);
        utilService.updateUserInfoToEntity(expenseClaimFeeType);
        expenseClaimFormMapper.updateById(expenseClaimFeeType);
        //保存表单明细内容(先删后增)
        expenseClaimFormItemService.deleteByFkid(expenseClaimFormDto.getId());
        if (GeneralTool.isNotEmpty(expenseClaimFormDto.getExpenseClaimFormItemVos())) {
            for (ExpenseClaimFormItemDto expenseClaimFormItemDto : expenseClaimFormDto.getExpenseClaimFormItemVos()) {
                ExpenseClaimFormItem expenseClaimFormItem = BeanCopyUtils.objClone(expenseClaimFormItemDto, ExpenseClaimFormItem::new);
                expenseClaimFormItem.setFkExpenseClaimFormId(expenseClaimFormDto.getId());
                utilService.updateUserInfoToEntity(expenseClaimFormItem);
                expenseClaimFormItemMapper.insert(expenseClaimFormItem);
            }
        }
        return findExpenseClaimFormById(expenseClaimFormDto.getId());
    }

    @Override
    public List<ExpenseClaimFormVo> getExpenseClaimForms(ExpenseClaimFormQueryDto expenseClaimFormVo, Page page) {
        if (GeneralTool.isNotEmpty(expenseClaimFormVo.getFkCompanyIds())) {
            if (!SecureUtil.validateCompanys(expenseClaimFormVo.getFkCompanyIds())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
        }
        //111111 待修改
        Result<Map<Long, Integer>> result = workflowCenterClient.getFromIdsByStaffId(SecureUtil.getStaffId(), TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        List<ExpenseClaimForm> expenseClaimForms = getExpenseClaimFormsByExample(expenseClaimFormVo, page, result.getData());
        List<ExpenseClaimFormVo> convertDatas = new ArrayList<>();
        //公司id集合
        Set<Long> companyIds = new HashSet<>();
        //部门id集合
        Set<Long> departmentIds = new HashSet<>();
//        //办公室id集合
//        Set<Long> officeIds = new HashSet<>();
        //报销人id集合
        Set<Long> staffIds = new HashSet<>();
        //报销单id集合
        List<Long> expenseClaimFormIds = new ArrayList<>();
        //获取各自集合的值
        for (ExpenseClaimForm expenseClaimForm : expenseClaimForms) {
            companyIds.add(expenseClaimForm.getFkCompanyId());
            departmentIds.add(expenseClaimForm.getFkDepartmentId());
//            officeIds.add(expenseClaimForm.getFkOfficeId());
            staffIds.add(expenseClaimForm.getFkStaffId());
            expenseClaimFormIds.add(expenseClaimForm.getId());
        }
        //feign调用 获取公司id-name的map
        companyIds.removeIf(Objects::isNull);
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        //feign调用 获取部门id-name的map
        departmentIds.removeIf(Objects::isNull);
        Map<Long, String> departmentNameMap = permissionCenterClient.getDepartmentNamesByIds(departmentIds).getData();
        //feign调用 获取办公室id-name的map
//        officeIds.removeIf(Objects::isNull);
//        Map<Long, String> officeNameMap = permissionCenterClient.getofficeNamesByIds(officeIds).getData();
        //feign调用 获取报销人id-name的map
        staffIds.removeIf(Objects::isNull);
        Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIds).getData();
        //feign调用 获取流程方面dto 报销单id-actRuTaskDot的map
        expenseClaimFormIds.removeIf(Objects::isNull);
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key, expenseClaimFormIds);
        Result<Map<Long, ActRuTaskVo>> result1 = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = result1.getData();
        //币种编号nums
        Set<String> currencyTypeNums = expenseClaimForms.stream().map(ExpenseClaimForm::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        }

        for (ExpenseClaimForm expenseClaimForm : expenseClaimForms) {
            ExpenseClaimFormVo expenseClaimFormDto = BeanCopyUtils.objClone(expenseClaimForm, ExpenseClaimFormVo::new);
            expenseClaimFormDto
                    //公司名称
                    .setCompanyName(companyNameMap.get(expenseClaimFormDto.getFkCompanyId()))
                    //部门名称
                    .setDepartmentName(departmentNameMap.get(expenseClaimFormDto.getFkDepartmentId()))
                    //办公室名称
//                    .setOfficeName(officeNameMap.get(expenseClaimFormDto.getFkOfficeId()))
                    //申请人名称
                    .setStaffName(staffNameMap.get(expenseClaimFormDto.getFkStaffId()))
                    //表单明细对象
                    .setExpenseClaimFormItemDtoList(expenseClaimFormItemService.getDtoByExpenseClaimFormId(expenseClaimFormDto.getId()))
                    //币种名称
                    .setCurrencyTypeName(currencyNamesByNums.get(expenseClaimFormDto.getFkCurrencyTypeNum()))
                    //报销单状态
                    .setExpenseClaimFormStatus(result.getData().get(expenseClaimFormDto.getId()));
            //流程对象
            ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(expenseClaimFormDto.getId());
            //正在进行的任务id
            if (GeneralTool.isNotEmpty(actRuTaskVo.getId())) {
                expenseClaimFormDto.setTaskId(Long.valueOf(actRuTaskVo.getId()));
            }
            //流程实例id
            if (GeneralTool.isNotEmpty(actRuTaskVo.getProcInstId())) {
                expenseClaimFormDto.setProcInstId(Long.valueOf(actRuTaskVo.getProcInstId()));
            }
            //任务版本
            if (GeneralTool.isNotEmpty(actRuTaskVo.getTaskVersion())) {
                expenseClaimFormDto.setTaskVersion(actRuTaskVo.getTaskVersion());
            }
            //获取报销金额总和
            BigDecimal amountSum = getAmountSum(expenseClaimFormDto);
            expenseClaimFormDto.setAmountSum(amountSum);
            convertDatas.add(expenseClaimFormDto);
        }
        return convertDatas;
    }

    private List<ExpenseClaimForm> getExpenseClaimFormsByExample(ExpenseClaimFormQueryDto expenseClaimFormVo, Page page, Map<Long, Integer> map) {
        LambdaQueryWrapper<ExpenseClaimForm> wrapper = new LambdaQueryWrapper();
        //不选所属公司时
        if (GeneralTool.isEmpty(expenseClaimFormVo) || GeneralTool.isEmpty(expenseClaimFormVo.getFkCompanyId())) {
            wrapper.in(ExpenseClaimForm::getFkCompanyId, SecureUtil.getCompanyIdsByStaffId(GetAuthInfo.getStaffId()));
        }
        if (GeneralTool.isNotEmpty(expenseClaimFormVo)) {
            //查询条件-所属公司 多选
            if (GeneralTool.isNotEmpty(expenseClaimFormVo.getFkCompanyIds())) {
                if (!SecureUtil.validateCompanys(expenseClaimFormVo.getFkCompanyIds())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.in(ExpenseClaimForm::getFkCompanyId, expenseClaimFormVo.getFkCompanyIds());
            }
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(expenseClaimFormVo.getFkCompanyId())) {
                if (!SecureUtil.validateCompany(expenseClaimFormVo.getFkCompanyId())) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
                }
                wrapper.eq(ExpenseClaimForm::getFkCompanyId, expenseClaimFormVo.getFkCompanyId());
            }
            //查询条件-所属部门
            if (GeneralTool.isNotEmpty(expenseClaimFormVo.getFkDepartmentId())) {
                wrapper.eq(ExpenseClaimForm::getFkDepartmentId, expenseClaimFormVo.getFkDepartmentId());
            }
            if (GeneralTool.isNotEmpty(expenseClaimFormVo.getStatus())) {
                wrapper.eq(ExpenseClaimForm::getStatus, expenseClaimFormVo.getStatus());
            }
            if (GeneralTool.isNotEmpty(expenseClaimFormVo.getGmtCreateUser())) {
                wrapper.eq(ExpenseClaimForm::getGmtCreateUser, expenseClaimFormVo.getGmtCreateUser());
            }
            //查询条件-表单编号
            if (GeneralTool.isNotEmpty(expenseClaimFormVo.getNum())) {
                wrapper.like(ExpenseClaimForm::getNum, expenseClaimFormVo.getNum());
            }
            //查询条件-开始日期
            if (GeneralTool.isNotEmpty(expenseClaimFormVo.getStartTime())) {
                wrapper.ge(ExpenseClaimForm::getGmtCreate, expenseClaimFormVo.getStartTime());
            }
            //查询条件-结束日期
            if (GeneralTool.isNotEmpty(expenseClaimFormVo.getEndTime())) {
                wrapper.le(ExpenseClaimForm::getGmtCreate, expenseClaimFormVo.getEndTime());
            }
            //根据不同的选择状态拼接不同的条件
            if ("0".equals(expenseClaimFormVo.getSelectStatus())) {
                //0表示要显示我的申请列表，即我创建的表单
                wrapper.eq(ExpenseClaimForm::getFkStaffId, GetAuthInfo.getStaffId());
            } else if ("1".equals(expenseClaimFormVo.getSelectStatus())) {
                //1表示所有表单

            } else if ("2".equals(expenseClaimFormVo.getSelectStatus())) {
                //2表示显示我的审批列表，即我操作过的表单都要显示
                List<Long> fromIds = new ArrayList<>(map.keySet());
                if (GeneralTool.isEmpty(fromIds)) {
                    fromIds.add(0L);
                }
                wrapper.in(ExpenseClaimForm::getId, fromIds);
            }
        }
        wrapper.orderByDesc(ExpenseClaimForm::getGmtCreate);
        IPage<ExpenseClaimForm> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ExpenseClaimForm> expenseClaimForms = pages.getRecords();
        page.setAll((int) pages.getTotal());
        return expenseClaimForms;
    }

    @Override
    public List<FMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<FMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<FMediaAndAttachedVo> mediaAndAttachedDtos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key);
            mediaAndAttachedDtos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedDtos;
    }

    /**
     * @return java.math.BigDecimal
     * @Description : 获取报销金额总和
     * @Param [expenseClaimFormDto]
     * <AUTHOR>
     */
    private BigDecimal getAmountSum(ExpenseClaimFormVo expenseClaimFormVo) {
        BigDecimal amountSum = BigDecimal.valueOf(0);
        for (ExpenseClaimFormItemVo expenseClaimFormItemVo : expenseClaimFormVo.getExpenseClaimFormItemDtoList()) {
            amountSum = amountSum.add(expenseClaimFormItemVo.getAmount());
        }
        return amountSum;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startProcess(Long companyId, Long businessKey, String procdefKey) {
        ExpenseClaimForm expenseClaimForm = expenseClaimFormMapper.selectById(businessKey);
        if (0 != expenseClaimForm.getStatus()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
        }
//        StaffVo staffDto = StaffContext.getStaff();
//        Long staffId = staffDto.getId();
        Map<String, Object> map = new HashMap<>();
        map.put("applyUser", GetAuthInfo.getStaffId());
        //开启流程后，更新表单状态
        Boolean result = workflowCenterClient.startProcess(String.valueOf(businessKey), procdefKey, String.valueOf(companyId), map).getData();
        if (!result) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
        }
        expenseClaimForm.setStatus(2);
        utilService.updateUserInfoToEntity(expenseClaimForm);
        expenseClaimFormMapper.updateById(expenseClaimForm);
    }

    @Override
    public void updateStatus(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ExpenseClaimForm expenseClaimForm = expenseClaimFormMapper.selectById(id);
        if (GeneralTool.isEmpty(expenseClaimForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (0 != expenseClaimForm.getStatus()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("process_already_start_cannot_Invalid"));
        }
        expenseClaimForm.setStatus(5);
        expenseClaimFormMapper.updateById(expenseClaimForm);
    }

    //    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = YException.class)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void getRevokeExpenseClaimForm(Long id, String summary) {
        ExpenseClaimForm expenseClaimForm = expenseClaimFormMapper.selectById(id);
        if (GeneralTool.isEmpty(expenseClaimForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        if (GeneralTool.isNotEmpty(expenseClaimFormMapper.getExistParentId(id))) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        List<ExpenseClaimFormItem> expenseClaimFormItems = expenseClaimFormItemMapper.getFormItemByExpenseClaimFormId(id);
        if (GeneralTool.isEmpty(expenseClaimFormItems)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        ExpenseClaimForm revocationForm;
        revocationForm = expenseClaimForm;
        revocationForm.setId(null);
        revocationForm.setStatus(ProjectExtraEnum.TO_BE_INITIATED.key);
        revocationForm.setGmtModified(null);
        revocationForm.setGmtModifiedUser(null);
        revocationForm.setFkExpenseClaimFormIdRevoke(id);
        utilService.updateUserInfoToEntity(revocationForm);
        expenseClaimFormMapper.insert(revocationForm);
        //获取表单编号并设置
        String expenseClaimFormNum = MyStringUtils.getFormNum("ECF",revocationForm.getId());
        revocationForm.setNum(expenseClaimFormNum);
        expenseClaimFormMapper.updateById(revocationForm);

        for (ExpenseClaimFormItem expenseClaimFormItem : expenseClaimFormItems) {
            ExpenseClaimFormItem formItem;
            formItem = expenseClaimFormItem;
            formItem.setId(null);
            formItem.setGmtModified(null);
            formItem.setGmtModifiedUser(null);
            formItem.setSummary(summary);
            formItem.setFkExpenseClaimFormId(revocationForm.getId());
            utilService.updateUserInfoToEntity(formItem);
            expenseClaimFormItemMapper.insert(formItem);
        }
        //成功则改状态
        ExpenseClaimForm parentExpenseClaimForm = expenseClaimFormMapper.selectById(id);
        if (GeneralTool.isEmpty(parentExpenseClaimForm)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //改为已撤单状态
        parentExpenseClaimForm.setStatus(ProjectExtraEnum.REVOKED.key);
        utilService.updateUserInfoToEntity(parentExpenseClaimForm);
        expenseClaimFormMapper.updateById(parentExpenseClaimForm);
    }

    /**
     * 根据id获取费用报销单
     * @param targetId
     * @return
     */
    @Override
    public ExpenseClaimForm getExpenseClaimFormById(Long targetId) {
        return expenseClaimFormMapper.selectById(targetId);
    }

    @Override
    public Boolean updateExpenseClaimFormStatus(ExpenseClaimForm expenseClaimForm) {
        utilService.updateUserInfoToEntity(expenseClaimForm);
        expenseClaimFormMapper.updateById(expenseClaimForm);
        return true;
    }

    /**
     * 获取费用报销单总金额
     * @param id
     * @return
     */
    @Override
    public BigDecimal getExpenseClaimFormTotalAmount(Long id) {
        return expenseClaimFormItemMapper.getExpenseClaimFormTotalAmount(id);
    }

//    /**
//     * @return java.util.List<java.lang.Long>
//     * @Description :获取登录人对应所有公司id集合
//     * @Param []
//     * <AUTHOR>
//     */
//    private List<Long> getCompanyIds() {
//        List<Long> companyIds = StaffContext.getStaff().getCompanyIds();
//        if (GeneralTool.isEmpty(companyIds)) {
//            companyIds.add(0L);
//        }
//        return companyIds;
//    }
}

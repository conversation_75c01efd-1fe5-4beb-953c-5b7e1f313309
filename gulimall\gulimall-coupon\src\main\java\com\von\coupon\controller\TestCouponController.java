package com.von.coupon.controller;

import cn.hutool.core.date.DateTime;
import com.von.common.utils.R;
import com.von.common.utils.TimeUtils;
import com.von.coupon.entity.SeckillSessionEntity;
import com.von.coupon.service.SeckillSessionService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2025/1/12 11:10
 * @Version 1.0
 */
@RestController
@Api(tags = "测试")
@RequiredArgsConstructor
@RequestMapping("/test")
public class TestCouponController {

    private final SeckillSessionService seckillSessionService;

    @GetMapping("/updateTimeSeckillSession")
    public R updateTimeSeckillSession() throws Exception {
        // 更新开始时间
        SeckillSessionEntity seckillSessionEntity = new SeckillSessionEntity();
        seckillSessionEntity.setId(1L);
        DateTime nowBeginTime = TimeUtils.getNowBeginTime();
        seckillSessionEntity.setStartTime(nowBeginTime);
        this.seckillSessionService.saveOrUpdate(seckillSessionEntity);

        // 更新结束时间
        SeckillSessionEntity seckillSessionEntity2 = new SeckillSessionEntity();
        seckillSessionEntity2.setId(2L);
        seckillSessionEntity2.setStartTime(TimeUtils.get2DayEndTime().toJdkDate());
        this.seckillSessionService.saveOrUpdate(seckillSessionEntity2);

        return R.ok();
    }

}

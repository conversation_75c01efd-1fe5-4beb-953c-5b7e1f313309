#服务器端口
server:
  port: 1098

#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: partnerdb
      datasource:
        partnerdb:
          url: ${get.datasource.gray.partnerdb.url}
          username: ${get.datasource.gray.partnerdb.username}
          password: ${get.datasource.gray.partnerdb.password}
        saledb:
          url: ${get.datasource.gray.saledb.url}
          username: ${get.datasource.gray.saledb.username}
          password: ${get.datasource.gray.saledb.password}
        permissiondb:
          url: ${get.datasource.gray.permissiondb.url}
          username: ${get.datasource.gray.permissiondb.username}
          password: ${get.datasource.gray.permissiondb.password}
        institutiondb:
          url: ${get.datasource.gray.institutiondb.url}
          username: ${get.datasource.gray.institutiondb.username}
          password: ${get.datasource.gray.institutiondb.password}
        systemdb:
          url: ${get.datasource.gray.systemdb.url}
          username: ${get.datasource.gray.systemdb.username}
          password: ${get.datasource.gray.systemdb.password}
        appfiledb:
          url: ${get.datasource.gray.appfiledb.url}
          username: ${get.datasource.gray.appfiledb.username}
          password: ${get.datasource.gray.appfiledb.password}
        pmp2db:
          url: ${get.datasource.gray.pmp2db.url}
          username: ${get.datasource.gray.pmp2db.username}
          password: ${get.datasource.gray.pmp2db.password}
        platformdb:
          url: ${get.datasource.gray.platformdb.url}
          username: ${get.datasource.gray.platformdb.username}
          password: ${get.datasource.gray.platformdb.password}
#服务器端口
server:
  port: 1098

#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: partnerdb
      datasource:
        partnerdb:
          url: ${get.datasource.prod.partnerdb.url}
          username: ${get.datasource.prod.partnerdb.username}
          password: ${get.datasource.prod.partnerdb.password}
        saledb:
          url: ${get.datasource.prod.saledb.url}
          username: ${get.datasource.prod.saledb.username}
          password: ${get.datasource.prod.saledb.password}
        permissiondb:
          url: ${get.datasource.prod.permissiondb.url}
          username: ${get.datasource.prod.permissiondb.username}
          password: ${get.datasource.prod.permissiondb.password}
        institutiondb:
          url: ${get.datasource.prod.institutiondb.url}
          username: ${get.datasource.prod.institutiondb.username}
          password: ${get.datasource.prod.institutiondb.password}
        systemdb:
          url: ${get.datasource.prod.systemdb.url}
          username: ${get.datasource.prod.systemdb.username}
          password: ${get.datasource.prod.systemdb.password}
        appfiledb:
          url: ${get.datasource.prod.appfiledb.url}
          username: ${get.datasource.prod.appfiledb.username}
          password: ${get.datasource.prod.appfiledb.password}
        pmp2db:
          url: ${get.datasource.prod.pmp2db.url}
          username: ${get.datasource.prod.pmp2db.username}
          password: ${get.datasource.prod.pmp2db.password}
        platformdb:
          url: ${get.datasource.prod.platformdb.url}
          username: ${get.datasource.prod.platformdb.username}
          password: ${get.datasource.prod.platformdb.password}

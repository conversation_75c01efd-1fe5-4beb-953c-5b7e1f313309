<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.reportcenter.dao.report.AdvSearchRunMapper">
  <resultMap id="BaseResultMap" type="com.get.reportcenter.entity.AdvSearchRun">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="query_key" jdbcType="VARCHAR" property="queryKey" />
    <result column="query_run_guid" jdbcType="VARCHAR" property="queryRunGuid" />
    <result column="param_condition" jdbcType="VARCHAR" property="paramCondition" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_type" jdbcType="VARCHAR" property="paramType" />
    <result column="param_operation" jdbcType="VARCHAR" property="paramOperation" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="param_sql_text" jdbcType="VARCHAR" property="paramSqlText" />
    <result column="param_sql_value" jdbcType="VARCHAR" property="paramSqlValue" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.reportcenter.entity.AdvSearchRun">
    <result column="param_sql" jdbcType="LONGVARCHAR" property="paramSql" />
  </resultMap>
  <sql id="Blob_Column_List">
    param_sql
  </sql>
  <insert id="insert" parameterType="com.get.reportcenter.entity.AdvSearchRun">
    insert into m_adv_search_run (id, query_key, query_run_guid, 
      param_condition, param_name, param_type, 
      param_operation, param_value, param_sql_text, 
      param_sql_value, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user, param_sql
      )
    values (#{id,jdbcType=BIGINT}, #{queryKey,jdbcType=VARCHAR}, #{queryRunGuid,jdbcType=VARCHAR}, 
      #{paramCondition,jdbcType=VARCHAR}, #{paramName,jdbcType=VARCHAR}, #{paramType,jdbcType=VARCHAR}, 
      #{paramOperation,jdbcType=VARCHAR}, #{paramValue,jdbcType=VARCHAR}, #{paramSqlText,jdbcType=VARCHAR}, 
      #{paramSqlValue,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}, #{paramSql,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.reportcenter.entity.AdvSearchRun">
    insert into m_adv_search_run
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="queryKey != null">
        query_key,
      </if>
      <if test="queryRunGuid != null">
        query_run_guid,
      </if>
      <if test="paramCondition != null">
        param_condition,
      </if>
      <if test="paramName != null">
        param_name,
      </if>
      <if test="paramType != null">
        param_type,
      </if>
      <if test="paramOperation != null">
        param_operation,
      </if>
      <if test="paramValue != null">
        param_value,
      </if>
      <if test="paramSqlText != null">
        param_sql_text,
      </if>
      <if test="paramSqlValue != null">
        param_sql_value,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="paramSql != null">
        param_sql,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="queryKey != null">
        #{queryKey,jdbcType=VARCHAR},
      </if>
      <if test="queryRunGuid != null">
        #{queryRunGuid,jdbcType=VARCHAR},
      </if>
      <if test="paramCondition != null">
        #{paramCondition,jdbcType=VARCHAR},
      </if>
      <if test="paramName != null">
        #{paramName,jdbcType=VARCHAR},
      </if>
      <if test="paramType != null">
        #{paramType,jdbcType=VARCHAR},
      </if>
      <if test="paramOperation != null">
        #{paramOperation,jdbcType=VARCHAR},
      </if>
      <if test="paramValue != null">
        #{paramValue,jdbcType=VARCHAR},
      </if>
      <if test="paramSqlText != null">
        #{paramSqlText,jdbcType=VARCHAR},
      </if>
      <if test="paramSqlValue != null">
        #{paramSqlValue,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="paramSql != null">
        #{paramSql,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.von.ware.dao.PurchaseDetailDao">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.von.ware.entity.PurchaseDetailEntity" id="purchaseDetailMap">
        <result property="id" column="id"/>
        <result property="purchaseId" column="purchase_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="skuNum" column="sku_num"/>
        <result property="skuPrice" column="sku_price"/>
        <result property="wareId" column="ware_id"/>
        <result property="status" column="status"/>
    </resultMap>


</mapper>
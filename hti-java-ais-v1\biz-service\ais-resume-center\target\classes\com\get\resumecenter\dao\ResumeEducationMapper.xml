<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.resumecenter.dao.ResumeEducationMapper">
  <resultMap id="BaseResultMap" type="com.get.resumecenter.entity.ResumeEducation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_resume_id" jdbcType="BIGINT" property="fkResumeId" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="institution" jdbcType="VARCHAR" property="institution" />
    <result column="graduation_level" jdbcType="VARCHAR" property="graduationLevel" />
    <result column="major" jdbcType="VARCHAR" property="major" />
    <result column="major_description" jdbcType="VARCHAR" property="majorDescription" />
    <result column="is_full_time" jdbcType="BIT" property="isFullTime" />
    <result column="is_abroad_experience" jdbcType="BIT" property="isAbroadExperience" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_resume_id, start_date, end_date, institution, graduation_level, major, major_description, 
    is_full_time, is_abroad_experience, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>
  <insert id="insertSelective" parameterType="com.get.resumecenter.entity.ResumeEducation" keyProperty="id" useGeneratedKeys="true">
    insert into m_resume_education
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkResumeId != null">
        fk_resume_id,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="institution != null">
        institution,
      </if>
      <if test="graduationLevel != null">
        graduation_level,
      </if>
      <if test="major != null">
        major,
      </if>
      <if test="majorDescription != null">
        major_description,
      </if>
      <if test="isFullTime != null">
        is_full_time,
      </if>
      <if test="isAbroadExperience != null">
        is_abroad_experience,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkResumeId != null">
        #{fkResumeId,jdbcType=BIGINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="institution != null">
        #{institution,jdbcType=VARCHAR},
      </if>
      <if test="graduationLevel != null">
        #{graduationLevel,jdbcType=VARCHAR},
      </if>
      <if test="major != null">
        #{major,jdbcType=VARCHAR},
      </if>
      <if test="majorDescription != null">
        #{majorDescription,jdbcType=VARCHAR},
      </if>
      <if test="isFullTime != null">
        #{isFullTime,jdbcType=BIT},
      </if>
      <if test="isAbroadExperience != null">
        #{isAbroadExperience,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>
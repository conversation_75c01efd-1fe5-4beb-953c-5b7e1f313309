package com.von.common.valid;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自定义校验器
 *
 * <AUTHOR>
 * @Date 2024/10/16 18:12
 * @Version 1.0
 */
public class ListValueConstraintValidator implements ConstraintValidator<ListValue, Integer> {

    private List<Integer> list;

    @Override
    public void initialize(ListValue constraintAnnotation) {
        // TODO: 没做非空校验
        int[] vals = constraintAnnotation.value();
        list = Arrays.stream(vals).boxed().collect(Collectors.toList());
    }

    @Override
    public boolean isValid(Integer integer, ConstraintValidatorContext constraintValidatorContext) {
        return list.contains(integer);
    }

}

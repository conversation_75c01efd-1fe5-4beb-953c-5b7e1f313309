package com.get.remindercenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.dao.RemindTemplateMapper;
import com.get.remindercenter.vo.RemindTemplateVo;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.service.RemindTemplateService;
import com.get.remindercenter.dto.RemindTemplateListDto;
import com.get.remindercenter.dto.RemindTemplateUpdateDto;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 12:17
 * Date: 2021/11/12
 * Description:提醒模板管理业务实现类
 */
@Service
public class RemindTemplateServiceImpl implements RemindTemplateService {

    @Resource
    private UtilService utilService;
    @Resource
    private RemindTemplateMapper remindTemplateMapper;

    /**
     * @Description: 列表数据
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public List<RemindTemplateVo> datas(RemindTemplateListDto remindTemplateListDto, Page page) {
//        Example example = new Example(RemindTemplate.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<RemindTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(remindTemplateListDto.getFkRemindEventTypeKey())) {
//            criteria.andLike("fkRemindEventTypeKey","%"+remindTemplateListDto.getFkRemindEventTypeKey()+"%");
            lambdaQueryWrapper.like(RemindTemplate::getFkRemindEventTypeKey, remindTemplateListDto.getFkRemindEventTypeKey());
        }
        if (GeneralTool.isNotEmpty(remindTemplateListDto.getSmsTemplate())) {
//            criteria.andLike("smsTemplate","%"+remindTemplateListDto.getSmsTemplate()+"%");
            lambdaQueryWrapper.like(RemindTemplate::getSmsTemplate, remindTemplateListDto.getSmsTemplate());

        }
        if (GeneralTool.isNotEmpty(remindTemplateListDto.getRemark())) {
//            criteria.andLike("remark","%"+remindTemplateListDto.getRemark()+"%");
            lambdaQueryWrapper.like(RemindTemplate::getRemark, remindTemplateListDto.getRemark());

        }
        if (GeneralTool.isNotEmpty(remindTemplateListDto.getEmailTemplate())) {
//            criteria.andLike("emailTemplate","%"+remindTemplateListDto.getEmailTemplate()+"%");
            lambdaQueryWrapper.like(RemindTemplate::getEmailTemplate, remindTemplateListDto.getEmailTemplate());
        }
        lambdaQueryWrapper.orderByDesc(RemindTemplate::getGmtCreate);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<RemindTemplate> pages = remindTemplateMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<RemindTemplate> remindTemplates = pages.getRecords();
        page.setAll((int) pages.getTotal());
        if (GeneralTool.isEmpty(remindTemplates)) {
            return new ArrayList<>();
        }
//        page.restPage(remindTemplates);
        return BeanCopyUtils.copyListProperties(remindTemplates, RemindTemplateVo::new);
    }


    /**
     * @Description: 新增
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public void add(RemindTemplateUpdateDto remindTemplateUpdateDto) {
        if (GeneralTool.isEmpty(remindTemplateUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        RemindTemplate remindTemplate = BeanCopyUtils.objClone(remindTemplateUpdateDto, RemindTemplate::new);
        utilService.updateUserInfoToEntity(remindTemplate);
        remindTemplateMapper.insert(remindTemplate);
    }


    /**
     * @Description: 更新
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public void update(RemindTemplateUpdateDto remindTemplateUpdateDto) {
        if (GeneralTool.isEmpty(remindTemplateUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        RemindTemplate remindTemplate = remindTemplateMapper.selectById(remindTemplateUpdateDto.getId());
        if (GeneralTool.isEmpty(remindTemplate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        BeanUtils.copyProperties(remindTemplateUpdateDto, remindTemplate);
        utilService.updateUserInfoToEntity(remindTemplate);
        remindTemplateMapper.updateByPrimaryKey(remindTemplate);
    }


    /**
     * @Description: 详情
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public RemindTemplateVo detail(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        RemindTemplate remindTemplate = remindTemplateMapper.selectById(id);
        if (GeneralTool.isEmpty(remindTemplate)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(remindTemplate, RemindTemplateVo::new);
    }


    /**
     * @Description: 删除
     * @Author: Jerry
     * @Date:11:41 2021/11/12
     */
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        remindTemplateMapper.deleteById(id);
    }

    /**
     * 获取邮件模板
     * @param typeKey
     * @return
     */
    @Override
    public RemindTemplate getRemindTemplateByTypeKey(String typeKey) {
        return remindTemplateMapper.selectOne(Wrappers.<RemindTemplate>lambdaQuery().eq(RemindTemplate::getFkRemindEventTypeKey, typeKey));
    }
}

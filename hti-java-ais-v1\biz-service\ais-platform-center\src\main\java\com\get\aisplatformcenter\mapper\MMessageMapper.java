package com.get.aisplatformcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aisplatformcenterap.vo.MMessageVo;
import com.get.aisplatformcenterap.entity.MMessageEntity;
import com.get.aisplatformcenterap.dto.MMessageParamsDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_message(消息记录)】的数据库操作Mapper
* @createDate 2024-12-16 18:03:59
* @Entity com.get.partnercenter.entity.MMessageEntity
*/
@Mapper
public interface MMessageMapper extends BaseMapper<MMessageEntity> {
    List<MMessageVo> searchPage(IPage<MMessageVo> page, @Param("query") MMessageParamsDto mMessageDto);

    int updateByIds(@Param("type")int type,@Param("ids")Long[] ids);
}





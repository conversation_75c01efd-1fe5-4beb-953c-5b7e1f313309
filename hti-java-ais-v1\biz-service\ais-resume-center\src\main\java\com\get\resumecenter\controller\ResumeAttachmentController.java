package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.ResumeAttachmentVo;
import com.get.resumecenter.service.IResumeAttachmentService;
import com.get.resumecenter.dto.ResumeAttachmentDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2020/12/30
 * @TIME: 10:01
 * @Description:
 **/

@Api(tags = "简历附件管理")
@RestController
@RequestMapping("resume/resumeAttachment")
public class ResumeAttachmentController {

    @Resource
    private IResumeAttachmentService resumeAttachmentService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.resumecenter.vo.ResumeAttachmentVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/简历附件管理/参数详情")
    @GetMapping("/{id}")
    public ResponseBo<ResumeAttachmentVo> detail(@PathVariable("id") Long id) {
        ResumeAttachmentVo resumeAttachmentVo = resumeAttachmentService.findResumeAttachmentById(id);
        return new ResponseBo<>(resumeAttachmentVo);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增信息
     * @Param [resumeAttachmentDto]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/简历附件管理/新增教育经验")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(ResumeAttachmentDto.Add.class) ResumeAttachmentDto resumeAttachmentDto) {
        return SaveResponseBo.ok(resumeAttachmentService.add(resumeAttachmentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 修改信息
     * @Param [resumeAttachmentDto]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/简历附件管理/修改")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ResumeAttachmentDto.Update.class) ResumeAttachmentDto resumeAttachmentDto) {
        return UpdateResponseBo.ok(resumeAttachmentService.update(resumeAttachmentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "人才中心/简历附件管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        resumeAttachmentService.delete(id);
        return ResponseBo.ok();
    }

}

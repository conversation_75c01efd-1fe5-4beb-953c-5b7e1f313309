<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.ULabelMapper">

    <select id="getMaxOrder" resultType="java.lang.Integer">
        select
            IFNULL(max(view_order)+1,0) view_order
        from
            u_label
    </select>
    <select id="getAgentLabel" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM ais_sale_center.r_agent_label
          <where>
              <if test="labelId != null">
                  and fk_label_id = #{labelId}
              </if>
          </where>
    </select>

    <select id="getLabelByLabelTypeIdAndKeyWord" resultType="com.get.aisplatformcenterap.vo.LabelAboutAgentVo">
        SELECT ul.id,
               ul.label_name        AS labelName,
               ul.label_key         AS labelKey,
               ul.icon_name         AS iconName,
               ul.color             AS color,
               ul.remark            AS labelRemark,
               ul.view_order        AS viewOrder,
               ul.gmt_create        AS gmtCreate,
               ul.gmt_modified      AS gmtModified,
               ul.gmt_create_user   AS gmtCreateUser,
               ul.gmt_modified_user AS gmtModifiedUser,
               ult.id               AS labelTypeId,
               ult.type_name        AS labelType
        FROM u_label AS ul
                 INNER JOIN u_label_type AS ult ON ult.id = ul.fk_label_type_id
        WHERE 1 = 1
          AND ul.is_active = 1
<!--        <if test="agentLabelVo.fkAgentId != null and agentLabelVo.fkAgentId != ''">-->
<!--            AND NOT EXISTS ( SELECT 1-->
<!--            FROM ais_sale_center.r_agent_label AS ral-->
<!--            WHERE ral.fk_label_id = ul.id-->
<!--            AND ral.fk_agent_id = #{agentLabelVo.fkAgentId}-->

<!--            <if test="agentLabelVo.labelEmail != null">-->
<!--                AND ral.email = #{agentLabelVo.labelEmail}-->
<!--            </if>-->
<!--            )-->
<!--        </if>-->

<!--        <if test="agentLabelVo.fkAgentIds != null and agentLabelVo.fkAgentIds.size() > 0">-->
<!--            AND NOT EXISTS ( SELECT 1-->
<!--            FROM ais_sale_center.r_agent_label AS ral-->
<!--            WHERE ral.fk_label_id = ul.id-->
<!--            AND ral.fk_agent_id IN-->
<!--            <foreach collection="agentLabelVo.fkAgentIds" item="fkAgentId" open="(" separator="," close=")">-->
<!--                #{fkAgentId}-->
<!--            </foreach>-->
<!--            <if test="agentLabelVo.labelEmail != null">-->
<!--                AND ral.email = #{agentLabelVo.labelEmail}-->
<!--            </if>-->
<!--            )-->
<!--        </if>-->
        <if test="agentLabelVo.labelTypeId != null and agentLabelVo.labelTypeId != ''">
            AND ult.id = #{agentLabelVo.labelTypeId}
        </if>

        <if test="agentLabelVo.labelKeyWord != null and agentLabelVo.labelKeyWord != ''">
            AND ul.label_name LIKE CONCAT('%', #{agentLabelVo.labelKeyWord}, '%')
            OR ul.remark LIKE CONCAT('%', #{agentLabelVo.labelKeyWord}, '%')
        </if>
        ORDER BY ul.id, ul.view_order DESC
    </select>
</mapper>

package com.von.common.enums;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.HashMap;
import java.util.Map;

/**
 * 属性类型
 *
 * <AUTHOR>
 * @Date 2024/10/18 19:38
 * @Version 1.0
 */
@Getter
@ToString
@AllArgsConstructor
public enum AttrTypeEnum {

    SALE("销售属性", 0),
    BASE("规格参数", 1);

    private final String text;
    private final int value;
    private static final Map<Integer, AttrTypeEnum> ATTR_TYPE_ENUM_MAP = new HashMap<>();

    static {
        for (AttrTypeEnum attrTypeEnum : AttrTypeEnum.values()) {
            ATTR_TYPE_ENUM_MAP.put(attrTypeEnum.value, attrTypeEnum);
        }
    }

    public static AttrTypeEnum valueOf(int value) {
        return ATTR_TYPE_ENUM_MAP.get(value);
    }

    /**
     * 判断是否为基本属性
     *
     * @param value
     * @return
     */
    public static Boolean isBaseType(Integer value) {
        if (ObjectUtil.isNull(value)) {
            return false;
        }

        switch (ATTR_TYPE_ENUM_MAP.get(value)) {
            case SALE:
                return false;
            case BASE:
                return true;
            default:
                return false;
        }
    }

}

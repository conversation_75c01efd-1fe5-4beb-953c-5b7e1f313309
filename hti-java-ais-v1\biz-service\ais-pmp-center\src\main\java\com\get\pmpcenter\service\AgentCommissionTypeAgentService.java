package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeAgentDto;
import com.get.pmpcenter.entity.AgentCommissionTypeAgent;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface AgentCommissionTypeAgentService extends IService<AgentCommissionTypeAgent> {
    void add(AgentCommissionTypeAgentDto agentCommissionTypeAgentDto);

    int delete(Long id);

    ResponseBo dataList(SearchBean<AgentCommissionTypeAgentDto> page);
}

package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.resumecenter.dao.MediaAndAttachedMapper;
import com.get.resumecenter.vo.MediaAndAttachedVo;
import com.get.resumecenter.entity.MediaAndAttached;
import com.get.resumecenter.service.IMediaAndAttachedService;
import com.get.resumecenter.dto.MediaAndAttachedDto;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 10:57
 * @Description: 媒体附件
 **/
@Service
public class MediaAndAttachedServiceImpl implements IMediaAndAttachedService {
    @Resource
    private MediaAndAttachedMapper attachedMapper;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Autowired
    private UtilService utilService;

    @Override
    public List<MediaAndAttachedVo> getFile(MediaAndAttachedDto attachedVo) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<MediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isEmpty(attachedVo.getFkTableName())) {
//            criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
            lambdaQueryWrapper.eq(MediaAndAttached::getFkTableName, attachedVo.getFkTableName());
        }
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
//            criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
            lambdaQueryWrapper.eq(MediaAndAttached::getFkTableId, attachedVo.getFkTableId());

        }
        if (GeneralTool.isEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
            lambdaQueryWrapper.eq(MediaAndAttached::getTypeKey, attachedVo.getTypeKey());

        }
//        List<MediaAndAttached> mediaAndAttacheds = attachedMapper.selectByExample(example);
        List<MediaAndAttached> mediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
        return mediaAndAttacheds.stream().map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
    }


    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles) {
        List<FileDto> fileDtos = null;
        //调用上传接口
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.RESUMECENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("FILE_UPLOAD_ERROR_PLEASE_RETRY"));
        }
        JSONArray jsonArray = JSONArray.fromObject(result.getData());
        fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
//            ListResponseBo responseBo = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.RESUMECENTER);
//            if (!ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
//                throw new GetServiceException("文件上传错误,请重试");
//            }
        return fileDtos;
    }

    @Override
    public List<FileDto> uploadAttached(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
//            ListResponseBo responseBo = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.RESUMECENTER);
//            if (!ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
//                throw new GetServiceException("上传失败,请重试");
//            }
//            JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());
//            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());


        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.RESUMECENTER);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    @Override
    public MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAndAttachedDto) {
        if (GeneralTool.isEmpty(mediaAndAttachedDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        if (GeneralTool.isEmpty(mediaAndAttachedDto.getFkTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
        }
        MediaAndAttached mediaAndAttached = BeanCopyUtils.objClone(mediaAndAttachedDto, MediaAndAttached::new);
        //查询索引值
        Integer nextIndexKey = attachedMapper.getNextIndexKey(mediaAndAttached.getFkTableId(), mediaAndAttachedDto.getFkTableName());
        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
        //设置属性
        mediaAndAttached.setIndexKey(nextIndexKey);
        //当无主键时未插入
        if (GeneralTool.isEmpty(mediaAndAttached.getId())) {
            utilService.updateUserInfoToEntity(mediaAndAttached);
            attachedMapper.insertSelective(mediaAndAttached);
        } else {
            //修改操作
            utilService.updateUserInfoToEntity(mediaAndAttached);
            attachedMapper.updateById(mediaAndAttached);
        }
        MediaAndAttachedVo mediaAndAttachedVo = BeanCopyUtils.objClone(mediaAndAttachedDto, MediaAndAttachedVo::new);
        //设置回显的值
        mediaAndAttachedVo.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
        mediaAndAttachedVo.setId(mediaAndAttached.getId());
        mediaAndAttachedVo.setFileKey(mediaAndAttachedDto.getFileKey());
        return mediaAndAttachedVo;
    }

    @Override
    public void updateTableId(Long mediaId, Long fkTableId) {
        MediaAndAttached mediaAndAttached = new MediaAndAttached();
        mediaAndAttached.setFkTableId(fkTableId);
        mediaAndAttached.setId(mediaId);
        attachedMapper.updateById(mediaAndAttached);
    }

    @Override
    public void deleteById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MediaAndAttached mediaAndAttached = attachedMapper.selectById(id);
        if (GeneralTool.isEmpty(mediaAndAttached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
        }
        Result<Boolean> result = fileCenterClient.delete(mediaAndAttached.getFkFileGuid(), LoggerModulesConsts.RESUMECENTER);
        if (result.isSuccess()) {
            int i = attachedMapper.deleteById(id);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo) {
        if (GeneralTool.isEmpty(attachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<MediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
//            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
            lambdaQueryWrapper.eq(MediaAndAttached::getTypeKey, attachedVo.getTypeKey());
        }
        lambdaQueryWrapper.eq(MediaAndAttached::getFkTableName, attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
        lambdaQueryWrapper.eq(MediaAndAttached::getFkTableId, attachedVo.getFkTableId());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
        List<MediaAndAttached> mediaAndAttacheds = attachedMapper.selectList(lambdaQueryWrapper);
        List<MediaAndAttachedVo> fileMedia = getFileMedia(mediaAndAttacheds);
        return fileMedia;
    }

    @Override
    public void delete(MediaAndAttachedDto attachedVo) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
//        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
//        criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
        attachedMapper.delete(Wrappers.<MediaAndAttached>lambdaQuery()
                .eq(MediaAndAttached::getFkTableName, attachedVo.getFkTableName())
                .eq(MediaAndAttached::getFkTableId, attachedVo.getFkTableId())
                .eq(MediaAndAttached::getTypeKey, attachedVo.getTypeKey()));
    }

    @Override
    public void update(MediaAndAttachedDto mediaAndAttachedDto) {
        if (GeneralTool.isEmpty(mediaAndAttachedDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        MediaAndAttached mediaAndAttached = BeanCopyUtils.objClone(mediaAndAttachedDto, MediaAndAttached::new);
        utilService.updateUserInfoToEntity(mediaAndAttached);
        attachedMapper.updateById(mediaAndAttached);
    }


    private List<MediaAndAttachedVo> getFileMedia(List<MediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(MediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
//        ListResponseBo responseBo = fileCenterClient.findFileByGuid(guidList, LoggerModulesConsts.RESUMECENTER);
//        JSONArray jsonArray = JSONArray.fromObject(responseBo.getDatas());
//        List<FileDto> fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        List<FileDto> fileDtos = new ArrayList<>();
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.RESUMECENTER, guidList);
        Result<List<FileDto>> fileDtoResult = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        if (fileDtoResult.isSuccess() && GeneralTool.isNotEmpty(fileDtoResult.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(fileDtoResult.getData());
            fileDtos.addAll(JSONArray.toList(jsonArray, new FileDto(), new JsonConfig()));
        }
        //返回结果不为空时
        List<MediaAndAttachedVo> collect = null;
        if (GeneralTool.isNotEmpty(fileDtos)) {
            //遍历查询GUID是否一致
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> fileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        mediaAndAttachedDto.setFkTableName(null);
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        return collect;
    }


}

package com.get.remindercenter.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.AESConstant;
import com.get.common.consts.ReminderCenterConstant;
import com.get.common.consts.SaleCenterConstant;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.AESUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dao.RemindTemplateMapper;
import com.get.remindercenter.dto.*;
import com.get.remindercenter.entity.*;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOfferItem;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.StudentOfferItemSendEmailVo;
import com.get.salecenter.vo.StudentOfferItemVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 接受Offer截止提醒 邮件组件类
 */
@Slf4j
@Component("acceptOfferDeadlineReminderEmailHelper")
public class AcceptOfferDeadlineReminderEmailHelper extends EmailAbstractHelper{

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private ISaleCenterClient saleCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Resource
    private IInstitutionCenterClient institutionCenterClient;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private EmailTemplateMapper emailTemplateMapper;



    @Value("${emailSwitch}")
    private boolean emailSwitch;

    /**
     * 根据模板key发送邮件
     * @param emailSenderQueue
     */
    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        log.info("发多邮件测试：开始<|>emailSenderQueue:{}", JSONObject.toJSONString(emailSenderQueue));
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            //组装数据
            AcceptOfferDeadlineReminderDto acceptOfferDeadlineReminderDto = assembleEmailData(emailSenderQueue);

            //人员信息
            StaffVo staffVo = null;
            String email = null;
            StringJoiner emailsCombined = new StringJoiner(", ");

            if (GeneralTool.isNotEmpty(emailSenderQueue.getFkTableId())) {
                //发送代理
                if (acceptOfferDeadlineReminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.getEmailTemplateKey())
                        || acceptOfferDeadlineReminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.getEmailTemplateKey())
                ) {
                    //发送代理联系人
                    log.info("发送代理联系人");
                    //发送人
                    StudentOfferItemSendEmailVo contactPersonEmailStaff = saleCenterClient.getContactPersonEmailStaff(emailSenderQueue.getFkTableId());
                    //发送代理联系人
                    sendEmailToContactPerson(contactPersonEmailStaff, acceptOfferDeadlineReminderDto,emailSenderQueue.getId());
                    //接收人邮箱
                    emailSenderQueue.setEmailTo(contactPersonEmailStaff.getContactPersonEmailDtos().toString());
                    //发送人邮箱
                    emailSenderQueue.setEmailFrom(contactPersonEmailStaff.getStudentOfferItemStaffEmailDto().getUserEmail());
                    LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
                    updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                            .set(EmailSenderQueue::getEmailTo,emailSenderQueue.getEmailTo());  // 只更新 emailTo 字段
                    emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
                    return;
                }
            }

            log.info("发多邮件测试：for循环<|>StaffEmailSet:{}", JSONObject.toJSONString(acceptOfferDeadlineReminderDto.getStaffEmailSet()));
                for (Long staffId : acceptOfferDeadlineReminderDto.getStaffEmailSet()) {

                        Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(staffId);
//            Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(1584L);
                        if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
                            staffVo = staffDtoResult.getData();
                        }
                        //设置邮件模板
                        String template = setEmailTemplate(acceptOfferDeadlineReminderDto);
                        EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                        emailSystemMQMessageDto.setEmailSenderQueueId(acceptOfferDeadlineReminderDto.getId());
                        emailSystemMQMessageDto.setTitle(acceptOfferDeadlineReminderDto.getEmailTitle());
                        emailSystemMQMessageDto.setContent(template);
                        emailSystemMQMessageDto.setToEmail(staffVo.getEmail());

                    try {
                        //发送项目成员
                        if (acceptOfferDeadlineReminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND.getEmailTemplateKey()) || acceptOfferDeadlineReminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.PAY_DEPOSIT_DUE_REMIND.getEmailTemplateKey())) {
                            //使用自己邮箱发送给自己
                            log.info("发多邮件测试：使用自己邮箱发送给自己<|>emailSystemMQMessageDto:{}", JSONObject.toJSONString(emailSystemMQMessageDto));
                            sendOfferItemRemind(staffVo, acceptOfferDeadlineReminderDto, emailSystemMQMessageDto);
                            if (GeneralTool.isNotEmpty(staffVo.getEmail())) {
                                emailsCombined.add(staffVo.getEmail());
                            }

                        } else {
                            log.info("发多邮件测试：sendSystemMail<|>emailSystemMQMessageDto:{}", JSONObject.toJSONString(emailSystemMQMessageDto));
                            //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                            remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                            if (GeneralTool.isNotEmpty(staffVo.getEmail())) {
                                emailsCombined.add(staffVo.getEmail());
                            }
                        }
                    }catch (Exception e){
                        // 记录发送失败的邮箱
                        String failedEmail = staffVo != null && staffVo.getEmail() != null ? staffVo.getEmail() : "staffId:" + staffId;
                        failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                        log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}", staffId, failedEmail, e.getMessage());
                        log.info("发多邮件测试：邮件发送失败<|>emailSystemMQMessageDto:{}", JSONObject.toJSONString(emailSystemMQMessageDto));

                    }
                    }
                emailSenderQueue.setEmailTo(emailsCombined.toString());
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        }catch (Exception e){
            log.error("AcceptOfferDeadlineReminderEmailHelper error:{}", e);
            failedEmails.add("邮件发送异常"+e.getMessage());
            emailSenderQueue.setErrorMessage(failedEmails.toString());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
            log.info("发多邮件测试：大异常失败<|>error:{}", e);
        }

    }

    /**
     * 根据队列id组装邮件数据
     */
    public AcceptOfferDeadlineReminderDto assembleEmailData(EmailSenderQueue emailSenderQueue){
        AcceptOfferDeadlineReminderDto reminderDto = new AcceptOfferDeadlineReminderDto();
        BeanUtils.copyProperties(emailSenderQueue,reminderDto);
        //获取申请计划
        StudentOfferItem studentOfferItem =saleCenterClient.getStudentOfferItemById(emailSenderQueue.getFkTableId()).getData();
        reminderDto.setFkStaffId(studentOfferItem.getFkStaffId());
        reminderDto.setDepositDeadline(studentOfferItem.getDepositDeadline());
        reminderDto.setAcceptOfferDeadline(studentOfferItem.getAcceptOfferDeadline());
        Map<String, String> map = new HashMap<>();
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        //获取学生信息
        Student student = saleCenterClient.getStudentById(studentOfferItem.getFkStudentId()).getData();
        //获取代理信息
        Agent agent = saleCenterClient.getAgentById(studentOfferItem.getFkAgentId()).getData();
        //获取适用国家
        Result<AreaCountry> country = institutionCenterClient.getCountryById(studentOfferItem.getFkAreaCountryId());
        //获取学校信息
        Result<Institution> institution = institutionCenterClient.getInstitutionById(studentOfferItem.getFkInstitutionId());
        //获取课程信息
        Result<InstitutionCourseVo> course = institutionCenterClient.getCourseById(studentOfferItem.getFkInstitutionCourseId());
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(student.getFkCompanyId());
        //学生名
        String studentName = new String();
        //申请国家
        String fkAreaCountryName = new String();
        //申请学校
        String fkInstitutionName = new String();
        //申请课程
        String fkCourseName = new String();
        //课程长度
        String duration = new String();
        //申请方式
        String appMethodName = new String();
        //长度类型
        String durationTypeName = new String();

        if (versionValue2.equals("en")) {
            studentName = student.getName();
            if (country.isSuccess() && GeneralTool.isNotEmpty(country.getData())) {
                fkAreaCountryName = country.getData().getName();
            }
            if (institution.isSuccess() && GeneralTool.isNotEmpty(institution.getData())) {
                fkInstitutionName = institution.getData().getName();
            }

            if (studentOfferItem.getFkInstitutionCourseId() != -1) {
                if (course.isSuccess() && GeneralTool.isNotEmpty(course.getData())) {
                    fkCourseName = course.getData().getName();
                }
            } else {
                fkCourseName = studentOfferItem.getOldCourseCustomName();
            }
            if (GeneralTool.isNotEmpty(studentOfferItem.getAppMethod())) {
                switch (studentOfferItem.getAppMethod()) {
                    case 0:
                        appMethodName = "Online Application";
                        break;
                    case 1:
                        appMethodName = "Scan";
                        break;
                    case 2:
                        appMethodName = "Original mailing";
                        break;
                    case 3:
                        appMethodName = "Other";
                        break;
                    default:
                        break;
                }
            }
            if (GeneralTool.isNotEmpty(studentOfferItem.getDurationType())) {
                //0周、1月、2年、3学期
                switch (studentOfferItem.getDurationType()) {
                    case 0:
                        durationTypeName = "week";
                        break;
                    case 1:
                        durationTypeName = "month";
                        break;
                    case 2:
                        durationTypeName = "year";
                        break;
                    case 3:
                        durationTypeName = "semester";
                        break;
                    default:
                        break;
                }
            }
        } else {
            studentName = student.getName() + "（" + student.getLastName() + "  " + student.getFirstName() + "）";
            if (country.isSuccess() && GeneralTool.isNotEmpty(country.getData())) {
                if (GeneralTool.isNotEmpty(country.getData().getNameChn())) {
                    fkAreaCountryName = country.getData().getName() + "(" + country.getData().getNameChn() + ")";
                } else {
                    fkAreaCountryName = country.getData().getName();
                }
            }
            if (institution.isSuccess() && GeneralTool.isNotEmpty(institution.getData())) {
                if (GeneralTool.isNotEmpty(institution.getData().getNameChn())) {
                    fkInstitutionName = institution.getData().getName() + "(" + institution.getData().getNameChn() + ")";
                } else {
                    fkInstitutionName = institution.getData().getName();
                }
            }
            if (studentOfferItem.getFkInstitutionCourseId() != -1) {
                if (course.isSuccess() && GeneralTool.isNotEmpty(course.getData())) {
                    fkCourseName = course.getData().getName() + "(" + course.getData().getNameChn() + ")";
                }
            } else {
                fkCourseName = studentOfferItem.getOldCourseCustomName();
            }
            if (GeneralTool.isNotEmpty(studentOfferItem.getAppMethod())) {
                switch (studentOfferItem.getAppMethod()) {
                    case 0:
                        appMethodName = "网申";
                        break;
                    case 1:
                        appMethodName = "扫描";
                        break;
                    case 2:
                        appMethodName = "原件邮递";
                        break;
                    case 3:
                        appMethodName = "其他";
                        break;
                    default:
                        break;
                }
            }
            if (GeneralTool.isNotEmpty(studentOfferItem.getDurationType())) {
                //0周、1月、2年、3学期
                switch (studentOfferItem.getDurationType()) {
                    case 0:
                        durationTypeName = "周";
                        break;
                    case 1:
                        durationTypeName = "月";
                        break;
                    case 2:
                        durationTypeName = "年";
                        break;
                    case 3:
                        durationTypeName = "学期";
                        break;
                    default:
                        break;
                }
            }
        }

        map.put("studentName", studentName);
        if (GeneralTool.isNotEmpty(agent)) {
            map.put("agentName", agent.getName());
        }
        if (GeneralTool.isEmpty(student.getBirthday())) {
            map.put("birthday", "");
        } else {
            map.put("birthday", sf.format(student.getBirthday()));
        }
        map.put("fkAreaCountryName", fkAreaCountryName);
        map.put("fkInstitutionName", fkInstitutionName);
        map.put("fkCourseName", fkCourseName);
        if(GeneralTool.isNotEmpty(studentOfferItem.getCourseOpenTime())){
            map.put("courseOpenTime",studentOfferItem.getCourseOpenTime().toString());
        }

        //0网申/1扫描/2原件邮递/3其他
        map.put("appMethodName", appMethodName);
        if (GeneralTool.isEmpty(studentOfferItem.getDuration())) {
            map.put("duration", "");
        } else {
            map.put("duration", studentOfferItem.getDuration().toString());
        }
        map.put("durationTypeName", durationTypeName);

        if (GeneralTool.isEmpty(studentOfferItem.getDeferOpeningTime())) {
            map.put("openingTime", "");
        } else {
            map.put("openingTime", sf.format(studentOfferItem.getDeferOpeningTime()));
        }

        if (GeneralTool.isNotEmpty(studentOfferItem.getCourseOpenTime())) {
            map.put("courseOpenTime", sf.format(studentOfferItem.getCourseOpenTime()));
        }
            map.put("dearName", studentName);
        String domainName = "";

        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
        String configValue2 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        if (GeneralTool.isNotEmpty(configValue2)) {
            domainName = configValue2;
        } else {
            Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
            if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                domainName = domainNameResult.getData();
            }
        }

        String detial = "";
        Result<String> detialResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.REMINDER_TASK_LINK_OFI_VIEW.key);
        if (detialResult.isSuccess() && GeneralTool.isNotEmpty(detialResult.getData())) {
            detial = detialResult.getData();
        }
        if (GeneralTool.isEmpty(domainName) || GeneralTool.isEmpty(detial)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        Set<Long> staffEmailSet = new HashSet<>();


        List<String> roles = new ArrayList<>();

        companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_AGENT_OFFER_ITEM_DEADLINE.key, 2).getData();
        configValue2 = companyConfigMap.get(student.getFkCompanyId());
        roles = JSONObject.parseArray(configValue2, String.class);

        //发送对象
        //项目成员
        if (GeneralTool.isNotEmpty(roles)) {
            List<String> roleList = roles.stream().filter(e -> e.contains("ROLE")).map(role -> role.substring("ROLE:".length())).collect(Collectors.toList());
            Set<Long> rolesIds = saleCenterClient.getRoleAndStaffByTableIdAndRoles(studentOfferItem.getFkStudentOfferId(), TableEnum.SALE_STUDENT_OFFER.key, roleList).getData();
            staffEmailSet.addAll(rolesIds);
        }
        if (roles.contains("OFFER_CREATE")) {
            Result<StaffVo> staffDtoResult = permissionCenterClient.getStaffByCreateUser(studentOfferItem.getGmtCreateUser());
            if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
                staffEmailSet.add(staffDtoResult.getData().getId());
            }
        }
        if (roles.contains("OFFER_ITEM_BD")) {
            staffEmailSet.add(studentOfferItem.getFkStaffId());
        }
        reminderDto.setStaffEmailSet(staffEmailSet);

        reminderDto.setMap(map);

        String template = "";
        if (versionValue2.equals("en")) {
            if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND.getEmailTemplateKey())) {
                reminderDto.setEmailTitle("[" + "Offer Acceptance Reminder" + "] Student：" + "（" + student.getName() + "）"
                        + "[" + fkAreaCountryName + "]" + fkInstitutionName + fkCourseName);
             // template=  ReminderTemplateUtils.getReminderTemplate(map, ReminderCenterConstant.OFFER_REMINDER_ENG );
            }else if(reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.PAY_DEPOSIT_DUE_REMIND.getEmailTemplateKey())){
                reminderDto.setEmailTitle("[" + "Deposit Deadline Reminder" + "] Student：" + "（" + student.getName() + "）"
                        + "[" + fkAreaCountryName + "]" + fkInstitutionName + fkCourseName);
                //template=  ReminderTemplateUtils.getReminderTemplate(map, ReminderCenterConstant.PAYDEPOSIT_REMINDER_ENG);
            } else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.getEmailTemplateKey())) { //代理学生接受Offer截止提醒
                reminderDto.setEmailTitle("[" + "Offer Acceptance Reminder" + "] Student：" + "（" + student.getName() + "）"
                        + "[" + fkAreaCountryName + "]" + fkInstitutionName + fkCourseName);
//                template = ReminderTemplateUtils.getReminderTemplate(map, ReminderCenterConstant.OFFER_REMINDER);
//                String toRemove = "<div>Agent Name：.*?</div>";
//                template=template.replaceAll(toRemove, "");
            }else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.getEmailTemplateKey())) {
                reminderDto.setEmailTitle("[" + "Deposit Deadline Reminder" + "] Student：" + "（" + student.getName() + "）"
                        + "[" + fkAreaCountryName + "]" + fkInstitutionName + fkCourseName);
//                template = ReminderTemplateUtils.getReminderTemplate(map, ReminderCenterConstant.PAYDEPOSIT_REMINDER_ENG);
//                String toRemove = "<div>Agent Name：.*?</div>";
//                template=template.replaceAll(toRemove, "");
            }else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.COURSE_OPENING_REMINDER.getEmailTemplateKey())) {
                reminderDto.setEmailTitle("Course Opening Reminder" + ", " + "Student:" + student.getName() + ", " + "Course Applies for：" + fkCourseName + ", " + "Course Schedule Opening Hours：" + DateUtil.format(studentOfferItem.getCourseOpenTime(), "yyyy-MM-dd"));
                //template = ReminderTemplateUtils.getReminderTemplate(map, ReminderCenterConstant.COURSE_OPENING_REMINDER_ENG);
            }
        } else {
            if(reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND.getEmailTemplateKey())){
                reminderDto.setEmailTitle("【" + "学生接受Offer提醒" + "】 学生：" + student.getName() + "（" + student.getFirstName() + student.getLastName() + "）"
                        + "【" + fkAreaCountryName + "】" + fkInstitutionName + fkCourseName);
               // template= ReminderTemplateUtils.getReminderTemplate(map,ReminderCenterConstant.OFFER_REMINDER);
            }else if(reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.PAY_DEPOSIT_DUE_REMIND.getEmailTemplateKey())){
                reminderDto.setEmailTitle("【" + "学生支付押金提醒" + "】 学生：" + student.getName() + "（" + student.getFirstName() + student.getLastName() + "）"
                        + "【" + fkAreaCountryName + "】" + fkInstitutionName + fkCourseName);
                //template=  ReminderTemplateUtils.getReminderTemplate(map, ReminderCenterConstant.PAYDEPOSIT_REMINDER);
            }else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.getEmailTemplateKey())) { //代理学生接受Offer截止提醒
                reminderDto.setEmailTitle("【" + "学生接受Offer提醒" + "】 学生：" + student.getName() + "（" + student.getFirstName() + student.getLastName() + "）"
                        + "【" + fkAreaCountryName + "】" + fkInstitutionName + fkCourseName);
//                template= ReminderTemplateUtils.getReminderTemplate(map,ReminderCenterConstant.OFFER_REMINDER);
//                String toRemove = "<div>代理名称：.*?</div>";
//                template=template.replaceAll(toRemove, "");
            }else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.getEmailTemplateKey())) {
                reminderDto.setEmailTitle("【" + "学生支付押金提醒" + "】 学生：" + student.getName() + "（" + student.getFirstName() + student.getLastName() + "）"
                        + "【" + fkAreaCountryName + "】" + fkInstitutionName + fkCourseName);
//                template=  ReminderTemplateUtils.getReminderTemplate(map, ReminderCenterConstant.PAYDEPOSIT_REMINDER);
//                String toRemove = "<div>代理名称：.*?</div>";
               // template=template.replaceAll(toRemove, "");
            }else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.COURSE_OPENING_REMINDER.getEmailTemplateKey())) {
                reminderDto.setEmailTitle("课程开放提醒" + ", " + "学生: " + student.getName() + ", " + "申请课程：" + fkCourseName + ", " + "课程计划开放时间：" + DateUtil.format(studentOfferItem.getCourseOpenTime(), "yyyy-MM-dd"));
                //template = ReminderTemplateUtils.getReminderTemplate(map, ReminderCenterConstant.COURSE_OPENING_REMINDER);
            }
        }

       // reminderDto.setTemplate(template);
        reminderDto.setLanguageCode(versionValue2);
        if (GeneralTool.isNotEmpty(reminderDto.getEmailTitle())) {
            emailSenderQueue.setEmailTitle(reminderDto.getEmailTitle());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
        return reminderDto;
    }



    /**
     * 发送代理联系人
     * @param contactPersonEmailStaff
     * @param reminderDto
     */
    private void sendEmailToContactPerson(StudentOfferItemSendEmailVo contactPersonEmailStaff,AcceptOfferDeadlineReminderDto reminderDto,Long queueId) {
        //是否禁止发送邮件 true 发送  false禁止发送
        if (!emailSwitch) {
            return;
        }
        //步骤不符则不发送邮件
        //支付押金截止提醒：提交完成（Submitted），已通知缴申请费（Notified Payment），院校已收件（App Received），已录取（Admitted/Offer），申请延期（App For Extension）,已延期（Postponed）
        //接受Offer截止提醒：提交完成（Submitted），已通知缴申请费（Notified Payment），院校已收件（App Received），已录取（Admitted/Offer），申请延期（App For Extension）,已延期（Postponed）
        if ((reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.getEmailTemplateKey()))
                && (!contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(2L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(11L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(3L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(4L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(13L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(14L)
//                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(5L)
        )) {
            return;
        } else if ((reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.getEmailTemplateKey()))
                && (!contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(2L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(11L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(3L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(4L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(13L)
                && !contactPersonEmailStaff.getFkStudentOfferItemStepId().equals(14L)
        )) {
            return;
        }
        //步骤不符则不发送邮件
        //达到过Offer Accepted则不发
        if (contactPersonEmailStaff.getIsStepOfferAccepted() || contactPersonEmailStaff.getIsStudentArrivedOs()) {
            return;
        }

        //模板设置
        String emailTemplate = setEmailTemplate(reminderDto);
        EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
        //是否拒收
        if (!contactPersonEmailStaff.getIsRejectEmail()) {

            //true 项目成员名义发送   false  系统默认邮箱发送
            Boolean isSenderAdmin = false;

            //校验发送人和接收人邮箱地址
            if (GeneralTool.isNotEmpty(contactPersonEmailStaff.getStudentOfferItemStaffEmailDto())) {
                if (GeneralTool.isEmpty(contactPersonEmailStaff.getStudentOfferItemStaffEmailDto().getUserEmail())) {
                    sendEmailErrorFromAdmin(contactPersonEmailStaff, null);
                    isSenderAdmin = true;
                } else if (GeneralTool.isEmpty(contactPersonEmailStaff.getStudentOfferItemStaffEmailDto().getPassword())) {
                    sendEmailErrorFromAdmin(contactPersonEmailStaff, null);
                    isSenderAdmin = true;
                }
            }

            if (!isSenderAdmin) {
                StudentOfferItemSendEmailVo.StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto = contactPersonEmailStaff.getStudentOfferItemStaffEmailDto();
                String toAgentEmail = "";
                List<String> ccAgentEmailList = new ArrayList<>();

                for (int i = 0; i < contactPersonEmailStaff.getContactPersonEmailDtos().size(); i++) {
                    StudentOfferItemSendEmailVo.ContactPersonEmailDto contactPersonEmailDto = contactPersonEmailStaff.getContactPersonEmailDtos().get(i);
                    if (0 == i) {
                        toAgentEmail = contactPersonEmailDto.getAgentEmail();
                    } else {
                        ccAgentEmailList.add(contactPersonEmailDto.getAgentEmail());
                    }
                }


                emailSystemMQMessageDto.setEmailSenderQueueId(reminderDto.getId());
                emailSystemMQMessageDto.setContent(emailTemplate);
                emailSystemMQMessageDto.setToEmail(toAgentEmail);
                emailSystemMQMessageDto.setCcEmail(ccAgentEmailList.toArray(new String[0]));
                emailSystemMQMessageDto.setTitle(reminderDto.getEmailTitle());

                if (studentOfferItemStaffEmailDto.getUserEmail().contains("@qq.com") || studentOfferItemStaffEmailDto.getUserEmail().contains("@ht-international.net")) {
                    // 邮件相关配置
                    String host = "smtp.exmail.qq.com";
                    int port = 465;
                    String protocol = "smtps";
                    String username = studentOfferItemStaffEmailDto.getUserEmail();
                    String password = studentOfferItemStaffEmailDto.getPassword();
                    try {
                        password = AESUtils.Decrypt(password, AESConstant.AESKEY);
                    } catch (Exception e) {
                        log.error("邮箱密码解析失败！studentOfferItemId::id=" + reminderDto.getFkTableId());
                        sendEmailErrorFromAdmin( contactPersonEmailStaff, toAgentEmail);
                        //发送失败，使用系统默认邮箱再发送一次
                        //sendMessageUtils.sendMail(remindTask.getTaskTitle(), emailTemplate, toAgentEmail, ccAgentEmailList.toArray(new String[0]));
                        //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                        remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                        log.info("系统默认邮箱发送邮件成功----------------队列id：" + reminderDto.getId());
                        throw  new GetServiceException("邮箱密码解析失败！studentOfferItemId::id=" + reminderDto.getFkTableId());
                    }

                    // 邮件内容
                    String to = toAgentEmail;
                    try {
                        // 发送邮件
                        EmailCustomMQMessageDto customMQMessageDto = new EmailCustomMQMessageDto();
                        customMQMessageDto.setEmailSenderQueueId(queueId);
                        customMQMessageDto.setDefaultEncoding("utf-8");
                        customMQMessageDto.setHost(host);
                        customMQMessageDto.setPort(port);
                        customMQMessageDto.setProtocol(protocol);
                        customMQMessageDto.setUserName(username);
                        customMQMessageDto.setPassword(password);
                        customMQMessageDto.setTitle(reminderDto.getEmailTitle());
                        // 按照 "; 后面跟任意数量的空白字符" 进行分割
                        //String[] toEmail = toAgentEmail.split("\\s*;\\s*");
                        String[] toEmail = to.split("\\s*;\\s*");
                        customMQMessageDto.setToEmail(toEmail);
                        if (GeneralTool.isNotEmpty(ccAgentEmailList)) {
                            customMQMessageDto.setCcEmail(ccAgentEmailList.toArray(new String[0]));
                        }
                        customMQMessageDto.setContent(emailTemplate);
                        customMQMessageDto.setIsHtml(true);
                        //rocKetMqCenterClient.specifiedPersonSendEmail(customMQMessageDto);
                        remindTaskQueueService.sendCustomMail(customMQMessageDto);
                        log.info("发送邮件成功(项目成员发送)----------------队列id：" + reminderDto.getId()+ "发送至邮箱：" + to);
                    } catch (Exception e) {
                        //记录
                        log.info("【项目成员名义发送邮件失败】"+e.getMessage());
                        sendEmailErrorFromAdmin(contactPersonEmailStaff,toAgentEmail);
                        //发送失败，使用系统默认邮箱再发送一次
                        //sendMessageUtils.sendMail(remindTask.getTaskTitle(), emailTemplate, toAgentEmail, ccAgentEmailList.toArray(new String[0]));
                        //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                        remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                        log.info("系统默认邮箱重发送邮件成功----------------队列id：" + reminderDto.getId()+"发送至邮箱：" + to);
                        log.info("项目成员名义发送邮件失败提示信息1----------------队列id：" + reminderDto.getId());
                        throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_send_email_on_behalf_of_project_member")+e.getMessage());
                    }
                }
            } else {
                for (StudentOfferItemSendEmailVo.ContactPersonEmailDto contactPersonEmailDto : contactPersonEmailStaff.getContactPersonEmailDtos()) {
                    emailSystemMQMessageDto.setEmailSenderQueueId(reminderDto.getId());
                    emailSystemMQMessageDto.setContent(emailTemplate);
                    emailSystemMQMessageDto.setToEmail(contactPersonEmailDto.getAgentEmail());
                    emailSystemMQMessageDto.setTitle(reminderDto.getEmailTitle());
                    log.info("方案联系人邮箱:" + contactPersonEmailDto.getAgentEmail());
                    //sendMessageUtils.sendMail(reminderDto.getEmailTitle(), emailTemplate, contactPersonEmailDto.getAgentEmail());
                    //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    log.info("发送邮件成功----------------队列id：" + reminderDto.getId()+"发送至邮箱：" + contactPersonEmailDto.getAgentEmail());
                }
            }
        } else {
            log.info("itemId:" + reminderDto.getFkTableId() + "代理：" + contactPersonEmailStaff.getFkAgentName() + "拒收邮箱");
        }
    }


    /**
     * 发给项目成员和创建人
     * @param staffVo
     * @param reminderDto
     * @param emailSystemMQMessageDto
     */
    private void sendOfferItemRemind(StaffVo staffVo,AcceptOfferDeadlineReminderDto reminderDto,EmailSystemMQMessageDto emailSystemMQMessageDto ) {
        //是否禁止发送邮件 true 发送  false禁止发送
        if (!emailSwitch) {
            return;
        }
        //true 项目成员名义发送   false  系统默认邮箱发送
        Boolean isSenderAdmin = true;

        //查询发件人邮箱
        StudentOfferItemSendEmailVo contactPersonEmailStaffDto = saleCenterClient.getContactPersonEmailStaff(reminderDto.getFkTableId());

        //步骤不符则不发送邮件
        //支付押金截止提醒：提交完成（Submitted），已通知缴申请费（Notified Payment），院校已收件（App Received），已录取（Admitted/Offer）,已延期（Postponed）,申请延期（App For Extension）
        //接受Offer截止提醒：提交完成（Submitted），已通知缴申请费（Notified Payment），院校已收件（App Received），已录取（Admitted/Offer）,已延期（Postponed）,申请延期（App For Extension）
        if ((reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND.getEmailTemplateKey()))
                && (!contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(2L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(11L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(3L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(4L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(13L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(14L)
//                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(5L)

        )) {
            return;
        } else if ((reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.PAY_DEPOSIT_DUE_REMIND.getEmailTemplateKey()))
                && (!contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(2L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(11L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(3L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(4L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(13L)
                && !contactPersonEmailStaffDto.getFkStudentOfferItemStepId().equals(14L)
        )) {
            return;
        }
        //步骤不符则不发送邮件
        //达到过Offer Accepted则不发
        if (contactPersonEmailStaffDto.getIsStepOfferAccepted() || contactPersonEmailStaffDto.getIsStudentArrivedOs()) {
            return;
        }

        //检验员工邮箱信息
        if (GeneralTool.isEmpty(contactPersonEmailStaffDto)) {
            //密码为空，需要填写密码
            StudentOfferItemSendEmailVo.StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto = contactPersonEmailStaffDto.getStudentOfferItemStaffEmailDto();
            if (GeneralTool.isEmpty(studentOfferItemStaffEmailDto) || GeneralTool.isEmpty(studentOfferItemStaffEmailDto.getUserEmail()) || GeneralTool.isEmpty(studentOfferItemStaffEmailDto.getPassword())) {
                log.info("密码为空，需要填写密码,员工" + staffVo.getName());
                isSenderAdmin = true;
            }
        }
        //如果isSenderAdmin=true,系统发送
        if (isSenderAdmin) {
            //sendMessageUtils.sendMail(taskTitle, emailTemplate, staffVo.getEmail());
            //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
            remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
            log.info("发送内部邮件(系统默认发送)----------------任务id：" + reminderDto.getId());
        } else {
            try {
                StudentOfferItemSendEmailVo.StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto = contactPersonEmailStaffDto.getStudentOfferItemStaffEmailDto();
                String password = AESUtils.Decrypt(studentOfferItemStaffEmailDto.getPassword(), AESConstant.AESKEY);
                if (studentOfferItemStaffEmailDto.getUserEmail().contains("@qq.com") || studentOfferItemStaffEmailDto.getUserEmail().contains("@ht-international.net")) {
                    // 邮件相关配置
                    String host = "smtp.exmail.qq.com";
                    int port = 465;
                    String protocol = "smtps";
                    String username = studentOfferItemStaffEmailDto.getUserEmail();

                    try {

                        // 发送邮件
                        EmailCustomMQMessageDto customMQMessageDto = new EmailCustomMQMessageDto();
                        customMQMessageDto.setDefaultEncoding("utf-8");
                        customMQMessageDto.setHost(host);
                        customMQMessageDto.setPort(port);
                        customMQMessageDto.setProtocol(protocol);
                        customMQMessageDto.setUserName(username);
                        customMQMessageDto.setPassword(password);
                        customMQMessageDto.setTitle(reminderDto.getEmailTitle());
                        String[] toEmail = new String[1];
                        toEmail[0] = staffVo.getEmail();
                        customMQMessageDto.setToEmail(toEmail);
                        List<String> ccAgentEmailList = new ArrayList<>();
                        ccAgentEmailList.add(staffVo.getEmail());
                        if (GeneralTool.isNotEmpty(ccAgentEmailList)) {
                            customMQMessageDto.setCcEmail(ccAgentEmailList.toArray(new String[0]));
                        }
                        customMQMessageDto.setIsHtml(true);
                        customMQMessageDto.setContent(emailSystemMQMessageDto.getContent());
                        //rocKetMqCenterClient.specifiedPersonSendEmail(customMQMessageDto);
                        remindTaskQueueService.sendCustomMail(customMQMessageDto);
                        log.info("发送邮件成功(项目成员内部发送)----------------队列id：" + reminderDto.getId()+ "发送至邮箱：" + staffVo.getEmail());
                    } catch (Exception e) {
                        log.info("发送邮件失败(项目成员内部发送)----------------任务id：" + reminderDto.getId());
                        isSenderAdmin = true;
                    }
                }
            } catch (Exception e) {
                isSenderAdmin = true;
            } finally {
                if (isSenderAdmin) {
                    //sendMessageUtils.sendMail(taskTitle, emailTemplate, staffVo.getEmail());
                    //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    log.info("发送内部邮件(系统默认发送)----------------任务id：" + reminderDto.getId() + "发送至邮箱：" + staffVo.getEmail());

                }
            }
        }

    }


    /**
     * 设置邮件模板
     * @param reminderDto
     * @return
     */
    private String setEmailTemplate(AcceptOfferDeadlineReminderDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND.getEmailTemplateKey()));
        } else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.PAY_DEPOSIT_DUE_REMIND.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.PAY_DEPOSIT_DUE_REMIND.getEmailTemplateKey()));
        }else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.getEmailTemplateKey()));
        }else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.getEmailTemplateKey()));
        } else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.COURSE_OPENING_REMINDER.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.COURSE_OPENING_REMINDER.getEmailTemplateKey()));
        }
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate =null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        }else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        //emailTemplate = doReplaceTemplate(reminderDto.getTemplate(), emailTemplate,reminderDto.getFkEmailTypeKey());
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");


        if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE.getEmailTemplateKey())||reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.PAY_DEPOSIT_DUE_REMIND.getEmailTemplateKey())) {
            if (!reminderDto.getLanguageCode().equals("en")) {
                emailTemplate = emailTemplate.replace("#{endTime}", "支付押金截止时间：" + sdf.format(reminderDto.getDepositDeadline()));
            }else {
                emailTemplate = emailTemplate.replace("#{endTime}", "Deposit Deadline：" + sdf.format(reminderDto.getAcceptOfferDeadline()));
            }
        } else if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE.getEmailTemplateKey())||reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.OFFER_ACCEPT_DUE_REMIND.getEmailTemplateKey())) {
            if (!reminderDto.getLanguageCode().equals("en")) {
                emailTemplate = emailTemplate.replace("#{endTime}", "接受Offer截止时间：" + sdf.format(reminderDto.getAcceptOfferDeadline()));
            } else {
                emailTemplate = emailTemplate.replace("#{endTime}", "Offer Acceptance Deadline：" + sdf.format(reminderDto.getAcceptOfferDeadline()));
            }
        }
        if (GeneralTool.isNotEmpty(reminderDto.getTemplate())) {
            emailTemplate = emailTemplate.replace("#{template}", reminderDto.getTemplate());
        } else {
            emailTemplate = emailTemplate.replace("#{template}", "");
        }

        //获取当前日期
        String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        List<Long> ids = new ArrayList<>();
        ids.add(reminderDto.getFkTableId());
        List<StudentOfferItemVo> offerItemDtos = saleCenterClient.getStudentByOfferItemIds(ids);

        //如果是最后一次发送提醒，需要添加字符【今日截止】
        if (currentDate.equals(new SimpleDateFormat("yyyy-MM-dd").format(reminderDto.getOperationTime()))) {
            if(reminderDto.getLanguageCode().equals("en")){
                if(reminderDto.getOperationTime().equals(reminderDto.getDepositDeadline())||reminderDto.getOperationTime().equals(reminderDto.getAcceptOfferDeadline())){
                    emailTemplate = emailTemplate.replace("#{taskTitle}", "[Due Today]" + reminderDto.getEmailTitle());
                    emailTemplate = emailTemplate.replace("${dearName}", offerItemDtos.get(0).getFkStudentName());
                }else {
                    emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
                }

            }else {
                if(reminderDto.getOperationTime().equals(reminderDto.getDepositDeadline())||reminderDto.getOperationTime().equals(reminderDto.getAcceptOfferDeadline())){
                    emailTemplate = emailTemplate.replace("#{taskTitle}", "【今日截止】" + reminderDto.getEmailTitle());
                    //设置名字
                    if (GeneralTool.isNotEmpty(offerItemDtos) && GeneralTool.isNotEmpty(offerItemDtos.get(0).getFkStudentName())) {
                        emailTemplate = emailTemplate.replace("${dearName}", offerItemDtos.get(0).getFkStudentName());
                    } else {
                        emailTemplate = emailTemplate.replace("${dearName}", "");
                    }
                }else {
                    emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
                }

            }
        } else {
            emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        }
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if(GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId())&&remindTemplates.get(0).getFkParentEmailTemplateId()!=0){
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate",emailTemplate);
//            reminderDto.getLanguageCode().equals("en")
            if ("en".equals(reminderDto.getLanguageCode())) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            }else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }




    /**
     * 错误邮件通知
     * @param contactPersonEmailStaff
     * @param email
     */
    private void sendEmailErrorFromAdmin(StudentOfferItemSendEmailVo contactPersonEmailStaff, String email) {
        StudentOfferItemSendEmailVo.StudentOfferItemStaffEmailDto studentOfferItemStaffEmailDto = contactPersonEmailStaff.getStudentOfferItemStaffEmailDto();
        StringBuilder sb = new StringBuilder();
        sb.append("项目成员：").append(studentOfferItemStaffEmailDto.getStaffName());
        if (GeneralTool.isNotEmpty(studentOfferItemStaffEmailDto.getUserEmail())) {
            sb.append("项目成员邮箱：").append(studentOfferItemStaffEmailDto.getUserEmail());
        }
        if (GeneralTool.isNotEmpty(email)) {
            sb.append("代理联系人邮箱：").append(email);
        } else {
            List<StudentOfferItemSendEmailVo.ContactPersonEmailDto> contactPersonEmailDtos = contactPersonEmailStaff.getContactPersonEmailDtos();
            for (StudentOfferItemSendEmailVo.ContactPersonEmailDto contactPersonEmailDto : contactPersonEmailDtos) {
                sb.append("代理联系人邮箱：").append(contactPersonEmailDto.getAgentEmail());
            }
        }

    }

}

<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel target="8" />
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ais-exam-center" options="-parameters" />
      <module name="ais-exam-center-ap" options="-parameters" />
      <module name="ais-finance-center" options="-parameters" />
      <module name="ais-finance-center-ap" options="-parameters" />
      <module name="ais-institution-center" options="-parameters" />
      <module name="ais-institution-center-ap" options="-parameters" />
      <module name="ais-mail-ap" options="-parameters" />
      <module name="ais-mail-center" options="-parameters" />
      <module name="ais-mail-center-ap" options="-parameters" />
      <module name="ais-mps-center" options="-parameters" />
      <module name="ais-mps-center-ap" options="-parameters" />
      <module name="ais-office-center" options="-parameters" />
      <module name="ais-office-center-ap" options="-parameters" />
      <module name="ais-partner-center" options="-parameters" />
      <module name="ais-partner-center-ap" options="-parameters" />
      <module name="ais-platform-center" options="-parameters" />
      <module name="ais-platform-center-ap" options="-parameters" />
      <module name="ais-registration-center" options="-parameters" />
      <module name="ais-registration-center-ap" options="-parameters" />
      <module name="ais-reminder-center" options="-parameters" />
      <module name="ais-reminder-center-ap" options="-parameters" />
      <module name="ais-report-center" options="-parameters" />
      <module name="ais-report-center-ap" options="-parameters" />
      <module name="ais-resume-center" options="-parameters" />
      <module name="ais-resume-center-ap" options="-parameters" />
      <module name="ais-sale-center" options="-parameters" />
      <module name="ais-sale-center-ap" options="-parameters" />
      <module name="ais-voting-center" options="-parameters" />
      <module name="ais-voting-center-ap" options="-parameters" />
      <module name="authentication" options="-parameters" />
      <module name="biz-service" options="-parameters" />
      <module name="biz-service-ap" options="-parameters" />
      <module name="common" options="-parameters" />
      <module name="file-center" options="-parameters" />
      <module name="file-center-ap" options="-parameters" />
      <module name="file-option-ap" options="-parameters" />
      <module name="gateway" options="-parameters" />
      <module name="help-center" options="-parameters" />
      <module name="help-center-ap" options="-parameters" />
      <module name="hti-java-ais" options="-parameters" />
      <module name="i18n-center-ap" options="-parameters" />
      <module name="mybatisplus-generator" options="-parameters" />
      <module name="permission-center" options="-parameters" />
      <module name="permission-center-ap" options="-parameters" />
      <module name="platform-config-center" options="-parameters" />
      <module name="platform-config-center-ap" options="-parameters" />
      <module name="seata-demo" options="-parameters" />
      <module name="sys-boot-admin" options="-parameters" />
      <module name="sys-log" options="-parameters" />
      <module name="sys-service" options="-parameters" />
      <module name="sys-service-ap" options="-parameters" />
      <module name="sys-swagger" options="-parameters" />
      <module name="websocket" options="-parameters" />
      <module name="workflow-center" options="-parameters" />
      <module name="workflow-center-ap" options="-parameters" />
      <module name="xxljob" options="-parameters" />
      <module name="xxljob-admin" options="-parameters" />
    </option>
  </component>
</project>
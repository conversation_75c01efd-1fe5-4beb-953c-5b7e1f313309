package com.get.aisplatformcenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.aisplatformcenter.dao.registration.UserInfoMapper;
import com.get.aisplatformcenter.dao.appmso.UserTypeMapper;
import com.get.aisplatformcenter.service.UserInfoService;
import com.get.aisplatformcenterap.dto.UserInfoDto;
import com.get.aisplatformcenterap.entity.UserInfoEntity;
import com.get.aisplatformcenterap.entity.UserTypeEntity;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.feign.IExamCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Jerry.
 * Time: 17:35
 * Date: 2021/7/8
 * Description:用户信息管理实现类
 */
@Service
public class UserInfoServiceImpl implements UserInfoService {

    @Resource
    private IPermissionCenterClient feignPermissionService;

    @Resource
    private IExamCenterClient feignExamService;


    @Resource
    private IInstitutionCenterClient feignInstitutionService;

    @Resource
    private UserInfoMapper userInfoMapper;

    @Resource
    private UserTypeMapper userTypeMapper;

    /**
     * @Description: 根据名称模糊搜索用户ids
     * @Author: Jerry
     * @Date:14:47 2021/8/23
     */
    @Override
    public Set<Long> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName) {
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria();
//        if(GeneralTool.isNotEmpty(userName)){
//            criteria.andLike("wechatNickname","%"+userName+"%");
//        }
//        if(GeneralTool.isNotEmpty(fkAreaCityId)){
//            criteria.andEqualTo("fkAreaCityId",fkAreaCityId);
//        }
//        if(GeneralTool.isNotEmpty(bdName)){
//            //根据bd名称获取员工ids
//            List<Long> staffIdsByNameKey = feignPermissionService.getStaffIdsByNameKey(bdName);
//            if(GeneralTool.isNotEmpty(staffIdsByNameKey)){
//                Set<Long> fkStaffIds = new HashSet<>(staffIdsByNameKey);
//                //根据员工ids（BD）获取用户ids
//                Set<Long> userIdsByStaffIds = feignExamService.getUserIdsByStaffIds(fkStaffIds);
//                if(GeneralTool.isEmpty(userIdsByStaffIds)){
//                    return null;
//                }
//                criteria.andIn("id",userIdsByStaffIds);
//            }else{
//                //查询不出用户
//                return null;
//            }
//        }
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        LambdaQueryWrapper<UserInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(userName)) {
            lambdaQueryWrapper.like(UserInfoEntity::getWechatNickname, userName);
        }
        if (GeneralTool.isNotEmpty(fkAreaCityId)) {
            lambdaQueryWrapper.eq(UserInfoEntity::getFkAreaCityId, fkAreaCityId);
        }
        if (GeneralTool.isNotEmpty(bdName)) {
            //根据bd名称获取员工ids 111111
            Result<List<Long>> resultstaffIdsByNameKey = feignPermissionService.getStaffIdsByNameKeyOrEnNameKey(bdName);
            if (!resultstaffIdsByNameKey.isSuccess()) {
                throw new GetServiceException(resultstaffIdsByNameKey.getMessage());
            }
            List<Long> staffIdsByNameKey = resultstaffIdsByNameKey.getData();
            if (GeneralTool.isNotEmpty(staffIdsByNameKey)) {
                Set<Long> fkStaffIds = new HashSet<>(staffIdsByNameKey);
                Set<Long> userIdsByStaffIds = new HashSet<>();
                //根据员工ids（BD）获取用户ids
                Result<Set<Long>> resultuserIdsByStaffIds = feignExamService.getUserIdsByStaffIds(fkStaffIds);
                if (resultuserIdsByStaffIds.isSuccess()) {
                    userIdsByStaffIds = resultuserIdsByStaffIds.getData();
                    if (GeneralTool.isEmpty(userIdsByStaffIds)) {
                        return null;
                    }
                    lambdaQueryWrapper.in(UserInfoEntity::getId, userIdsByStaffIds);
                }

            } else {
                //查询不出用户
                return null;
            }
        }
        List<UserInfoEntity> userInfos = userInfoMapper.selectList(lambdaQueryWrapper);

        if (GeneralTool.isEmpty(userInfos)) {
            return null;
        }
        Set<Long> userIds = userInfos.stream().map(UserInfoEntity::getId).collect(Collectors.toSet());
        return userIds;
    }


    /**
     * @Description: feign调用 根据userid获取名称（微信昵称）
     * @Author: Jerry
     * @Date:14:23 2021/8/23
     */
    @Override
    public Map<Long, String> getUserNickNamesByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        List<UserInfoEntity> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        for (UserInfoEntity userInfo : userInfos) {
            map.put(userInfo.getId(), userInfo.getWechatNickname());
        }
        return map;
    }

    /**
     * @Description: 根据ids获取人员对应的城市名称
     * @Author: Jerry
     * @Date:10:39 2021/8/30
     */
    @Override
    public Map<Long, String> getCityNamesByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        List<UserInfoEntity> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        //获取所有的城市ids
        Set<Long> fkAreaCityIds = userInfos.stream().map(UserInfoEntity::getFkAreaCityId).collect(Collectors.toSet());
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAreaCityIds)) {
            //通过城市ids 查找对应的城市名称map 111111
            Result<Map<Long, String>> resultcityNamesByIds = feignInstitutionService.getCityNameChnsByIds(fkAreaCityIds);
            if (resultcityNamesByIds.isSuccess()) {
                cityNamesByIds = resultcityNamesByIds.getData();
            }
        }
        for (UserInfoEntity userInfo : userInfos) {
            map.put(userInfo.getId(), cityNamesByIds.get(userInfo.getFkAreaCityId()));
        }
        return map;
    }

    /**
     * @Description: feign调用 根据userid获取手机号
     * @Author: Jerry
     * @Date:10:30 2021/10/15
     */
    @Override
    public Map<Long, String> getMobileByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);

        List<UserInfoEntity> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        for (UserInfoEntity userInfo : userInfos) {
            if (GeneralTool.isNotEmpty(userInfo.getMobile())) {
                map.put(userInfo.getId(), userInfo.getMobile());
            }
        }
        return map;
    }

    /**
     * @Description: 根据名称模糊或者手机号搜索用户ids
     * @Author: Jerry
     * @Date:11:14 2021/8/27
     */
    @Override
    public Set<Long> getUserIdsByNameOrMobile(String userName, String phoneNumber) {
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria();
//        if(GeneralTool.isNotEmpty(userName)){
//            criteria.andLike("wechatNickname","%"+userName+"%");
//        }
//        if(GeneralTool.isNotEmpty(phoneNumber)){
//            criteria.andEqualTo("mobile",phoneNumber);
//        }
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        LambdaQueryWrapper<UserInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(userName)) {
            lambdaQueryWrapper.like(UserInfoEntity::getWechatNickname, userName);
        }
        if (GeneralTool.isNotEmpty(phoneNumber)) {
            lambdaQueryWrapper.eq(UserInfoEntity::getMobile, phoneNumber);
        }
        List<UserInfoEntity> userInfos = userInfoMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(userInfos)) {
            return null;
        }
        Set<Long> userIds = userInfos.stream().map(UserInfoEntity::getId).collect(Collectors.toSet());
        return userIds;
    }


    /**
     * @Description: feign调用 根据userid获取对象
     * @Author: Jerry
     * @Date:17:04 2021/8/26
     */
    @Override
    public Map<Long, UserInfoVo> getUserInfoDtoByIds(Set<Long> userIds) {
        Map<Long, UserInfoVo> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        List<UserInfoEntity> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        for (UserInfoEntity userInfo : userInfos) {
            UserInfoVo userInfoVo = BeanCopyUtils.objClone(userInfo, UserInfoVo::new);
            map.put(userInfo.getId(), userInfoVo);
        }
        return map;
    }

    /**
     * 用户信息管理列表数据
     *
     * @param userInfoDto
     * @param page
     * @return
     */
    @Override
    public List<UserInfoVo> getUserInfoList(UserInfoDto userInfoDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria();
//        if(GeneralTool.isNotEmpty(userInfoDto.getFkPlatformType())){
//            criteria.andEqualTo("fkPlatformType",userInfoDto.getFkPlatformType());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getNickname())){
//            criteria.andLike("nickname","%"+userInfoDto.getNickname()+"%");
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFamilyNamePy())){
//            criteria.andLike("familyNamePy","%"+userInfoDto.getFamilyNamePy()+"%");
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFirstNamePy())){
//            criteria.andLike("firstNamePy","%"+userInfoDto.getFirstNamePy()+"%");
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getMobile())){
//            criteria.andEqualTo("mobile",userInfoDto.getMobile());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getGender())){
//            criteria.andEqualTo("gender",userInfoDto.getGender());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getIdentityCard())){
//            criteria.andEqualTo("identityCard",userInfoDto.getIdentityCard());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getEmail())){
//            criteria.andEqualTo("email",userInfoDto.getEmail());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFkAreaCountryId())){
//            criteria.andEqualTo("fkAreaCountryId",userInfoDto.getFkAreaCountryId());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFkAreaStateId())){
//            criteria.andEqualTo("fkAreaStateId",userInfoDto.getFkAreaStateId());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFkAreaCityId())){
//            criteria.andEqualTo("fkAreaCityId",userInfoDto.getFkAreaCityId());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getMobileAreaCode())){
//            criteria.andEqualTo("mobileAreaCode",userInfoDto.getMobileAreaCode());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getCreateBeginDate())){
//            criteria.andGreaterThanOrEqualTo("gmtCreate", userInfoDto.getCreateBeginDate());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getCreateEndDate())){
//            criteria.andLessThanOrEqualTo("gmtCreate", userInfoDto.getCreateEndDate());
//        }
//        example.setOrderByClause("gmt_create desc");
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
//        page.restPage(userInfos);

//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<UserInfoEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(userInfoDto.getFkPlatformType())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getFkPlatformType, userInfoDto.getFkPlatformType());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getNickname())) {
            lambdaQueryWrapper.like(UserInfoEntity::getNickname, userInfoDto.getNickname());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFamilyNamePy())) {
            lambdaQueryWrapper.like(UserInfoEntity::getFamilyNamePy, userInfoDto.getFamilyNamePy());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFirstNamePy())) {
            lambdaQueryWrapper.like(UserInfoEntity::getFirstNamePy, userInfoDto.getFirstNamePy());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getMobile())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getMobile, userInfoDto.getMobile());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getGender())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getGender, userInfoDto.getGender());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getIdentityCard())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getIdentityCard, userInfoDto.getIdentityCard());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getEmail())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getEmail, userInfoDto.getEmail());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFkAreaCountryId())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getFkAreaCountryId, userInfoDto.getFkAreaCountryId());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFkAreaStateId())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getFkAreaStateId, userInfoDto.getFkAreaStateId());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFkAreaCityId())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getFkAreaCityId, userInfoDto.getFkAreaCityId());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getMobileAreaCode())) {
            lambdaQueryWrapper.eq(UserInfoEntity::getMobileAreaCode, userInfoDto.getMobileAreaCode());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getCreateBeginDate())) {
            lambdaQueryWrapper.ge(UserInfoEntity::getGmtCreate, userInfoDto.getCreateBeginDate());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getCreateEndDate())) {
            lambdaQueryWrapper.le(UserInfoEntity::getGmtCreate, userInfoDto.getCreateEndDate());
        }
        lambdaQueryWrapper.orderByDesc(UserInfoEntity::getGmtCreate);
        IPage<UserInfoEntity> pages = userInfoMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<UserInfoEntity> userInfos = pages.getRecords();
        page.setAll((int) pages.getTotal());

        Set<Long> userTypeIds = new HashSet<>();
        Set<Long> ids = userInfos.stream().map(UserInfoEntity::getId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(ids)){
            List<UserTypeEntity> userTypes = userTypeMapper.selectList(Wrappers.<UserTypeEntity>lambdaQuery().eq(UserTypeEntity::getType, 1).in(UserTypeEntity::getFkUserId, ids));
            if (GeneralTool.isNotEmpty(userTypes)){
                userTypeIds = userTypes.stream().map(UserTypeEntity::getFkUserId).collect(Collectors.toSet());
            }
        }
        List<UserInfoVo> userInfoVos = new ArrayList<>();
        for (UserInfoEntity userInfo : userInfos) {
            UserInfoVo userInfoVo = BeanCopyUtils.objClone(userInfo, UserInfoVo::new);
            if ("0".equals(userInfo.getGender())) {
                userInfoVo.setGender("女");
            } else if ("1".equals(userInfo.getGender())) {
                userInfoVo.setGender("男");
            }
            if (userTypeIds.contains(userInfo.getId())){
                userInfoVo.setIsVipUser(true);
            }
//            userInfoVo.setFkPlatformTypeName(ProjectKeyEnum.getValue(userInfo.getFkPlatformType()));
            userInfoVo.setFkPlatformTypeName(ProjectKeyEnum.getInitialValue(userInfo.getFkPlatformType()));
            userInfoVos.add(userInfoVo);
        }
        return userInfoVos;
    }
}
